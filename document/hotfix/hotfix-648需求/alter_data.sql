CREATE TABLE `admin_statement_builder_title_config`
(
    `id`     int(10) unsigned NOT NULL AUTO_INCREMENT,
    `code`   int(10)          NOT NULL DEFAULT '0' COMMENT '代号',
    `title`  varchar(128)     NOT NULL DEFAULT '' COMMENT '标题',
    `status` tinyint(2)       NOT NULL DEFAULT '1' COMMENT '状态 1正常 2 隐藏',
    `tabs`   tinyint(2)       NOT NULL DEFAULT '1' COMMENT '分类(即tabs:1人才 2单位 3职位 4公告 5投递)',
    `type`   tinyint(2)       NOT NULL DEFAULT '1' COMMENT '类型：1明细 2统计',
    `remark` varchar(255)     NOT NULL DEFAULT '' COMMENT '备注',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_code` (`code`) USING BTREE,
    KEY `idx_type` (`type`) USING BTREE,
    KEY `idx_tabs` (`tabs`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='报表生成器标题(表头)配置表';



INSERT INTO `admin_statement_builder_title_config` (`id`, `code`, `title`, `status`, `tabs`, `type`, `remark`)
VALUES (1, 510001, '单位ID', 1, 5, 1, '');
INSERT INTO `admin_statement_builder_title_config` (`id`, `code`, `title`, `status`, `tabs`, `type`, `remark`)
VALUES (2, 510002, '单位名称', 1, 5, 1, '');
INSERT INTO `admin_statement_builder_title_config` (`id`, `code`, `title`, `status`, `tabs`, `type`, `remark`)
VALUES (3, 510003, '职位ID', 1, 5, 1, '');
INSERT INTO `admin_statement_builder_title_config` (`id`, `code`, `title`, `status`, `tabs`, `type`, `remark`)
VALUES (4, 510004, '职位名称', 1, 5, 1, '');
INSERT INTO `admin_statement_builder_title_config` (`id`, `code`, `title`, `status`, `tabs`, `type`, `remark`)
VALUES (5, 510005, '公告ID', 1, 5, 1, '');
INSERT INTO `admin_statement_builder_title_config` (`id`, `code`, `title`, `status`, `tabs`, `type`, `remark`)
VALUES (6, 510006, '公告名称', 1, 5, 1, '');
INSERT INTO `admin_statement_builder_title_config` (`id`, `code`, `title`, `status`, `tabs`, `type`, `remark`)
VALUES (7, 510007, '人才ID', 1, 5, 1, '');
INSERT INTO `admin_statement_builder_title_config` (`id`, `code`, `title`, `status`, `tabs`, `type`, `remark`)
VALUES (8, 510008, '人才姓名', 1, 5, 1, '');
INSERT INTO `admin_statement_builder_title_config` (`id`, `code`, `title`, `status`, `tabs`, `type`, `remark`)
VALUES (9, 510009, '户籍/国籍', 1, 5, 1, '');
INSERT INTO `admin_statement_builder_title_config` (`id`, `code`, `title`, `status`, `tabs`, `type`, `remark`)
VALUES (10, 510010, '性别', 1, 5, 1, '');
INSERT INTO `admin_statement_builder_title_config` (`id`, `code`, `title`, `status`, `tabs`, `type`, `remark`)
VALUES (11, 510011, '职称', 1, 5, 1, '');
INSERT INTO `admin_statement_builder_title_config` (`id`, `code`, `title`, `status`, `tabs`, `type`, `remark`)
VALUES (12, 510012, '出生年月', 1, 5, 1, '');
INSERT INTO `admin_statement_builder_title_config` (`id`, `code`, `title`, `status`, `tabs`, `type`, `remark`)
VALUES (13, 510013, '身份类型', 1, 5, 1, '');
INSERT INTO `admin_statement_builder_title_config` (`id`, `code`, `title`, `status`, `tabs`, `type`, `remark`)
VALUES (14, 510014, '是否博士后经历', 1, 5, 1, '');
INSERT INTO `admin_statement_builder_title_config` (`id`, `code`, `title`, `status`, `tabs`, `type`, `remark`)
VALUES (15, 510015, '最晚毕业的博士的院校', 1, 5, 1, '');
INSERT INTO `admin_statement_builder_title_config` (`id`, `code`, `title`, `status`, `tabs`, `type`, `remark`)
VALUES (16, 510016, '最晚毕业的硕士的院校', 1, 5, 1, '');
INSERT INTO `admin_statement_builder_title_config` (`id`, `code`, `title`, `status`, `tabs`, `type`, `remark`)
VALUES (17, 510017, '最晚毕业的本科的院校', 1, 5, 1, '');
INSERT INTO `admin_statement_builder_title_config` (`id`, `code`, `title`, `status`, `tabs`, `type`, `remark`)
VALUES (18, 510018, '最高学历', 1, 5, 1, '');
INSERT INTO `admin_statement_builder_title_config` (`id`, `code`, `title`, `status`, `tabs`, `type`, `remark`)
VALUES (19, 510019, '最高教育经历一级专业', 1, 5, 1, '显示最高教育经历一级专业');
INSERT INTO `admin_statement_builder_title_config` (`id`, `code`, `title`, `status`, `tabs`, `type`, `remark`)
VALUES (20, 510020, '最高教育经历二级专业', 1, 5, 1, '显示最高教育经历二级专业');
INSERT INTO `admin_statement_builder_title_config` (`id`, `code`, `title`, `status`, `tabs`, `type`, `remark`)
VALUES (21, 510021, '最高教育经历三级专业', 1, 5, 1, '显示最高教育经历三级专业');
INSERT INTO `admin_statement_builder_title_config` (`id`, `code`, `title`, `status`, `tabs`, `type`, `remark`)
VALUES (22, 510022, '最高教育经历专业', 1, 5, 1, '显示最高教育经历一二三级专业');
INSERT INTO `admin_statement_builder_title_config` (`id`, `code`, `title`, `status`, `tabs`, `type`, `remark`)
VALUES (23, 510023, '研究方向', 1, 5, 1, '');
INSERT INTO `admin_statement_builder_title_config` (`id`, `code`, `title`, `status`, `tabs`, `type`, `remark`)
VALUES (24, 510024, '博士毕业时间（年月）', 1, 5, 1, '');
INSERT INTO `admin_statement_builder_title_config` (`id`, `code`, `title`, `status`, `tabs`, `type`, `remark`)
VALUES (25, 510025, '是否海外经历', 1, 5, 1, '');
INSERT INTO `admin_statement_builder_title_config` (`id`, `code`, `title`, `status`, `tabs`, `type`, `remark`)
VALUES (26, 510026, '最高教育经历是否985/211', 1, 5, 1, '');
INSERT INTO `admin_statement_builder_title_config` (`id`, `code`, `title`, `status`, `tabs`, `type`, `remark`)
VALUES (27, 110007, '人才ID', 1, 1, 1, '');
INSERT INTO `admin_statement_builder_title_config` (`id`, `code`, `title`, `status`, `tabs`, `type`, `remark`)
VALUES (28, 110008, '人才姓名', 1, 1, 1, '');
INSERT INTO `admin_statement_builder_title_config` (`id`, `code`, `title`, `status`, `tabs`, `type`, `remark`)
VALUES (29, 210001, '单位ID', 1, 2, 1, '');
INSERT INTO `admin_statement_builder_title_config` (`id`, `code`, `title`, `status`, `tabs`, `type`, `remark`)
VALUES (30, 210002, '单位名称', 1, 2, 1, '');
INSERT INTO `admin_statement_builder_title_config` (`id`, `code`, `title`, `status`, `tabs`, `type`, `remark`)
VALUES (31, 310001, '职位ID', 1, 3, 1, '');
INSERT INTO `admin_statement_builder_title_config` (`id`, `code`, `title`, `status`, `tabs`, `type`, `remark`)
VALUES (32, 310002, '职位名称', 1, 3, 1, '');
INSERT INTO `admin_statement_builder_title_config` (`id`, `code`, `title`, `status`, `tabs`, `type`, `remark`)
VALUES (33, 310003, '公告ID', 1, 3, 1, '');
INSERT INTO `admin_statement_builder_title_config` (`id`, `code`, `title`, `status`, `tabs`, `type`, `remark`)
VALUES (34, 310004, '公告名称', 1, 3, 1, '');
INSERT INTO `admin_statement_builder_title_config` (`id`, `code`, `title`, `status`, `tabs`, `type`, `remark`)
VALUES (35, 40005, '公告ID', 1, 4, 1, '');
INSERT INTO `admin_statement_builder_title_config` (`id`, `code`, `title`, `status`, `tabs`, `type`, `remark`)
VALUES (36, 40006, '公告名称', 1, 4, 1, '');
INSERT INTO `admin_statement_builder_title_config` (`id`, `code`, `title`, `status`, `tabs`, `type`, `remark`)
VALUES (37, 510027, '投递时间', 1, 5, 1, '');
INSERT INTO `admin_statement_builder_title_config` (`id`, `code`, `title`, `status`, `tabs`, `type`, `remark`)
VALUES (38, 520007, '投递ID', 1, 5, 2, '');
INSERT INTO `admin_statement_builder_title_config` (`id`, `code`, `title`, `status`, `tabs`, `type`, `remark`)
VALUES (39, 310005, '单位ID', 1, 3, 1, '');
INSERT INTO `admin_statement_builder_title_config` (`id`, `code`, `title`, `status`, `tabs`, `type`, `remark`)
VALUES (41, 310006, '单位名称', 1, 3, 1, '');
INSERT INTO `admin_statement_builder_title_config` (`id`, `code`, `title`, `status`, `tabs`, `type`, `remark`)
VALUES (42, 320001, '职位ID', 1, 3, 2, '');
INSERT INTO `admin_statement_builder_title_config` (`id`, `code`, `title`, `status`, `tabs`, `type`, `remark`)
VALUES (43, 320002, '职位名称', 1, 3, 2, '');
INSERT INTO `admin_statement_builder_title_config` (`id`, `code`, `title`, `status`, `tabs`, `type`, `remark`)
VALUES (44, 320003, '公告ID', 1, 3, 2, '');
INSERT INTO `admin_statement_builder_title_config` (`id`, `code`, `title`, `status`, `tabs`, `type`, `remark`)
VALUES (45, 320004, '公告名称', 1, 3, 2, '');
INSERT INTO `admin_statement_builder_title_config` (`id`, `code`, `title`, `status`, `tabs`, `type`, `remark`)
VALUES (46, 320005, '单位ID', 1, 3, 2, '');
INSERT INTO `admin_statement_builder_title_config` (`id`, `code`, `title`, `status`, `tabs`, `type`, `remark`)
VALUES (47, 320006, '单位名称', 1, 3, 2, '');
INSERT INTO `admin_statement_builder_title_config` (`id`, `code`, `title`, `status`, `tabs`, `type`, `remark`)
VALUES (48, 310007, '所属销售', 1, 3, 1, '');
INSERT INTO `admin_statement_builder_title_config` (`id`, `code`, `title`, `status`, `tabs`, `type`, `remark`)
VALUES (49, 310008, '招聘状态', 1, 3, 1, '');
INSERT INTO `admin_statement_builder_title_config` (`id`, `code`, `title`, `status`, `tabs`, `type`, `remark`)
VALUES (50, 310009, '职位阅读量', 1, 3, 1, '');
INSERT INTO `admin_statement_builder_title_config` (`id`, `code`, `title`, `status`, `tabs`, `type`, `remark`)
VALUES (51, 310010, '职位收藏量', 1, 3, 1, '累计收藏量');
INSERT INTO `admin_statement_builder_title_config` (`id`, `code`, `title`, `status`, `tabs`, `type`, `remark`)
VALUES (52, 310011, '投递量', 1, 3, 1, '');
INSERT INTO `admin_statement_builder_title_config` (`id`, `code`, `title`, `status`, `tabs`, `type`, `remark`)
VALUES (53, 310012, '投递人数', 1, 3, 1, '同一个人去重统计');
INSERT INTO `admin_statement_builder_title_config` (`id`, `code`, `title`, `status`, `tabs`, `type`, `remark`)
VALUES (54, 310013, '博士投递量', 1, 3, 1, '');
INSERT INTO `admin_statement_builder_title_config` (`id`, `code`, `title`, `status`, `tabs`, `type`, `remark`)
VALUES (55, 310014, '博士投递人数', 1, 3, 1, '同一个人去重统计');
INSERT INTO `admin_statement_builder_title_config` (`id`, `code`, `title`, `status`, `tabs`, `type`, `remark`)
VALUES (56, 310015, '硕士投递量', 1, 3, 1, '');
INSERT INTO `admin_statement_builder_title_config` (`id`, `code`, `title`, `status`, `tabs`, `type`, `remark`)
VALUES (57, 310016, '硕士投递人数', 1, 3, 1, '同一个人去重统计');
INSERT INTO `admin_statement_builder_title_config` (`id`, `code`, `title`, `status`, `tabs`, `type`, `remark`)
VALUES (58, 310017, '博士海外经历', 1, 3, 1, '海外工作或者最高学历有海外的博士数量');
INSERT INTO `admin_statement_builder_title_config` (`id`, `code`, `title`, `status`, `tabs`, `type`, `remark`)
VALUES (59, 310018, '博士985/211', 1, 3, 1, '博士中选出勾选最高教育经历勾选了 985/211');
INSERT INTO `admin_statement_builder_title_config` (`id`, `code`, `title`, `status`, `tabs`, `type`, `remark`)
VALUES (60, 310019, '博士且工作2年及以上', 1, 3, 1, '博士中工作超过两年的人数');
INSERT INTO `admin_statement_builder_title_config` (`id`, `code`, `title`, `status`, `tabs`, `type`, `remark`)
VALUES (61, 310020, '博士为职场人', 1, 3, 1, '对应身份字段去统计（统计身份为职场人）');
INSERT INTO `admin_statement_builder_title_config` (`id`, `code`, `title`, `status`, `tabs`, `type`, `remark`)
VALUES (62, 310021, '博士为应届生/在校生', 1, 3, 1, '对应身份字段去统计（统计身份为应届生/在校生）');
INSERT INTO `admin_statement_builder_title_config` (`id`, `code`, `title`, `status`, `tabs`, `type`, `remark`)
VALUES (63, 310022, '高级职称', 1, 3, 1, '对人不去重统计');
INSERT INTO `admin_statement_builder_title_config` (`id`, `code`, `title`, `status`, `tabs`, `type`, `remark`)
VALUES (64, 310023, '中级职称', 1, 3, 1, '对人不去重统计');
INSERT INTO `admin_statement_builder_title_config` (`id`, `code`, `title`, `status`, `tabs`, `type`, `remark`)
VALUES (65, 310024, '副高级职称', 1, 3, 1, '对人不去重统计');
INSERT INTO `admin_statement_builder_title_config` (`id`, `code`, `title`, `status`, `tabs`, `type`, `remark`)
VALUES (66, 210003, '单位创建时间', 1, 2, 1, '单位创建时间');
INSERT INTO `admin_statement_builder_title_config` (`id`, `code`, `title`, `status`, `tabs`, `type`, `remark`)
VALUES (67, 210004, '发布公告数量', 1, 2, 1, '');
INSERT INTO `admin_statement_builder_title_config` (`id`, `code`, `title`, `status`, `tabs`, `type`, `remark`)
VALUES (68, 210005, '发布职位数量', 1, 2, 1, '');
INSERT INTO `admin_statement_builder_title_config` (`id`, `code`, `title`, `status`, `tabs`, `type`, `remark`)
VALUES (69, 210006, '收到简历数量', 1, 2, 1, '');
INSERT INTO `admin_statement_builder_title_config` (`id`, `code`, `title`, `status`, `tabs`, `type`, `remark`)
VALUES (70, 210007, '博士简历数量', 1, 2, 1, '');
INSERT INTO `admin_statement_builder_title_config` (`id`, `code`, `title`, `status`, `tabs`, `type`, `remark`)
VALUES (71, 210008, '硕士简历数量', 1, 2, 1, '');
INSERT INTO `admin_statement_builder_title_config` (`id`, `code`, `title`, `status`, `tabs`, `type`, `remark`)
VALUES (72, 210009, '本科简历数量', 1, 2, 1, '');
INSERT INTO `admin_statement_builder_title_config` (`id`, `code`, `title`, `status`, `tabs`, `type`, `remark`)
VALUES (73, 210010, '公告点击量总量', 1, 2, 1, '');
INSERT INTO `admin_statement_builder_title_config` (`id`, `code`, `title`, `status`, `tabs`, `type`, `remark`)
VALUES (74, 210011, '职位点击总量', 1, 2, 1, '');
INSERT INTO `admin_statement_builder_title_config` (`id`, `code`, `title`, `status`, `tabs`, `type`, `remark`)
VALUES (75, 210013, '单位关系', 1, 2, 1, '合作单位/非合作单位');
INSERT INTO `admin_statement_builder_title_config` (`id`, `code`, `title`, `status`, `tabs`, `type`, `remark`)
VALUES (84, 210012, '单位点击总量', 1, 2, 1, '');
INSERT INTO `admin_statement_builder_title_config` (`id`, `code`, `title`, `status`, `tabs`, `type`, `remark`)
VALUES (88, 210014, '单位所在省', 1, 2, 1, '');
INSERT INTO `admin_statement_builder_title_config` (`id`, `code`, `title`, `status`, `tabs`, `type`, `remark`)
VALUES (89, 210015, '单位所在市', 1, 2, 1, '');
INSERT INTO `admin_statement_builder_title_config` (`id`, `code`, `title`, `status`, `tabs`, `type`, `remark`)
VALUES (90, 210016, '邀面数量', 1, 2, 1, '');
INSERT INTO `admin_statement_builder_title_config` (`id`, `code`, `title`, `status`, `tabs`, `type`, `remark`)
VALUES (91, 210017, '站内投递数量', 1, 2, 1, '');
INSERT INTO `admin_statement_builder_title_config` (`id`, `code`, `title`, `status`, `tabs`, `type`, `remark`)
VALUES (92, 210018, '站外投递数量', 1, 2, 1, '');
INSERT INTO `admin_statement_builder_title_config` (`id`, `code`, `title`, `status`, `tabs`, `type`, `remark`)
VALUES (93, 210019, '平台投递数量', 1, 2, 1, '');
INSERT INTO `admin_statement_builder_title_config` (`id`, `code`, `title`, `status`, `tabs`, `type`, `remark`)
VALUES (94, 210020, '邮件投递数量', 1, 2, 1, '');
INSERT INTO `admin_statement_builder_title_config` (`id`, `code`, `title`, `status`, `tabs`, `type`, `remark`)
VALUES (95, 210021, '链接投递数量', 1, 2, 1, '');
INSERT INTO `admin_statement_builder_title_config` (`id`, `code`, `title`, `status`, `tabs`, `type`, `remark`)
VALUES (96, 210022, '单位类型', 1, 2, 1, '');
INSERT INTO `admin_statement_builder_title_config` (`id`, `code`, `title`, `status`, `tabs`, `type`, `remark`)
VALUES (97, 210023, '单位性质', 1, 2, 1, '');
INSERT INTO `admin_statement_builder_title_config` (`id`, `code`, `title`, `status`, `tabs`, `type`, `remark`)
VALUES (98, 210024, '单位业务员', 1, 2, 1, '');


# 第二次
UPDATE `admin_statement_builder_title_config`
SET `code`   = 520001,
    `title`  = '单位ID',
    `status` = 1,
    `tabs`   = 5,
    `type`   = 2,
    `remark` = ''
WHERE `id` = 38;

INSERT INTO `admin_statement_builder_title_config` (`id`, `code`, `title`, `status`, `tabs`, `type`, `remark`)
VALUES (null, 520002, '单位名称', 1, 5, 2, '');
INSERT INTO `admin_statement_builder_title_config` (`id`, `code`, `title`, `status`, `tabs`, `type`, `remark`)
VALUES (null, 520003, '投递量', 1, 5, 2, '');
INSERT INTO `admin_statement_builder_title_config` (`id`, `code`, `title`, `status`, `tabs`, `type`, `remark`)
VALUES (null, 520004, '公告投递量', 1, 5, 2, '');
INSERT INTO `admin_statement_builder_title_config` (`id`, `code`, `title`, `status`, `tabs`, `type`, `remark`)
VALUES (null, 520005, '职位投递量', 1, 5, 2, '');
INSERT INTO `admin_statement_builder_title_config` (`id`, `code`, `title`, `status`, `tabs`, `type`, `remark`)
VALUES (null, 520006, '博士简历数量', 1, 5, 2, '');
INSERT INTO `admin_statement_builder_title_config` (`id`, `code`, `title`, `status`, `tabs`, `type`, `remark`)
VALUES (null, 520007, '硕士简历数量', 1, 5, 2, '');
INSERT INTO `admin_statement_builder_title_config` (`id`, `code`, `title`, `status`, `tabs`, `type`, `remark`)
VALUES (null, 520008, '本科简历数量', 1, 5, 2, '');
INSERT INTO `admin_statement_builder_title_config` (`id`, `code`, `title`, `status`, `tabs`, `type`, `remark`)
VALUES (null, 520009, '站内投递数量', 1, 5, 2, '');
INSERT INTO `admin_statement_builder_title_config` (`id`, `code`, `title`, `status`, `tabs`, `type`, `remark`)
VALUES (null, 520010, '站外投递数量', 1, 5, 2, '');
INSERT INTO `admin_statement_builder_title_config` (`id`, `code`, `title`, `status`, `tabs`, `type`, `remark`)
VALUES (null, 520011, '平台投递数量', 1, 5, 2, '');
INSERT INTO `admin_statement_builder_title_config` (`id`, `code`, `title`, `status`, `tabs`, `type`, `remark`)
VALUES (null, 520012, '邮件投递数量', 1, 5, 2, '');
INSERT INTO `admin_statement_builder_title_config` (`id`, `code`, `title`, `status`, `tabs`, `type`, `remark`)
VALUES (null, 520013, '链接投递数量', 1, 5, 2, '');


# 第三次
INSERT INTO `admin_statement_builder_title_config` (`id`, `code`, `title`, `status`, `tabs`, `type`, `remark`)
VALUES (null, 310025, '公告阅读量', 1, 3, 1, '会根据导出时间去计算时间内的公告阅读总量');

# 第四次
INSERT INTO `admin_statement_builder_title_config` (`id`, `code`, `title`, `status`, `tabs`, `type`, `remark`)
VALUES (null, 310026, '单位阅读量', 1, 3, 1, '单位阅读量');
INSERT INTO `admin_statement_builder_title_config` (`id`, `code`, `title`, `status`, `tabs`, `type`, `remark`)
VALUES (null, 310027, '公告收藏量', 1, 3, 1, '公告收藏量');
INSERT INTO `admin_statement_builder_title_config` (`id`, `code`, `title`, `status`, `tabs`, `type`, `remark`)
VALUES (null, 310028, '单位收藏量', 1, 3, 1, '单位收藏量');


