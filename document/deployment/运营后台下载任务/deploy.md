# ReadMe

### 关联产品原型版本
- 下载中心

***

### 参与人员

- 龚传栋
- 杜孙鹤

***

### 分支

|仓库|开发分支|提測分支|备注|
|:----:|:----:|:----:|:----:|
|new_gaoxiao_yii|feature/运营后台下载中心|release/运营后台下载中心|-|



***

### 上线部署步骤

`内容为空,填写"-"即可`

|步骤|是否执行|执行内容|
|:----|:----:|:----|
|提醒前端提前合并代码到master和添加权限节点|-|-|
|执行sql语句|是|见下方"执行sql语句"|
|更新后端代码|-|-|
|composer安装|-|见下方"composer安装"|
|更新配置|-|见下方"更新配置"|
|创建队列|-|见下方"创建队列"|
|执行脚本|-|见下方"执行脚本"|
|删除redis缓存|-|见下方"删除redis缓存"|
|重启队列|-|上线前, 暂停所有队列, 上线后再重启|
|更新前端代码|-|-|
|添加定时任务|是|见下方"定时任务"|
|群内通知部署完毕|是|-|

#### 执行sql语句(按顺序执行)

* alter_data.sql

#### 添加配置文件

```
在common/params-local.php里面添加一个阿里云oss的包
具体需要参考本地文件
```

#### 添加队列任务

```
[program:运营后台下载任务]
command=/www/wwwroot/new_gaoxiao_yii/yii admin-download-task-queue/listen
directory=/www/wwwroot/new_gaoxiao_yii/
autorestart=true
startsecs=3
startretries=3
stdout_logfile=/www/server/panel/plugin/supervisor/log/运营后台下载任务.out.log
stderr_logfile=/www/server/panel/plugin/supervisor/log/运营后台下载任务.err.log
stdout_logfile_maxbytes=2MB
stderr_logfile_maxbytes=2MB
user=www
priority=999
numprocs=1
process_name=%(program_name)s_%(process_num)02d
```

