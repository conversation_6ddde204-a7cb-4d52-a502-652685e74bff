CREATE TABLE `pay_transform_buried_point_log`
(
    `id`           int(10)          NOT NULL AUTO_INCREMENT,
    `add_time`     datetime         NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`  datetime         NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '更新时间',
    `ip`           int(11) unsigned NOT NULL DEFAULT '0' COMMENT '用户ip',
    `user_cookies` varchar(64)      NOT NULL DEFAULT '' COMMENT '用户的cookie',
    `action_type`  tinyint(4)       NOT NULL DEFAULT '0' COMMENT '动作类型\n',
    `action_id`    int(10)          NOT NULL COMMENT '事件id',
    `params`       text             NOT NULL COMMENT '事件参数',
    `action_url`   varchar(255)     NOT NULL DEFAULT '' COMMENT '操作页面',
    `member_id`    int(11)          NOT NULL DEFAULT '0' COMMENT '用户id',
    `action_name`  varchar(255)     NOT NULL COMMENT '事件名称',
    `platform`     tinyint(4)       NOT NULL COMMENT '触发端口（1pc端  2小程序）',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='支付转化用户埋点数据表';


ALTER TABLE pay_transform_buried_point_log
    ADD COLUMN `uuid` bigint(32) unsigned NOT NULL COMMENT '唯一码';
ALTER TABLE pay_transform_buried_point_log
    ADD COLUMN `order_no` varchar(255) NOT NULL DEFAULT '' COMMENT '平台订单号';

ALTER TABLE pay_transform_buried_point_log
    MODIFY COLUMN action_url VARCHAR(1024) NOT NULL DEFAULT '' COMMENT '操作页面';