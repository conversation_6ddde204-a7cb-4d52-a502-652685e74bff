<?php

namespace admin\controllers;

use common\service\memberCancel\ResumeCancelDataCleanService;
use common\service\memberCancel\ResumeCancelSearchService;
use Yii;
use yii\base\Exception;

/**
 * 求职者注销管理控制器
 * 运营后台用于管理求职者注销申请
 */
class ResumeCancelController extends BaseAdminController
{
    /**
     * 获取注销申请列表
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionGetList()
    {
        try {
            $params = Yii::$app->request->get();
            $data   = ResumeCancelSearchService::getList($params);

            return $this->success($data);
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取注销申请详情
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionGetDetail()
    {
        try {
            $cancelLogId = Yii::$app->request->get('cancelLogId');
            if (empty($cancelLogId)) {
                throw new Exception('注销日志ID不能为空');
            }

            $data = ResumeCancelSearchService::getDetail($cancelLogId);

            return $this->success($data);
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取统计数据
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionGetStatistics()
    {
        try {
            $params = Yii::$app->request->get();
            $data   = ResumeCancelSearchService::getStatistics($params);

            return $this->success($data);
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取筛选选项
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionGetFilterOptions()
    {
        try {
            $data = ResumeCancelSearchService::getFilterOptions();

            return $this->success($data);
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 人工执行注销（运营后台操作）
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionManualCancel()
    {
        $cancelLogId = Yii::$app->request->post('cancelLogId');
        if (empty($cancelLogId)) {
            return $this->fail('注销日志ID不能为空');
        }
        try {
            // 开启事务
            $transaction = Yii::$app->db->beginTransaction();

            $adminId = Yii::$app->user->id;

            // 执行注销操作
            $dataCleanService = new ResumeCancelDataCleanService();
            $result           = $dataCleanService->executeCancel($cancelLogId, $adminId);

            $transaction->commit();

            return $this->success($result, '注销操作执行成功');
        } catch (Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

}