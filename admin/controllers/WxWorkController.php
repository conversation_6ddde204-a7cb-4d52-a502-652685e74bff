<?php

namespace admin\controllers;

use admin\models\Admin;
use admin\models\LoginForm;
use common\base\models\BaseActivityFormOptionSign;
use common\base\models\BaseAdminDownloadTask;
use common\helpers\StringHelper;
use common\libs\Cache;
use common\libs\WxWork;
use common\service\stat\OperationDepartmentService;
use common\service\stat\StateApplication;
use EasyWeChat\Factory;
use Yii;

class WxWorkController extends BaseAdminController
{

    public function actionCallback()
    {
        $get = Yii::$app->request->get();

        $code  = $get['code'];
        $state = $get['state'];

        if ($code && $state) {
            $key = Cache::PC_ADMIN_WXWORK_LOGIN_KEY . ':' . $state;
            // 保留一分钟
            Cache::set($key, $code, 300);
            echo "<script type='text/javascript'>document.onload = window.close();</script>";
        }
    }

    public function actionNotify()
    {
    }

    public function actionGetLoginConfig()
    {
        $config = Yii::$app->params['wxWork'];
        $state  = StringHelper::randText() . '_' . CUR_TIMESTAMP;

        $data = [
            'appid'       => $config['corp_id'],
            'agentid'     => $config['agent_id'],
            'state'       => $state,
            'redirectUri' => Yii::$app->request->hostInfo . '/wx-work/callback',
            // 'redirectUri' => 'http://test.admin.gcjob.ideaboat.cn/wx-work/callback1',
        ];

        return $this->success($data);
    }

    public function actionCheckLogin()
    {
        $state = Yii::$app->request->get('state');

        if ($state) {
            $key  = Cache::PC_ADMIN_WXWORK_LOGIN_KEY . ':' . $state;
            $code = Cache::get($key);

            if ($code) {
                Cache::delete($key);
                try {
                    $config    = Yii::$app->params['wxWork'];
                    $app       = Factory::work($config);
                    $user      = $app->oauth->detailed()
                        ->userFromCode($code);
                    $userId    = $user->getId(); // 对应企业微信英文名（userid）
                    $loginForm = new LoginForm();
                    if ($loginForm->loginByWxWork($userId)) {
                        $model = new Admin();
                        $model->loginLog();

                        return $this->success([
                            'name'   => Yii::$app->user->identity->name,
                            // 随机token,其实作用就是给前端记住登录状态
                            'token'  => StringHelper::randNumber(32),
                            'router' => [

                            ],
                        ]);
                    } else {
                        return $this->success();
                    }
                } catch (\Exception $e) {
                    return $this->fail($e->getMessage());
                }
            } else {
                // 还没有
                return $this->success();
            }
        } else {
            return $this->success();
        }
    }

    /**
     * 企业微信JS-SDK登录验证接口
     * POST /wx-work/login-by-code
     */
    public function actionLoginByCode()
    {
        $request = Yii::$app->request;
        $code    = $request->post('code');
        $state   = $request->post('state');

        if (!$code || !$state) {
            return $this->fail('参数错误');
        }

        // 验证state参数防CSRF攻击
        if (!$this->validateState($state)) {
            return $this->fail('状态验证失败，请重新登录');
        }

        try {
            $config = Yii::$app->params['wxWork'];
            $app    = Factory::work($config);
            $user   = $app->oauth->detailed()
                ->userFromCode($code);
            $userId = $user->getId(); // 对应企业微信英文名（userid）

            $loginForm = new LoginForm();
            if ($loginForm->loginByWxWork($userId)) {
                $model = new Admin();
                $model->loginLog();

                return $this->success([
                    'name'   => Yii::$app->user->identity->name,
                    // 随机token,其实作用就是给前端记住登录状态
                    'token'  => StringHelper::randNumber(32),
                    'router' => [

                    ],
                ]);
            }

            return $this->fail('登录失败，请联系管理员绑定企业微信账号');
        } catch (\Exception $e) {
            return $this->fail('登录失败：' . $e->getMessage());
        }
    }

    /**
     * 验证state参数防CSRF攻击
     * @param string $state
     * @return bool
     */
    private function validateState($state)
    {
        // 简单验证state格式：随机字符串_时间戳
        if (!preg_match('/^[a-zA-Z0-9]+_\d+$/', $state)) {
            return false;
        }

        // 验证时间戳是否在合理范围内（10分钟内）
        $parts       = explode('_', $state);
        $timestamp   = end($parts);
        $currentTime = time();

        // 检查时间戳是否在10分钟内
        return $currentTime - $timestamp <= 600;
    }

    public function actionGetAllUser()
    {
        $wxWork = WxWork::getInstance();

        return $this->success($wxWork->getAllUser());
    }

    public function actionServiceCallback()
    {
        $wxWorkConfig = Yii::$app->params['wxWork'];

        $config = [
            'corp_id'  => $wxWorkConfig['corp_id'],
            'agent_id' => $wxWorkConfig['agent_id'],
            'secret'   => $wxWorkConfig['secret'],
            'token'    => $wxWorkConfig['token'],
            'aes_key'  => $wxWorkConfig['aes_key'],
        ];

        $app = Factory::work($config);

        $app->server->push(function ($message) {
            $fromUserName = $message['FromUserName'];
            $admin        = Admin::findOne(['wx_work_userid' => $fromUserName]);

            // 这里做一下限制
            switch ($message['MsgType']) {
                case 'event':
                    if ($message['Event'] === 'taskcard_click') {
                        // 点击卡片任务
                        $text = $this->clickKey($message, $admin);

                        return $text;
                    }

                    return $this->textToData($message['EventKey'], $admin->id);
                case 'text':
                    return $this->textToData($message['Content'], $admin->id);
                case 'image':
                    return '收到图片消息';
                case 'voice':
                    return '收到语音消息';
                case 'video':
                    return '收到视频消息';
                case 'location':
                    return '收到坐标消息';
                case 'link':
                    return '收到链接消息';
                case 'file':
                    return '收到文件消息';
                default:
                    return '收到其它消息';
            }
        });

        $response = $app->server->serve();

        $response->send();

        exit;
    }

    private function textToData($text, $adminId)
    {
        if (!$adminId) {
            return '你没有权限';
        }
        $statService = StateApplication::getInstance();
        $wx          = WxWork::getInstance();
        switch ($text) {
            case '投递量':
                if (!$wx->checkPermissions($adminId, $wx::PERMISSIONS_RESUME)) {
                    return '没有权限';
                }

                return '请确认你是需要多长时间的投递量数据,可回复: 今日投递量,昨日投递量,近7天投递量,近30天投递量';
            case '今日投递量':
                if (!$wx->checkPermissions($adminId, $wx::PERMISSIONS_RESUME)) {
                    return '没有权限';
                }

                return $statService->todayAllJobApply();
            case '昨日投递量':
                if (!$wx->checkPermissions($adminId, $wx::PERMISSIONS_RESUME)) {
                    return '没有权限';
                }

                return $statService->yesterdayAllJobApply();
            case '近7天投递量':
                if (!$wx->checkPermissions($adminId, $wx::PERMISSIONS_RESUME)) {
                    return '没有权限';
                }

                return $statService->last7DayAllJobApply();
            case '近30天投递量':
                if (!$wx->checkPermissions($adminId, $wx::PERMISSIONS_RESUME)) {
                    return '没有权限';
                }

                return $statService->last30DayAllJobApply();
            case '人才数据':
                if (!$wx->checkPermissions($adminId, $wx::PERMISSIONS_JOB_APPLY)) {
                    return '没有权限';
                }

                return $statService->last3DayResumeData();

            case '邀约投递数据':
                if (!$wx->checkPermissions($adminId, $wx::PERMISSIONS_JOB_APPLY)) {
                    return '没有权限';
                }

                $statService->invitationResultData($adminId);

                exit;
            // case '今日双会数据':
            //     if (!$wx->checkPermissions($adminId, $wx::PERMISSIONS_DOUBLE_MEETING)) {
            //         return '没有权限';
            //     }
            //
            //     $path = Yii::getAlias('@base');
            //
            //     $shell = $path . '/timer_yii stat/double-meeting-resume-list ' . $adminId . ' 2 s> /dev/null 2>/dev/null &';
            //     shell_exec($shell);
            //
            //     return '数据比较多,请稍等';
            // case '昨日双会数据':
            //     if (!$wx->checkPermissions($adminId, $wx::PERMISSIONS_DOUBLE_MEETING)) {
            //         return '没有权限';
            //     }
            //
            //     $path = Yii::getAlias('@base');
            //
            //     $shell = $path . '/timer_yii stat/double-meeting-resume-list ' . $adminId . ' 3 s> /dev/null 2>/dev/null &';
            //     shell_exec($shell);
            //
            //     return '数据比较多,请稍等';
            // case '近7天双会数据':
            //     if (!$wx->checkPermissions($adminId, $wx::PERMISSIONS_DOUBLE_MEETING)) {
            //         return '没有权限';
            //     }
            //     $path = Yii::getAlias('@base');
            //
            //     $shell = $path . '/timer_yii stat/double-meeting-resume-list ' . $adminId . ' 4 s> /dev/null 2>/dev/null &';
            //     shell_exec($shell);
            //
            //     return '数据比较多,请稍等';
            // case '近30天双会数据':
            //     if (!$wx->checkPermissions($adminId, $wx::PERMISSIONS_DOUBLE_MEETING)) {
            //         return '没有权限';
            //     }
            //     $path = Yii::getAlias('@base');
            //
            //     $shell = $path . '/timer_yii stat/double-meeting-resume-list ' . $adminId . ' 5 s> /dev/null 2>/dev/null &';
            //     shell_exec($shell);
            //
            //     return '数据比较多,请稍等';
            // case '双会实时数据':
            //     if (!$wx->checkPermissions($adminId, $wx::PERMISSIONS_DOUBLE_MEETING)) {
            //         return '没有权限';
            //     }
            //
            //     $path = Yii::getAlias('@base');
            //
            //     $shell = $path . '/timer_yii stat/double-meeting-resume-list ' . $adminId . ' true s> /dev/null 2>/dev/null &';
            //     shell_exec($shell);
            //
            //     return '数据比较多,请稍等';
            case '近10天订单数据':
                if (!$wx->checkPermissions($adminId, $wx::PERMISSIONS_ORDER)) {
                    return '没有权限';
                }

                return StateApplication::getInstance()
                    ->last10DayOrder();
            case '我的客户数据':
                if (!$wx->checkPermissions($adminId, $wx::PERMISSIONS_MY_COMPANY_LIST)) {
                    return '没有权限或者在等待下载';
                }

                BaseAdminDownloadTask::create($adminId, BaseAdminDownloadTask::TYPE_MY_COMPANY_LIST,
                    ['adminId' => $adminId]);

                return '数据比较多,请稍等';
            default:

                // 这里检测关键字 选项ID=
                if (strpos($text, '选项ID=') !== false) {
                    $optionId = str_replace('选项ID=', '', $text);
                    $optionId = intval($optionId);
                    if ($optionId) {
                        return BaseActivityFormOptionSign::getOptionSignStatistic($optionId)['message'];
                    }
                }

                // 这里检测关键字 #开头，代表查询数据
                if (strpos($text, '#') === 0) {
                    if (!$wx->checkPermissions($adminId, $wx::PERMISSIONS_RESUME)) {
                        return '没有权限';
                    }
                    $text = substr($text, 1);

                    return (new OperationDepartmentService())->textToData($text);
                }

                return '无法识别';
        }
    }

    private function clickKey($data, $adminId)
    {
        if (!$adminId) {
            return '你没有权限';
        }
        $wx = WxWork::getInstance();

        return $wx->verifyClick($data, $adminId);
        // data
        //
        //   'ToUserName' => 'wwc9b3b67df0d43d97',
        //   'FromUserName' => 'GongChuanDong',
        //   'MsgType' => 'event',
        //   'Event' => 'taskcard_click',
        //   'CreateTime' => '1736237891',
        //   'EventKey' => 'verificationCodeTaskConfirm',
        //   'TaskId' => 'verificationCodeTask7',
        //   'AgentId' => '1000019',

    }

}