<?php

namespace admin\models;

use common\base\models\BaseCompanyFeatureTag;
use common\base\models\BaseCompanyFeatureTagRelation;
use yii\base\Exception;

class CompanyFeatureTagRelation extends BaseCompanyFeatureTagRelation
{
    /**
     * 获取公司配置的特色标签列表
     * @param array|string $companyIds 公司等id
     * @param array        $field      查询等字段
     * @return array|\yii\db\ActiveRecord[]
     */
    public static function getCompanyFeaturedTagList(
        $companyIds,
        array $field = [
            't.tag',
            'tr.company_id',
            't.id as feature_tag_id',
        ]
    ): array {
        if (!is_array($companyIds)) {
            $companyIds = explode(',', $companyIds);
        }

        return self::find()
            ->alias('tr')
            ->select($field)
            ->filterWhere([
                'in',
                'tr.company_id',
                $companyIds,
            ])
            ->leftJoin(['t' => BaseCompanyFeatureTag::tableName()], 't.id=tr.feature_tag_id')
            ->asArray()
            ->all();
    }

    /**
     * 批量编辑单位特色标签(先删除后新增)
     * @param string $companyId      单位id
     * @param array  $featuredTagIds 新增的id
     * @throws Exception
     */
    public static function batchSaveCompanyFeaturedTagsRelation($companyId, $featuredTagIds = []): void
    {
        $transaction = \Yii::$app->db->beginTransaction();

        try {
            self::deleteAll(['company_id' => $companyId]);

            if ($featuredTagIds) {
                foreach ($featuredTagIds as $featuredTagId) {
                    $model = new self();
                    $model->company_id = $companyId;
                    $model->feature_tag_id = $featuredTagId;
                    $res = $model->save();
                    if (!$res) {
                        $transaction->rollBack();
                        throw new Exception('更新失败');
                    }
                }
            }
            $transaction->commit();
        } catch (Exception $exception) {
            $transaction->rollBack();
            throw new Exception($exception->getMessage());
        }
    }
}