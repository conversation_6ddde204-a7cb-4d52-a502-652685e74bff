<?php

namespace admin\models;

use common\base\models\BaseAdmin;
use common\base\models\BaseAdminDepartment;
use common\base\models\BaseAdminPosition;
use common\helpers\IpHelper;
use common\libs\WxWork;
use common\models\AdminLoginLog;
use common\models\AdminPositionMenu;
use UserAgentParser\Provider\WhichBrowser;
use Yii;
use yii\base\Exception;
use yii\base\NotSupportedException;
use yii\web\IdentityInterface;

class Admin extends BaseAdmin implements IdentityInterface
{

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return '{{%admin}}';
    }

    public function rules()
    {
        return array_merge(parent::rules(), [
            [
                ['username'],
                'required',
                'message' => '用户名必须',
            ],
            [
                ['name'],
                'required',
                'message' => '姓名必须',
            ],
            [
                ['position_id'],
                'required',
                'message' => '职位必须选择',
            ],
            [
                ['department_id'],
                'required',
                'message' => '部门不能为空',
            ],
            [
                ['password'],
                'required',
                'message' => '密码必须填写',
                'on'      => ['create'],
            ],
            [
                'username',
                'only',
                'params' => [
                    'message' => '该用户名已经存在',
                    'field'   => ['username'],
                ],
            ],
            [
                'wx_work_userid',
                'only',
                'params' => [
                    'message' => '该id已经被绑定',
                    'field'   => ['wx_work_userid'],
                ],
            ],
            [
                'job_number',
                'only',
                'params' => [
                    'message' => '该工号已经存在',
                    'field'   => ['job_number'],
                ],
            ],
        ]);
    }

    /**
     * 添加账号/修改账号信息
     * @param $data
     */
    public function create($data)
    {
        $id = $data['id'];
        if ($id) {
            $model = self::findOne($id);
        } else {
            $model = new self();
            if (!$data['password']) {
                throw new Exception('请输入密码');
            }
            $model->password = Yii::$app->getSecurity()
                ->generatePasswordHash($data['password']);
        }

        $model->username       = $data['username'];
        $model->name           = $data['name'];
        $model->job_number     = $data['jobNumber'];
        $model->wx_work_userid = $data['wxWorkUserid'] ?: '';

        if ($id && $data['password']) {
            throw new Exception('不允许修改密码');
        }
        $model->position_id = $data['positionId'];
        // 找到对应的部门
        $department           = AdminPosition::findOne($data['positionId']);
        $model->department_id = $department->department_id;

        if (!$model->save()) {
            throw new Exception($model->getFirstErrorsMessage());
        }
    }

    public static function getList($params)
    {
        $query = self::find()
            ->select('a.id,a.name,p.name as position,d.name as department,a.name as assistant,d.id as department_id,a.username,a.add_time,a.position_id,a.job_number,a.wx_work_userid,a.status')
            ->alias('a')
            ->leftJoin(['p' => AdminPosition::tableName()], 'p.id=a.position_id') // 职位
            ->leftJoin(['d' => AdminDepartment::tableName()], 'd.id=a.department_id'); // 部门

        $query->andFilterCompare('a.position_id', $params['positionId']);
        $query->andFilterCompare('a.department_id', $params['departmentId']);
        $query->andFilterCompare('p.id', 0, '!=');
        $query->andFilterCompare('a.name', $params['name'], 'like');
        $query->andFilterCompare('a.username', $params['username'], 'like');
        $query->andFilterCompare('a.job_number', $params['jobNumber'], 'like');
        $query->andFilterCompare('a.status', $params['status']);

        $count    = $query->count();
        $pageSize = $params['pageSize'] ?: Yii::$app->params['defaultPageSize'];

        $pages = self::setPage($count, $params['page'], $pageSize);

        $list = $query->offset($pages['offset'])
            ->limit($pages['limit'])
            ->orderBy('a.id')
            ->asArray()
            ->all();

        foreach ($list as &$item) {
            $item['statusTxt'] = self::STATUS_LIST[$item['status']];
            // 获取职位的全部权限菜单
            $item['permissions'] = self::getPermissionsNameList($item['position_id']);
        }

        $data = [
            'list'  => $list,
            'pages' => [
                'size'  => (int)$pageSize,
                'total' => (int)$count,
            ],
        ];

        return $data;
    }


    public static function getPermissionsNameList($positionId)
    {
        $menuList = AdminPositionMenu::find()
            ->alias('a')
            ->select([
                'b.name',
            ])
            ->innerJoin(['b' => AdminMenu::tableName()], 'b.id=a.admin_menu_id')
            ->where(['a.admin_position_id' => $positionId])
            ->asArray()
            ->all();

        return array_column($menuList, 'name');
    }

    /**
     * 获取权限列表
     */
    public static function getPermissionsList($userId)
    {
        // 找到对应的职位
        $positionId = self::findOneVal(['id' => $userId], 'position_id');
        // 找到对应的menuList
        if ($positionId) {
            $where    = ['admin_position_id' => $positionId];
            $menuList = AdminPositionMenu::find()
                ->alias('a')
                ->select([
                    'key',
                    'admin_menu_action_ids',
                ])
                ->innerJoin(['b' => AdminMenu::tableName()], 'b.id=a.admin_menu_id')
                ->where($where)
                ->andWhere(['b.status' => 1])
                ->asArray()
                ->all();
            // 这里需要把用户的信息来处理一下
            $newMenuList = [];
            foreach ($menuList as $datum) {
                // 找到有的action对于的key
                if ($datum['admin_menu_action_ids']) {
                    $keysRs        = AdminMenuAction::find()
                        ->select(['key'])
                        ->where(['id' => explode(',', $datum['admin_menu_action_ids'])])
                        ->asArray()
                        ->all();
                    $keyArray      = array_column($keysRs, 'key');
                    $newMenuList[] = [
                        'route'  => $datum['key'],
                        'action' => $keyArray,
                    ];
                } else {
                    $newMenuList[] = [
                        'route'  => $datum['key'],
                        'action' => [],
                    ];
                }
            }
            $newMenuList[]['route'] = 'home';
        } else {
            $menuList = AdminMenu::find()
                ->select([
                    'key',
                    'id',
                ])
                ->andWhere(['status' => 1])
                ->andWhere([
                    '<>',
                    'parent_id',
                    0,
                ])
                ->asArray()
                ->all();
            // 这里需要把用户的信息来处理一下
            foreach ($menuList as $datum) {
                // 找到对应的action
                $keysRs        = AdminMenuAction::find()
                    ->select(['key'])
                    ->where(['admin_menu_id' => explode(',', $datum['id'])])
                    ->asArray()
                    ->all();
                $keyArray      = array_column($keysRs, 'key');
                $newMenuList[] = [
                    'route'  => $datum['key'],
                    'action' => $keyArray,
                ];
            }
            $newMenuList[]['route'] = 'home';
        }

        return $newMenuList;
    }

    /**
     * 写登录日志
     */
    public function loginLog($type = 0)
    {
        $ua = $_SERVER['HTTP_USER_AGENT'];
        try {
            // 在这里才记录登录
            $result = (new WhichBrowser())->parse($ua);
            if (!Yii::$app->user->getId()) {
                return;
            }
            $data = [
                'admin_id'   => \Yii::$app->user->getId() ?: '',
                'os'         => $result->getOperatingSystem()
                    ->getName() ?: '',
                'os_version' => $result->getOperatingSystem()
                    ->getVersion()
                    ->getComplete() ?: '',
                'wx_version' => $result->getBrowser()
                    ->getVersion()
                    ->getComplete() ?: '',
                'model'      => $result->getDevice()
                    ->getModel() ?: '',
                'user_agent' => $ua,
            ];

            if (isset(Yii::$app->request->userIP)) {
                $data['ip'] = IpHelper::getIpInt();
            }

            $model = new AdminLoginLog();
            $model->load($data, '');
            $model->save();

            $wxWork = WxWork::getInstance();

            switch ($type) {
                case self::LOGIN_TYPE_PASSWORD:
                    // $wxWork->message(Yii::$app->user->getId(), '有人使用账号密码的方式登录了你的账号,请注意');
                    break;
                case self::LOGIN_TYPE_WX_WORK:
                    // $wxWork->message(Yii::$app->user->getId(), /**/'有人使用企业微信扫码登录的方式登录了你的账号,请注意');
                    break;
            }
        } catch (\Exception $e) {
        }
    }

    /**
     * {@inheritdoc}
     */
    public static function findIdentity($id)
    {
        return static::findOne([
            'id'     => $id,
            'status' => self::STATUS_ACTIVE,
        ]);
    }

    public static function changeStatus($id)
    {
        $model = self::findOne($id);
        if (!$model) {
            throw new \Exception('用户不存在');
        }
        if ($model) {
            $model->status = $model->status == self::STATUS_ACTIVE ? self::STATUS_STOP : self::STATUS_ACTIVE;
            $model->save();

            return true;
        }

        return false;
    }

    /**
     * {@inheritdoc}
     */
    public static function findIdentityByAccessToken($token, $type = null)
    {
        throw new NotSupportedException('"findIdentityByAccessToken" is not implemented.');
    }

    /**
     * Finds user by username
     *
     * @param string $username
     * @return static|null
     */
    public static function findByUsername($username)
    {
        return static::findOne([
            'username' => $username,
            'status'   => self::STATUS_ACTIVE,
        ]);
    }

    /**
     * Finds user by username
     *
     * @param string $userId
     * @return static|null
     */
    public static function findByWxWorkdId($userId)
    {
        return static::findOne([
            'wx_work_userid' => $userId,
            'status'         => self::STATUS_ACTIVE,
        ]);
    }

    /**
     * Finds user by username
     *
     * @param string $name
     * @return static|null
     */
    public static function findByName($name)
    {
        return static::findOne([
            'name'   => $name,
            'status' => self::STATUS_ACTIVE,
        ]);
    }

    /**
     * Finds user by password reset token
     *
     * @param string $token password reset token
     * @return static|null
     */
    public static function findByPasswordResetToken($token)
    {
        if (!static::isPasswordResetTokenValid($token)) {
            return null;
        }

        return static::findOne([
            'password_reset_token' => $token,
            'status'               => self::STATUS_ACTIVE,
        ]);
    }

    /**
     * Finds out if password reset token is valid
     *
     * @param string $token password reset token
     * @return bool
     */
    public static function isPasswordResetTokenValid($token)
    {
        if (empty($token)) {
            return false;
        }

        $timestamp = (int)substr($token, strrpos($token, '_') + 1);
        $expire    = Yii::$app->params['user.passwordResetTokenExpire'];

        return $timestamp + $expire >= time();
    }

    /**
     * {@inheritdoc}
     */
    public function getId()
    {
        return $this->getPrimaryKey();
    }

    /**
     * {@inheritdoc}
     */
    public function getAuthKey()
    {
        return true;
    }

    /**
     * {@inheritdoc}
     */
    public function validateAuthKey($authKey)
    {
        return $this->getAuthKey() === $authKey;
    }

    /**
     * Validates password
     *
     * @param string $password password to validate
     * @return bool if password provided is valid for current user
     */
    public function validatePassword($password)
    {
        return Yii::$app->security->validatePassword($password, $this->password);
    }

    /**
     * Generates password hash from password and sets it to the model
     *
     * @param string $password
     */
    public function setPassword($password)
    {
        $this->password_hash = Yii::$app->security->generatePasswordHash($password);
    }

    /**
     * Generates "remember me" authentication key
     */
    //    public function generateAuthKey()
    //    {
    //        $this->auth_key = Yii::$app->security->generateRandomString();
    //    }

    /**
     * Generates new password reset token
     */
    public function generatePasswordResetToken()
    {
        $this->password_reset_token = Yii::$app->security->generateRandomString() . '_' . time();
    }

    /**
     * Removes password reset token
     */
    public function removePasswordResetToken()
    {
        $this->password_reset_token = null;
    }

    public static function getAllSaleList()
    {
        // 暂时把全部admin返回
        $list = self::find()
            ->where([
                'a.status' => self::STATUS_ACTIVE,
            ])
            ->alias('a')
            ->select('a.id,a.name,d.name as department,p.name as position')
            ->innerJoin(['d' => BaseAdminDepartment::tableName()], 'd.id=a.department_id')
            ->innerJoin(['p' => BaseAdminPosition::tableName()], 'p.id=a.position_id')
            ->asArray()
            ->orderBy('a.id asc')
            ->asArray()
            ->all();

        return $list;
    }
}
