/*! iScroll v5.2.0-snapshot ~ (c) 2008-2017 <PERSON> ~ http://cubiq.org/license */
!function(t,i,e){var s=t.requestAnimationFrame||t.webkitRequestAnimationFrame||t.mozRequestAnimationFrame||t.oRequestAnimationFrame||t.msRequestAnimationFrame||function(i){t.setTimeout(i,1e3/60)},n=function(){var s={},n=i.createElement("div").style,o=function(){for(var t=["t","webkitT","MozT","msT","OT"],i=0,e=t.length;i<e;i++)if(t[i]+"ransform"in n)return t[i].substr(0,t[i].length-1);return!1}();function r(t){return!1!==o&&(""===o?t:o+t.charAt(0).toUpperCase()+t.substr(1))}s.getTime=Date.now||function(){return(new Date).getTime()},s.extend=function(t,i){for(var e in i)t[e]=i[e]},s.addEvent=function(t,i,e,s){t.addEventListener(i,e,!!s)},s.removeEvent=function(t,i,e,s){t.removeEventListener(i,e,!!s)},s.prefixPointerEvent=function(i){return t.MSPointerEvent?"MSPointer"+i.charAt(7).toUpperCase()+i.substr(8):i},s.momentum=function(t,i,s,n,o,r){var h,a,l=t-i,c=e.abs(l)/s;return a=c/(r=void 0===r?6e-4:r),(h=t+c*c/(2*r)*(l<0?-1:1))<n?(h=o?n-o/2.5*(c/8):n,a=(l=e.abs(h-t))/c):h>0&&(h=o?o/2.5*(c/8):0,a=(l=e.abs(t)+h)/c),{destination:e.round(h),duration:a}};var h=r("transform");return s.extend(s,{hasTransform:!1!==h,hasPerspective:r("perspective")in n,hasTouch:"ontouchstart"in t,hasPointer:!(!t.PointerEvent&&!t.MSPointerEvent),hasTransition:r("transition")in n}),s.isBadAndroid=function(){var i=t.navigator.appVersion;if(/Android/.test(i)&&!/Chrome\/\d/.test(i)){var e=i.match(/Safari\/(\d+.\d)/);return!(e&&"object"==typeof e&&e.length>=2)||parseFloat(e[1])<535.19}return!1}(),s.extend(s.style={},{transform:h,transitionTimingFunction:r("transitionTimingFunction"),transitionDuration:r("transitionDuration"),transitionDelay:r("transitionDelay"),transformOrigin:r("transformOrigin"),touchAction:r("touchAction")}),s.hasClass=function(t,i){return new RegExp("(^|\\s)"+i+"(\\s|$)").test(t.className)},s.addClass=function(t,i){if(!s.hasClass(t,i)){var e=t.className.split(" ");e.push(i),t.className=e.join(" ")}},s.removeClass=function(t,i){if(s.hasClass(t,i)){var e=new RegExp("(^|\\s)"+i+"(\\s|$)","g");t.className=t.className.replace(e," ")}},s.offset=function(t){for(var i=-t.offsetLeft,e=-t.offsetTop;t=t.offsetParent;)i-=t.offsetLeft,e-=t.offsetTop;return{left:i,top:e}},s.preventDefaultException=function(t,i){for(var e in i)if(i[e].test(t[e]))return!0;return!1},s.extend(s.eventType={},{touchstart:1,touchmove:1,touchend:1,mousedown:2,mousemove:2,mouseup:2,pointerdown:3,pointermove:3,pointerup:3,MSPointerDown:3,MSPointerMove:3,MSPointerUp:3}),s.extend(s.ease={},{quadratic:{style:"cubic-bezier(0.25, 0.46, 0.45, 0.94)",fn:function(t){return t*(2-t)}},circular:{style:"cubic-bezier(0.1, 0.57, 0.1, 1)",fn:function(t){return e.sqrt(1- --t*t)}},back:{style:"cubic-bezier(0.175, 0.885, 0.32, 1.275)",fn:function(t){return(t-=1)*t*(5*t+4)+1}},bounce:{style:"",fn:function(t){return(t/=1)<1/2.75?7.5625*t*t:t<2/2.75?7.5625*(t-=1.5/2.75)*t+.75:t<2.5/2.75?7.5625*(t-=2.25/2.75)*t+.9375:7.5625*(t-=2.625/2.75)*t+.984375}},elastic:{style:"",fn:function(t){return 0===t?0:1==t?1:.4*e.pow(2,-10*t)*e.sin((t-.055)*(2*e.PI)/.22)+1}}}),s.tap=function(t,e){var s=i.createEvent("Event");s.initEvent(e,!0,!0),s.pageX=t.pageX,s.pageY=t.pageY,t.target.dispatchEvent(s)},s.click=function(e){var s,n=e.target;/(SELECT|INPUT|TEXTAREA)/i.test(n.tagName)||((s=i.createEvent(t.MouseEvent?"MouseEvents":"Event")).initEvent("click",!0,!0),s.view=e.view||t,s.detail=1,s.screenX=n.screenX||0,s.screenY=n.screenY||0,s.clientX=n.clientX||0,s.clientY=n.clientY||0,s.ctrlKey=!!e.ctrlKey,s.altKey=!!e.altKey,s.shiftKey=!!e.shiftKey,s.metaKey=!!e.metaKey,s.button=0,s.relatedTarget=null,s._constructed=!0,n.dispatchEvent(s))},s.getTouchAction=function(t,i){var e="none";return"vertical"===t?e="pan-y":"horizontal"===t&&(e="pan-x"),i&&"none"!=e&&(e+=" pinch-zoom"),e},s.getRect=function(t){if(t instanceof SVGElement){var i=t.getBoundingClientRect();return{top:i.top,left:i.left,width:i.width,height:i.height}}return{top:t.offsetTop,left:t.offsetLeft,width:t.offsetWidth,height:t.offsetHeight}},s}();function o(e,s){for(var o in this.wrapper="string"==typeof e?i.querySelector(e):e,this.scroller=this.wrapper.children[0],this.scrollerStyle=this.scroller.style,this.options={disablePointer:!n.hasPointer,disableTouch:n.hasPointer||!n.hasTouch,disableMouse:n.hasPointer||n.hasTouch,startX:0,startY:0,scrollY:!0,directionLockThreshold:5,momentum:!0,bounce:!0,bounceTime:600,bounceEasing:"",preventDefault:!0,preventDefaultException:{tagName:/^(INPUT|TEXTAREA|BUTTON|SELECT)$/},HWCompositing:!0,useTransition:!0,useTransform:!0,bindToWrapper:void 0===t.onmousedown},s)this.options[o]=s[o];this.translateZ=this.options.HWCompositing&&n.hasPerspective?" translateZ(0)":"",this.options.useTransition=n.hasTransition&&this.options.useTransition,this.options.useTransform=n.hasTransform&&this.options.useTransform,this.options.eventPassthrough=!0===this.options.eventPassthrough?"vertical":this.options.eventPassthrough,this.options.preventDefault=!this.options.eventPassthrough&&this.options.preventDefault,this.options.scrollY="vertical"!=this.options.eventPassthrough&&this.options.scrollY,this.options.scrollX="horizontal"!=this.options.eventPassthrough&&this.options.scrollX,this.options.freeScroll=this.options.freeScroll&&!this.options.eventPassthrough,this.options.directionLockThreshold=this.options.eventPassthrough?0:this.options.directionLockThreshold,this.options.bounceEasing="string"==typeof this.options.bounceEasing?n.ease[this.options.bounceEasing]||n.ease.circular:this.options.bounceEasing,this.options.resizePolling=void 0===this.options.resizePolling?60:this.options.resizePolling,!0===this.options.tap&&(this.options.tap="tap"),this.options.useTransition||this.options.useTransform||/relative|absolute/i.test(this.scrollerStyle.position)||(this.scrollerStyle.position="relative"),this.x=0,this.y=0,this.directionX=0,this.directionY=0,this._events={},this._init(),this.refresh(),this.scrollTo(this.options.startX,this.options.startY),this.enable()}o.prototype={version:"5.2.0-snapshot",_init:function(){this._initEvents()},destroy:function(){this._initEvents(!0),clearTimeout(this.resizeTimeout),this.resizeTimeout=null,this._execEvent("destroy")},_transitionEnd:function(t){t.target==this.scroller&&this.isInTransition&&(this._transitionTime(),this.resetPosition(this.options.bounceTime)||(this.isInTransition=!1,this._execEvent("scrollEnd")))},_start:function(t){if(1!=n.eventType[t.type]&&0!==(t.which?t.button:t.button<2?0:4==t.button?1:2))return;if(this.enabled&&(!this.initiated||n.eventType[t.type]===this.initiated)){!this.options.preventDefault||n.isBadAndroid||n.preventDefaultException(t.target,this.options.preventDefaultException)||t.preventDefault();var i,s=t.touches?t.touches[0]:t;this.initiated=n.eventType[t.type],this.moved=!1,this.distX=0,this.distY=0,this.directionX=0,this.directionY=0,this.directionLocked=0,this.startTime=n.getTime(),this.options.useTransition&&this.isInTransition?(this._transitionTime(),this.isInTransition=!1,i=this.getComputedPosition(),this._translate(e.round(i.x),e.round(i.y)),this._execEvent("scrollEnd")):!this.options.useTransition&&this.isAnimating&&(this.isAnimating=!1,this._execEvent("scrollEnd")),this.startX=this.x,this.startY=this.y,this.absStartX=this.x,this.absStartY=this.y,this.pointX=s.pageX,this.pointY=s.pageY,this._execEvent("beforeScrollStart")}},_move:function(t){if(this.enabled&&n.eventType[t.type]===this.initiated){this.options.preventDefault&&t.preventDefault();var i,s,o,r,h=t.touches?t.touches[0]:t,a=h.pageX-this.pointX,l=h.pageY-this.pointY,c=n.getTime();if(this.pointX=h.pageX,this.pointY=h.pageY,this.distX+=a,this.distY+=l,o=e.abs(this.distX),r=e.abs(this.distY),!(c-this.endTime>300&&o<10&&r<10)){if(this.directionLocked||this.options.freeScroll||(o>r+this.options.directionLockThreshold?this.directionLocked="h":r>=o+this.options.directionLockThreshold?this.directionLocked="v":this.directionLocked="n"),"h"==this.directionLocked){if("vertical"==this.options.eventPassthrough)t.preventDefault();else if("horizontal"==this.options.eventPassthrough)return void(this.initiated=!1);l=0}else if("v"==this.directionLocked){if("horizontal"==this.options.eventPassthrough)t.preventDefault();else if("vertical"==this.options.eventPassthrough)return void(this.initiated=!1);a=0}a=this.hasHorizontalScroll?a:0,l=this.hasVerticalScroll?l:0,i=this.x+a,s=this.y+l,(i>0||i<this.maxScrollX)&&(i=this.options.bounce?this.x+a/3:i>0?0:this.maxScrollX),(s>0||s<this.maxScrollY)&&(s=this.options.bounce?this.y+l/3:s>0?0:this.maxScrollY),this.directionX=a>0?-1:a<0?1:0,this.directionY=l>0?-1:l<0?1:0,this.moved||this._execEvent("scrollStart"),this.moved=!0,this._translate(i,s),c-this.startTime>300&&(this.startTime=c,this.startX=this.x,this.startY=this.y)}}},_end:function(t){if(this.enabled&&n.eventType[t.type]===this.initiated){this.options.preventDefault&&!n.preventDefaultException(t.target,this.options.preventDefaultException)&&t.preventDefault();t.changedTouches&&t.changedTouches[0];var i,s,o=n.getTime()-this.startTime,r=e.round(this.x),h=e.round(this.y),a=e.abs(r-this.startX),l=e.abs(h-this.startY),c=0,u="";if(this.isInTransition=0,this.initiated=0,this.endTime=n.getTime(),!this.resetPosition(this.options.bounceTime)){if(this.scrollTo(r,h),!this.moved)return this.options.tap&&n.tap(t,this.options.tap),this.options.click&&n.click(t),void this._execEvent("scrollCancel");if(this._events.flick&&o<200&&a<100&&l<100)this._execEvent("flick");else{if(this.options.momentum&&o<300&&(i=this.hasHorizontalScroll?n.momentum(this.x,this.startX,o,this.maxScrollX,this.options.bounce?this.wrapperWidth:0,this.options.deceleration):{destination:r,duration:0},s=this.hasVerticalScroll?n.momentum(this.y,this.startY,o,this.maxScrollY,this.options.bounce?this.wrapperHeight:0,this.options.deceleration):{destination:h,duration:0},r=i.destination,h=s.destination,c=e.max(i.duration,s.duration),this.isInTransition=1),r!=this.x||h!=this.y)return(r>0||r<this.maxScrollX||h>0||h<this.maxScrollY)&&(u=n.ease.quadratic),void this.scrollTo(r,h,c,u);this._execEvent("scrollEnd")}}}},_resize:function(){var t=this;clearTimeout(this.resizeTimeout),this.resizeTimeout=setTimeout(function(){t.refresh()},this.options.resizePolling)},resetPosition:function(t){var i=this.x,e=this.y;return t=t||0,!this.hasHorizontalScroll||this.x>0?i=0:this.x<this.maxScrollX&&(i=this.maxScrollX),!this.hasVerticalScroll||this.y>0?e=0:this.y<this.maxScrollY&&(e=this.maxScrollY),(i!=this.x||e!=this.y)&&(this.scrollTo(i,e,t,this.options.bounceEasing),!0)},disable:function(){this.enabled=!1},enable:function(){this.enabled=!0},refresh:function(){n.getRect(this.wrapper),this.wrapperWidth=this.wrapper.clientWidth,this.wrapperHeight=this.wrapper.clientHeight;var t=n.getRect(this.scroller);this.scrollerWidth=t.width,this.scrollerHeight=t.height,this.maxScrollX=this.wrapperWidth-this.scrollerWidth,this.maxScrollY=this.wrapperHeight-this.scrollerHeight,this.hasHorizontalScroll=this.options.scrollX&&this.maxScrollX<0,this.hasVerticalScroll=this.options.scrollY&&this.maxScrollY<0,this.hasHorizontalScroll||(this.maxScrollX=0,this.scrollerWidth=this.wrapperWidth),this.hasVerticalScroll||(this.maxScrollY=0,this.scrollerHeight=this.wrapperHeight),this.endTime=0,this.directionX=0,this.directionY=0,n.hasPointer&&!this.options.disablePointer&&(this.wrapper.style[n.style.touchAction]=n.getTouchAction(this.options.eventPassthrough,!0),this.wrapper.style[n.style.touchAction]||(this.wrapper.style[n.style.touchAction]=n.getTouchAction(this.options.eventPassthrough,!1))),this.wrapperOffset=n.offset(this.wrapper),this._execEvent("refresh"),this.resetPosition()},on:function(t,i){this._events[t]||(this._events[t]=[]),this._events[t].push(i)},off:function(t,i){if(this._events[t]){var e=this._events[t].indexOf(i);e>-1&&this._events[t].splice(e,1)}},_execEvent:function(t){if(this._events[t]){var i=0,e=this._events[t].length;if(e)for(;i<e;i++)this._events[t][i].apply(this,[].slice.call(arguments,1))}},scrollBy:function(t,i,e,s){t=this.x+t,i=this.y+i,e=e||0,this.scrollTo(t,i,e,s)},scrollTo:function(t,i,e,s){s=s||n.ease.circular,this.isInTransition=this.options.useTransition&&e>0;var o=this.options.useTransition&&s.style;!e||o?(o&&(this._transitionTimingFunction(s.style),this._transitionTime(e)),this._translate(t,i)):this._animate(t,i,e,s.fn)},scrollToElement:function(t,i,s,o,r){if(t=t.nodeType?t:this.scroller.querySelector(t)){var h=n.offset(t);h.left-=this.wrapperOffset.left,h.top-=this.wrapperOffset.top;var a=n.getRect(t),l=n.getRect(this.wrapper);!0===s&&(s=e.round(a.width/2-l.width/2)),!0===o&&(o=e.round(a.height/2-l.height/2)),h.left-=s||0,h.top-=o||0,h.left=h.left>0?0:h.left<this.maxScrollX?this.maxScrollX:h.left,h.top=h.top>0?0:h.top<this.maxScrollY?this.maxScrollY:h.top,i=null==i||"auto"===i?e.max(e.abs(this.x-h.left),e.abs(this.y-h.top)):i,this.scrollTo(h.left,h.top,i,r)}},_transitionTime:function(t){if(this.options.useTransition){t=t||0;var i=n.style.transitionDuration;if(i&&(this.scrollerStyle[i]=t+"ms",!t&&n.isBadAndroid)){this.scrollerStyle[i]="0.0001ms";var e=this;s(function(){"0.0001ms"===e.scrollerStyle[i]&&(e.scrollerStyle[i]="0s")})}}},_transitionTimingFunction:function(t){this.scrollerStyle[n.style.transitionTimingFunction]=t},_translate:function(t,i){this.options.useTransform?this.scrollerStyle[n.style.transform]="translate("+t+"px,"+i+"px)"+this.translateZ:(t=e.round(t),i=e.round(i),this.scrollerStyle.left=t+"px",this.scrollerStyle.top=i+"px"),this.x=t,this.y=i},_initEvents:function(i){var e=i?n.removeEvent:n.addEvent,s=this.options.bindToWrapper?this.wrapper:t;e(t,"orientationchange",this),e(t,"resize",this),this.options.click&&e(this.wrapper,"click",this,!0),this.options.disableMouse||(e(this.wrapper,"mousedown",this),e(s,"mousemove",this),e(s,"mousecancel",this),e(s,"mouseup",this)),n.hasPointer&&!this.options.disablePointer&&(e(this.wrapper,n.prefixPointerEvent("pointerdown"),this),e(s,n.prefixPointerEvent("pointermove"),this),e(s,n.prefixPointerEvent("pointercancel"),this),e(s,n.prefixPointerEvent("pointerup"),this)),n.hasTouch&&!this.options.disableTouch&&(e(this.wrapper,"touchstart",this),e(s,"touchmove",this),e(s,"touchcancel",this),e(s,"touchend",this)),e(this.scroller,"transitionend",this),e(this.scroller,"webkitTransitionEnd",this),e(this.scroller,"oTransitionEnd",this),e(this.scroller,"MSTransitionEnd",this)},getComputedPosition:function(){var i,e,s=t.getComputedStyle(this.scroller,null);return this.options.useTransform?(i=+((s=s[n.style.transform].split(")")[0].split(", "))[12]||s[4]),e=+(s[13]||s[5])):(i=+s.left.replace(/[^-\d.]/g,""),e=+s.top.replace(/[^-\d.]/g,"")),{x:i,y:e}},_animate:function(t,i,e,o){var r=this,h=this.x,a=this.y,l=n.getTime(),c=l+e;this.isAnimating=!0,function u(){var p,f,d,m=n.getTime();if(m>=c)return r.isAnimating=!1,r._translate(t,i),void(r.resetPosition(r.options.bounceTime)||r._execEvent("scrollEnd"));d=o(m=(m-l)/e),p=(t-h)*d+h,f=(i-a)*d+a,r._translate(p,f),r.isAnimating&&s(u)}()},handleEvent:function(t){switch(t.type){case"touchstart":case"pointerdown":case"MSPointerDown":case"mousedown":this._start(t);break;case"touchmove":case"pointermove":case"MSPointerMove":case"mousemove":this._move(t);break;case"touchend":case"pointerup":case"MSPointerUp":case"mouseup":case"touchcancel":case"pointercancel":case"MSPointerCancel":case"mousecancel":this._end(t);break;case"orientationchange":case"resize":this._resize();break;case"transitionend":case"webkitTransitionEnd":case"oTransitionEnd":case"MSTransitionEnd":this._transitionEnd(t);break;case"wheel":case"DOMMouseScroll":case"mousewheel":this._wheel(t);break;case"keydown":this._key(t);break;case"click":this.enabled&&!t._constructed&&(t.preventDefault(),t.stopPropagation())}}},o.utils=n,"undefined"!=typeof module&&module.exports?module.exports=o:"function"==typeof define&&define.amd?define(function(){return o}):t.IScroll=o}(window,document,Math),function(t,i,e){"undefined"!=typeof module&&module.exports?module.exports=i(t,e):"function"==typeof define&&define.amd?define(i(t,e)):t.TagNav=i.call(t,t,e)}(this,function(t,i){"use strict";var e,s=(e={},{subscribe:function(t,i){return e[t]||(e[t]={queue:[]}),function(t,i){return{remove:function(){delete e[t].queue[i]}}}(t,e[t].queue.push(i)-1)},publish:function(t,i){if(e[t]&&e[t].queue.length)for(var s=e[t].queue,n=0,o=s.length;n<o;n++)"function"==typeof s[n]&&s[n](i||{})}});function n(t,i){return this instanceof n?this._init(t,i):new n(t,i)}return n.prototype={constructor:n,_init:function(t,i){var e=this,s=$(t),n=s.find("ul").first(),o=s.find("li");switch(e.el=t,e.opts=i||{},e.type=this.opts.type||"simple",e.curClassName=e.opts.curClassName||"weui-state-active",e.index=e.opts.index,e.iScroll=new IScroll(e.el,{scrollX:!0,scrollY:!1,click:!0,bindToWrapper:!0}),void 0===e.index&&(e.index=e.index||n.find("."+e.curClassName).index(),e.index<0&&(e.index=0)),this.opts.type){case"scrollToFirst":e._scrollToFirstEvent();break;case"scrollToNext":e._scrollToNextEvent()}o.on("click",function(t){e.switchTo($(this).index(),t)}),e.$list=n,e.$items=o,e.switchTo(e.index)},_scrollToFirstEvent:function(){var t=this;s.subscribe("select",function(i){var e=t.$items.eq(i.index)[0];setTimeout(function(){t.iScroll.scrollToElement(e,400)},180)})},_scrollToNextEvent:function(){var t,i=this;s.subscribe("select",function(e){void 0===t&&(t=i.index?0:1);var s,n=e.index>t,o=i.$items.eq(e.index)[n?"next":"prev"]().offset()||i.$items.eq(e.index).offset(),r=$(i.el).offset();if(n?o.left+o.width>r.left+r.width:o.left<r.left){s=i.$list.offset();var h=n?r.width-o.left+s.left-o.width:s.left-o.left;setTimeout(function(){i.iScroll.scrollTo(h,0,400)},180)}t=e.index})},switchTo:function(t,i){this.$items.removeClass(this.curClassName).eq(t).addClass(this.curClassName),this.index=t,s.publish("select",{index:t})},unselect:function(){this.index=-1,this.$items.removeClass(self.curClassName)},getIndex:function(){return this.index}},n},document);