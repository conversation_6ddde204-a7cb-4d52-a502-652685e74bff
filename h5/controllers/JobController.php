<?php

namespace h5\controllers;

use admin\models\Trade;
use common\base\models\BaseArea;
use common\base\models\BaseCategoryJob;
use common\base\models\BaseDictionary;
use common\base\models\BaseFile;
use common\base\models\BaseHomePosition;
use common\base\models\BaseJob;
use common\base\models\BaseJobApply;
use common\base\models\BaseJobApplyRecord;
use common\base\models\BaseJobClickLog;
use common\base\models\BaseJobCollect;
use common\base\models\BaseJobReport;
use common\base\models\BaseJobSubscribe;
use common\base\models\BaseJobSubscribeSendLog;
use common\base\models\BaseMember;
use common\base\models\BaseResume;
use common\base\models\BaseResumeComplete;
use common\base\models\BaseResumeIntention;
use common\base\models\BaseShowcase;
use common\base\models\BaseTrade;
use common\base\models\BaseWelfareLabel;
use common\helpers\ArrayHelper;
use common\helpers\FileHelper;
use common\helpers\HtmlHelper;
use common\helpers\UrlHelper;
use common\libs\BaiduTimeFactor;
use common\libs\ToutiaoTimeFactor;
use common\libs\WxMiniApp;
use common\service\CommonService;
use common\service\job\RecommendService;
use common\service\jobSubscribe\JobSubscribeApplication;
use common\service\match\PersonToJobNewService;
use common\service\match\PersonToJobService;
use common\service\resume\DeliveryService;
use common\service\search\CommonSearchApplication;
use common\service\specialNeedService\JobInformationService;
use h5\models\Announcement;
use h5\models\Area;
use h5\models\HomePosition;
use h5\models\Job;
use h5\models\Major;
use h5\models\Member;
use h5\models\Resume;
use h5\models\ResumeAttachment;
use h5\models\ResumeEquity;
use h5\models\ResumeEquitySetting;
use common\service\match\MatchCompleteService;
use h5\models\ResumeJobReportRecord;
use h5\models\JobReport;
use Yii;
use yii\base\Exception;
use yii\helpers\Url;

class JobController extends BaseH5Controller
{
    /**
     * @throws \Exception
     */
    public function actionIndex(): string
    {
        //获取请求数据
        $searchData = \Yii::$app->request->get();
        //判断用户条件，进行分发
        $memberId = Yii::$app->user->id;

        //判断用户是否有求职意向
        $intentionInfo = BaseResumeIntention::findOne(['member_id' => $memberId]);

        if (!empty($memberId) && !empty($intentionInfo)) {
            return $this->hasIntentionList();
        } else {
            return $this->notIntentionList();
        }
    }

    /**
     * 没有求职意向的职位列表页面
     * @return string
     */
    public function notIntentionList()
    {
        $searchData = \Yii::$app->request->get();

        // 开启内存到1g
        ini_set('memory_limit', '1024M');
        //获取职位列表
        try {
            $isLogin = false;
            if (!empty(Yii::$app->user->id)) {
                $isLogin                = true;
                $searchData['memberId'] = Yii::$app->user->id;
            }
            // 这里需要对有编制的一些查询进行过滤,避免非会员使用了这个参数进去
            // 校验是否拥有编制查询权益
            $resumeId = $this->getResumeId();
            if (ResumeEquity::checkEquity($resumeId, ResumeEquitySetting::ID_ESTABLISHMENT_QUERY) === false) {
                unset($searchData['isEstablishment'], $searchData['applyHeatType']);
            }
            // 迁移到服务层
            $app  = new CommonSearchApplication();
            $data = $app->h5JobListSearch($searchData);
        } catch (Exception $e) {
            $data = [];
        }

        //学科分类
        $majorList = Major::getAllListByLevel2();
        //获取单位类型
        $companyTypeList = BaseDictionary::getCompanyTypeList();
        //获取工作地点
        $cityList    = BaseArea::getAllHierarchyCityList();
        $hotCityList = \Yii::$app->params['homePosition']['jobList']['hotCity'];
        //发布时间
        $releaseTimeList = BaseDictionary::getReleaseTimeList();
        //学历要求
        $educationRequire = BaseDictionary::getEducationList();
        //行业类别
        $tradeList = BaseTrade::getTradeList();
        //单位性质
        $companyNatureList = BaseDictionary::getCompanyNatureList();
        //单位规模
        $companyScaleList = BaseDictionary::getCompanyScaleList();
        //薪资范围
        $wageRangeList = BaseDictionary::getWageRangeList();
        //经验要求
        $experienceList = BaseDictionary::getExperienceList();
        //工作性质
        $jobNatureList = BaseDictionary::getNatureList();
        //职称
        $titleTypeList = BaseDictionary::getTitleList();
        //职位福利
        $welfareLabelList = BaseWelfareLabel::getWelfareLabelList();
        //职位类型
        $categoryJobList = BaseCategoryJob::getPersonCategoryJobList();
        //获取职位热度列表
        $jobHeatList = BaseJob::APPLY_HEAT_TYPE_TEXT_LIST;

        $seoConfig = Yii::$app->params['seo']['jobList'];

        //获取职位类型的信息，用于展示选中--这里
        $jobCategoryName = '';
        if (!empty($searchData['jobCategoryId'])) {
            //查询名称--这里改为多选的信息回
            $jobCategoryId = array_filter(explode('_', $searchData['jobCategoryId']));
            foreach ($jobCategoryId as $Id) {
                $jobCategoryName .= BaseCategoryJob::findOneVal(['id' => $Id], 'name') . ',';
            }
            $jobCategoryName = substr($jobCategoryName, 0, -1);
        }

        //新增广告位，推荐单位
        $recommendCompanyList = HomePosition::getShowCaseList('zhiweituijiandanweiK1_m', 'jobList');

        $this->setSeo([
            'title'       => $seoConfig['title'],
            'keywords'    => $seoConfig['keywords'],
            'description' => $seoConfig['description'],
        ]);

        //数据格式化，做回显用的
        if (!empty($searchData['majorId'])) {
            $majorIdList = json_encode(explode('_', $searchData['majorId']));
        }
        if (!empty($searchData['companyType'])) {
            $companyTypeIdList = json_encode(explode('_', $searchData['companyType']));
        }
        if (!empty($searchData['educationType'])) {
            $educationTypeList = json_encode(explode('_', $searchData['educationType']));
        }
        if (!empty($searchData['companyNature'])) {
            $companyNatureIdList = json_encode(explode('_', $searchData['companyNature']));
        }
        if (!empty($searchData['scale'])) {
            $companyScaleTypeList = json_encode(explode('_', $searchData['scale']));
        }
        if (!empty($searchData['experienceType'])) {
            $experienceTypeList = json_encode(explode('_', $searchData['experienceType']));
        }
        if (!empty($searchData['natureType'])) {
            $natureTypeList = json_encode(explode('_', $searchData['natureType']));
        }
        if (!empty($searchData['welfareLabelId'])) {
            $welfareLabelIdList = json_encode(explode('_', $searchData['welfareLabelId']));
        }
        if (!empty($searchData['titleType'])) {
            $titleTypeIdList = json_encode(explode('_', $searchData['titleType']));
        }
        if (!empty($searchData['areaId'])) {
            $areaIdList = json_encode(explode('_', $searchData['areaId']));
        }
        if (!empty($searchData['wageId'])) {
            $wageIdList = json_encode(explode('_', $searchData['wageId']));
        }

        if (!empty($data['list'])) {
            //判断职位是否被查看过
            foreach ($data['list'] as &$item) {
                $item['isClick'] = BaseJobClickLog::checkJobClickStatus($searchData['memberId'], $item['id'], 7);
            }
        }

        //获取添加求职意向的url
        $resumeId   = BaseMember::getMainId($searchData['memberId']);
        $resumeStep = BaseResumeComplete::getResumeStep($resumeId);
        if ($resumeStep < 4) {
            $addIntentionUrl = '/resume/index';
        } else {
            $addIntentionUrl = '/resume/resume-intention-view';
        }

        //获取职位列表广告插入位置
        if ($searchData['page'] == 1 || !$searchData['page']) {
            $listShowcaseInfo = BaseJob::getListShowcaseInfo(count($data['list']), Yii::$app->user->id,
                BaseJob::VIP_SHOWCASE_POSITION_TYPE_H5_JOB_LIST);
        }
        $data = [
            'jobList'              => $data['list'],
            'cityList'             => $cityList,
            'companyNatureList'    => $companyNatureList,
            'companyTypeList'      => $companyTypeList,
            'majorList'            => $majorList,
            'welfareLabelList'     => $welfareLabelList,
            'educationRequire'     => $educationRequire,
            'tradeList'            => $tradeList,
            'jobNatureList'        => $jobNatureList,
            'experienceList'       => $experienceList,
            'wageRangeList'        => $wageRangeList,
            'companyScaleList'     => $companyScaleList,
            'releaseTimeList'      => $releaseTimeList,
            'titleTypeList'        => $titleTypeList,
            'titleTypeIdList'      => $titleTypeIdList,
            'categoryJobList'      => $categoryJobList,
            'jobCategoryName'      => $jobCategoryName,
            'searchData'           => $searchData,
            'majorIdList'          => $majorIdList,
            'companyTypeIdList'    => $companyTypeIdList,
            'educationTypeList'    => $educationTypeList,
            'companyNatureIdList'  => $companyNatureIdList,
            'companyScaleTypeList' => $companyScaleTypeList,
            'experienceTypeList'   => $experienceTypeList,
            'welfareLabelIdList'   => $welfareLabelIdList,
            'natureTypeList'       => $natureTypeList,
            'hotCityList'          => $hotCityList,
            'areaIdList'           => $areaIdList,
            'wageIdList'           => $wageIdList,
            'jobHeatList'          => $jobHeatList,
            'isLogin'              => $isLogin,
            'industryText'         => Trade::getIndustryName($searchData['industryId']),
            'addIntentionUrl'      => $addIntentionUrl,
            'isVip'                => BaseResume::checkVip(Yii::$app->user->id),
            'listShowcaseInfo'     => $listShowcaseInfo,
        ];

        //职位列表热门搜索
        $jobHotSearchList = Job::getHotList();

        $recommendCompanyListOne   = array_slice($recommendCompanyList, 0, 4);
        $recommendCompanyListTwo   = array_slice($recommendCompanyList, 4, 4);
        $recommendCompanyListThree = array_slice($recommendCompanyList, 8, 4);

        return $this->render('notIntentionList.html', array_merge($data, [
            'recommendCompanyListOne'   => $recommendCompanyListOne,
            'recommendCompanyListTwo'   => $recommendCompanyListTwo,
            'recommendCompanyListThree' => $recommendCompanyListThree,
            'jobHotSearchList'          => $jobHotSearchList,
        ]));
    }

    /**
     * @throws \Exception
     */
    private function completeJobInfo($listInfo, $memberId)
    {
        if (!empty($listInfo['list'])) {
            foreach ($listInfo['list'] as &$item) {
                //判断职位是否被查看过(这里需求调整了,不需要判断是否被查看过)
                // $item['isClick'] = BaseJobClickLog::checkJobClickStatus($memberId, $item['job_id'], 7);
                $item['isClick'] = false;
                //获取专业名称
                $item['major'] = BaseJob::getRecommendJobMajorText($item['job_id'], $memberId);
            }
        }
        if (!empty($listInfo['recommend_list'])) {
            foreach ($listInfo['recommend_list'] as &$item) {
                //判断职位是否被查看过
                // $item['isClick'] = BaseJobClickLog::checkJobClickStatus($memberId, $item['job_id'], 7);
                $item['isClick'] = false;
                //获取专业名称
                $item['major'] = BaseJob::getRecommendJobMajorText($item['job_id'], $memberId);
            }
        }

        return $listInfo;
    }

    /**
     * 有求职意向的职位列表
     * @return string
     * @throws Exception
     */
    public function hasIntentionList()
    {
        $memberId = Yii::$app->user->id;

        //获取用户全部求职意向
        $intentionList = BaseResumeIntention::getIntentionJobCateList($memberId);
        //取第一个求职意向职位类型的列表
        // $service  = new PersonToJobService();
        // $params   = [
        //     'intentionId' => $intentionList[0]['id'],
        //     'page'        => 1,
        //     'type'        => $service::LIST_TYPE_RECOMMEND,
        //     'pageSize'    => 10,
        // ];
        // $listInfo = $service->setPlatform(CommonService::PLATFORM_H5)
        //     ->init($params)
        //     ->run();

        //获取完整的信息
        // $listInfo = $this->completeJobInfo($listInfo, $memberId);

        //职位列表热门搜索
        $jobHotSearchList = Job::getHotList();

        //新增广告位，推荐单位
        $recommendCompanyList = HomePosition::getShowCaseList('zhiweituijiandanweiK1_m', 'jobList');

        $recommendCompanyListOne   = array_slice($recommendCompanyList, 0, 4);
        $recommendCompanyListTwo   = array_slice($recommendCompanyList, 4, 4);
        $recommendCompanyListThree = array_slice($recommendCompanyList, 8, 4);

        return $this->render('hasIntentionList.html', [
            // 'jobList'                   => $listInfo['list'],
            // 'recommendList'             => $listInfo['recommend_list'],
            'jobHotSearchList'          => $jobHotSearchList,
            'intentionList'             => $intentionList,
            'recommendCompanyListOne'   => $recommendCompanyListOne,
            'recommendCompanyListTwo'   => $recommendCompanyListTwo,
            'recommendCompanyListThree' => $recommendCompanyListThree,
        ]);
    }

    /**
     * 查询结果页面
     * @return string
     */
    public function actionSearchResult()
    {
        $searchData = Yii::$app->request->get();
        //获取职位列表
        // $data = Job::search($searchData, BaseJob::NEED_PAGE_INFO_YES, true);
        try {
            //$data = Job::searchForList($searchData);
            $isLogin = false;
            if (!empty(Yii::$app->user->id)) {
                $isLogin                = true;
                $searchData['memberId'] = Yii::$app->user->id;
            }

            // 内存到512M
            ini_set('memory_limit', '512M');

            // 这里需要对有编制的一些查询进行过滤,避免非会员使用了这个参数进去
            // 校验是否拥有编制查询权益
            $resumeId = $this->getResumeId();
            if (ResumeEquity::checkEquity($resumeId, ResumeEquitySetting::ID_ESTABLISHMENT_QUERY) === false) {
                unset($searchData['isEstablishment'], $searchData['applyHeatType']);
            }
            // 迁移到服务层
            $app  = new CommonSearchApplication();
            $data = $app->h5JobListSearch($searchData);
        } catch (Exception $e) {
            $data = [];
        }

        //学科分类
        $majorList = Major::getAllListByLevel2();
        //获取单位类型
        $companyTypeList = BaseDictionary::getCompanyTypeList();
        //获取工作地点
        $cityList    = BaseArea::getAllHierarchyCityList();
        $hotCityList = \Yii::$app->params['homePosition']['jobList']['hotCity'];
        //发布时间
        $releaseTimeList = BaseDictionary::getReleaseTimeList();
        //学历要求
        $educationRequire = BaseDictionary::getEducationList();
        //行业类别
        $tradeList = BaseTrade::getTradeList();
        //单位性质
        $companyNatureList = BaseDictionary::getCompanyNatureList();
        //单位规模
        $companyScaleList = BaseDictionary::getCompanyScaleList();
        //薪资范围
        $wageRangeList = BaseDictionary::getWageRangeList();
        //经验要求
        $experienceList = BaseDictionary::getExperienceList();
        //工作性质
        $jobNatureList = BaseDictionary::getNatureList();
        //职称
        $titleTypeList = BaseDictionary::getTitleList();
        //职位福利
        $welfareLabelList = BaseWelfareLabel::getWelfareLabelList();
        //职位类型
        $categoryJobList = BaseCategoryJob::getPersonCategoryJobList();
        //获取职位热度列表
        $jobHeatList = BaseJob::APPLY_HEAT_TYPE_TEXT_LIST;

        $seoConfig = Yii::$app->params['seo']['jobList'];

        //获取职位类型的信息，用于展示选中--这里
        $jobCategoryName = '';
        if (!empty($searchData['jobCategoryId'])) {
            //查询名称--这里改为多选的信息回
            $jobCategoryId = array_filter(explode('_', $searchData['jobCategoryId']));
            foreach ($jobCategoryId as $Id) {
                $jobCategoryName .= BaseCategoryJob::findOneVal(['id' => $Id], 'name') . ',';
            }
            $jobCategoryName = substr($jobCategoryName, 0, -1);
        }

        $this->setSeo([
            'title'       => $seoConfig['title'],
            'keywords'    => $seoConfig['keywords'],
            'description' => $seoConfig['description'],
        ]);

        //数据格式化，做回显用的
        if (!empty($searchData['majorId'])) {
            $majorIdList = json_encode(explode('_', $searchData['majorId']));
        }
        if (!empty($searchData['companyType'])) {
            $companyTypeIdList = json_encode(explode('_', $searchData['companyType']));
        }
        if (!empty($searchData['educationType'])) {
            $educationTypeList = json_encode(explode('_', $searchData['educationType']));
        }
        if (!empty($searchData['companyNature'])) {
            $companyNatureIdList = json_encode(explode('_', $searchData['companyNature']));
        }
        if (!empty($searchData['companyScaleType'])) {
            $companyScaleTypeList = json_encode(explode('_', $searchData['companyScaleType']));
        }
        if (!empty($searchData['experienceType'])) {
            $experienceTypeList = json_encode(explode('_', $searchData['experienceType']));
        }
        if (!empty($searchData['natureType'])) {
            $natureTypeList = json_encode(explode('_', $searchData['natureType']));
        }
        if (!empty($searchData['welfareLabelId'])) {
            $welfareLabelIdList = json_encode(explode('_', $searchData['welfareLabelId']));
        }
        if (!empty($searchData['titleType'])) {
            $titleTypeIdList = json_encode(explode('_', $searchData['titleType']));
        }
        if (!empty($searchData['areaId'])) {
            $areaIdList = json_encode(explode('_', $searchData['areaId']));
        }
        if (!empty($searchData['wageId'])) {
            $wageIdList = json_encode(explode('_', $searchData['wageId']));
        }

        $data = [
            'jobList'              => $data['list'],
            'cityList'             => $cityList,
            'companyNatureList'    => $companyNatureList,
            'companyTypeList'      => $companyTypeList,
            'majorList'            => $majorList,
            'welfareLabelList'     => $welfareLabelList,
            'educationRequire'     => $educationRequire,
            'tradeList'            => $tradeList,
            'jobNatureList'        => $jobNatureList,
            'experienceList'       => $experienceList,
            'wageRangeList'        => $wageRangeList,
            'companyScaleList'     => $companyScaleList,
            'releaseTimeList'      => $releaseTimeList,
            'titleTypeList'        => $titleTypeList,
            'titleTypeIdList'      => $titleTypeIdList,
            'categoryJobList'      => $categoryJobList,
            'jobCategoryName'      => $jobCategoryName,
            'searchData'           => $searchData,
            'majorIdList'          => $majorIdList,
            'companyTypeIdList'    => $companyTypeIdList,
            'educationTypeList'    => $educationTypeList,
            'companyNatureIdList'  => $companyNatureIdList,
            'companyScaleTypeList' => $companyScaleTypeList,
            'experienceTypeList'   => $experienceTypeList,
            'welfareLabelIdList'   => $welfareLabelIdList,
            'natureTypeList'       => $natureTypeList,
            'hotCityList'          => $hotCityList,
            'areaIdList'           => $areaIdList,
            'wageIdList'           => $wageIdList,
            'jobHeatList'          => $jobHeatList,
            'industryText'         => Trade::getIndustryName($searchData['industryId']),
            'isLogin'              => $isLogin,
            'isVip'                => BaseResume::checkVip(Yii::$app->user->id),
        ];

        //获取职位类型列表
        $categoryList    = ArrayHelper::objMoreArr(BaseCategoryJob::getCompanyCategoryJobList());
        $topList         = BaseCategoryJob::getPopularType();
        $jobCategoryList = array_merge($topList, $categoryList);
        //获取职位类型的信息，用于展示选中
        $jobCategoryName = '';
        if (!empty($searchData['jobCategoryId'])) {
            //查询名称
            $jobCategoryId = array_filter(explode('_', $searchData['jobCategoryId']));
            foreach ($jobCategoryId as $Id) {
                $jobCategoryName .= BaseCategoryJob::findOneVal(['id' => $Id], 'name') . ',';
            }
            $jobCategoryName = substr($jobCategoryName, 0, -1);
        }

        $this->setSeo(['title' => Yii::$app->params['seo']['search']['title']]);

        return $this->render('search-result.html', array_merge($data, [
            'jobCategoryName' => $jobCategoryName,
            'jobCategoryList' => $jobCategoryList,
            'keyword'         => $searchData['keyword'],
            'searchData'      => $searchData,
        ]));
    }

    /**
     * 检查用户简历完成度，用于投递前的检查
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionCheckMemberCompleteStatus()
    {
        $memberId = Yii::$app->user->id;

        if (!$memberId) {
            return $this->fail('用户登陆状态错误');
        }
        $resumeId = BaseMember::getMainId($memberId);

        $completePercent = Yii::$app->params['completeResumePercent'];

        //获取用户简历完成度
        $percent = BaseResume::getComplete($memberId);

        if ($percent < $completePercent) {
            //获取用户简历完成步数
            $data['title']   = '提示';
            $data['content'] = '您的在线简历完善度' . $percent . '%，简历完善度达' . $completePercent . '%方可投递，请先完善简历';

            return $this->success($data);
        } else {
            return $this->success();
        }
    }

    public function actionGetList()
    {
        //获取请求数据
        $searchData = \Yii::$app->request->get();

        // 开启内存到1g
        ini_set('memory_limit', '1024M');
        //获取职位列表
        try {
            $data = Job::searchForList($searchData);

            return $this->success($data['list']);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取有求职意向的职位列表
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionGetHasIntentionList()
    {
        //获取请求数据
        $searchData = \Yii::$app->request->get();
        $memberId   = Yii::$app->user->id;
        //获取职位列表
        try {
            //取第一个求职意向职位类型的列表

            // 这里开始临时被修改了 需要的时候就更换注释部分  ---------------------

            // $service  = new PersonToJobService();
            // $pageSize = $searchData['pageSize'];
            // if ($pageSize > 100) {
            //     $pageSize = 100;
            // }
            // $params   = [
            //     'intentionId' => $searchData['intentionId'],
            //     'page'        => $searchData['page'],
            //     'type'        => $searchData['type'],
            //     'pageSize'    => $pageSize,
            // ];
            // $listInfo = $service->setPlatform(CommonService::PLATFORM_H5)
            //     ->init($params)
            //     ->run();

            $service  = new PersonToJobNewService();
            $pageSize = $searchData['pageSize'];
            if ($pageSize > 100) {
                $pageSize = 100;
            }
            $params   = [
                'intentionId' => $searchData['intentionId'],
                'page'        => $searchData['page'],
                'type'        => $searchData['type'],
                'pageSize'    => $pageSize,
                'platform'    => CommonService::PLATFORM_H5,
                'memberId'    => $memberId,
            ];
            $listInfo = $service->getList($params);

            // 这里开始临时被修改了 ---------------------

            $listInfo = $this->completeJobInfo($listInfo, $memberId);
            //获取职位列表广告插入位置
            if ($params['page'] == 1 || !$params['page']) {
                $listInfo['showcaseInfo'] = BaseJob::getListShowcaseInfo(count($listInfo['list']), Yii::$app->user->id,
                    BaseJob::VIP_SHOWCASE_POSITION_TYPE_H5_JOB_LIST);
            }

            return $this->success($listInfo);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    public function actionExclusiveRecommend()
    {
        $id = Yii::$app->request->get('id');
        if (!$id) {
            $this->notFound();
        }

        return $this->render('exclusiveRecommend.html', ['id' => $id]);
    }

    /**
     * 获取订阅专项推荐职位列表
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionGetSubscribeRecommendList()
    {
        $memberId = Yii::$app->user->id;
        $resumeId = BaseMember::getMainId($memberId);
        $id       = Yii::$app->request->get('id');
        try {
            //获取用户订阅的id
            $subscribeId = BaseJobSubscribeSendLog::findOneVal([
                'resume_id' => $resumeId,
                'id'        => $id,
            ], 'id');
            if (!$subscribeId) {
                throw new \Exception('非法操作');
            }
            $service = new JobSubscribeApplication();
            $list    = $service->getLogJobList($id);

            return $this->success($list);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    public function actionSearch()
    {
        //职位类型
        $categoryJobList = BaseCategoryJob::getPersonCategoryJobList();
        //职位列表热门搜索
        $jobNumber        = 'zhiweiliebiao_remensousuo_m';
        $jobHotId         = BaseHomePosition::findOneVal(['number' => $jobNumber], 'id');
        $jobHotSearchList = BaseShowcase::getByPositionConfig($jobHotId, $jobNumber);
        foreach ($jobHotSearchList as $k => $value) {
            if (strlen($value['real_target_link']) < 1) {
                $jobHotSearchList[$k]['url'] = Url::toRoute([
                    'job/index',
                    'keyword' => $value['title'],
                ]);
            }
        }

        return $this->render('search.html', [
            'categoryJobList'  => $categoryJobList,
            'jobHotSearchList' => $jobHotSearchList,
        ]);
    }

    /**
     * 获取职位详情信息
     * @return string
     * @throws \Exception
     */
    public function actionDetail()
    {
        $id = Yii::$app->request->get('id');
        if (empty($id)) {
            $this->notFound();
        }
        $memberId = Yii::$app->user->id;
        $resumeId = BaseMember::getMainId($memberId);
        try {
            $info                  = BaseJob::getJobDetail($id, $memberId);
            $info['applyStatus']   = BaseJobApplyRecord::checkJobApplyStatus($resumeId, $id);
            $info['collectStatus'] = BaseJobCollect::getCollectStatus($id, $memberId);
            $info['userEmail']     = BaseMember::findOneVal(['id' => $memberId], 'email');

            if ($info['status'] == BaseJob::STATUS_DELETE || $info['status'] == BaseJob::STATUS_WAIT || $info['is_show'] == BaseJob::IS_SHOW_NO) {
                $this->notFound();
            }
            //查看公告是否有附件需要显示
            $info['fileList'] = [];
            if (!empty($info['file_ids'])) {
                $file_ids = $info['file_ids'];
            } else {
                $file_ids = $info['announcement_file_ids'];
            }
            if (!empty($file_ids)) {
                $fileIdsArr = explode(',', $file_ids);
                $file_data  = BaseFile::getIdsList($fileIdsArr);
                $fileArr    = [];
                foreach ($file_data as &$value) {
                    if (!empty($value['path'])) {
                        $item['path']   = FileHelper::getFullUrl($value['path']);
                        $item['name']   = $value['name'];
                        $item['suffix'] = FileHelper::getFileSuffixClassName($value['suffix']);
                        $item['id']     = $value['id'];
                        array_push($fileArr, $item);
                    }
                }
                $info['fileList'] = $fileArr;
            }

            $info['isEmailApply']      = $info['isEmailApply'] == 'true' ? 1 : 0;
            $info['isOnlineApply']     = $info['isOnlineApply'] == true ? 1 : 0;
            $info['isOtherApply']      = $info['isOtherApply'] == true ? 1 : 0;
            $info['applyButtonStatus'] = 1;// 申请按钮是否需要置灰 0不置灰 1置灰
            if ($info['isCooperation'] && $info['isOnlineApply']) {//合作单位跳连接的
                $info['applyButtonStatus'] = 0;
            }
            //获取默认的简历id
            $defaultResumeToken = ResumeAttachment::getDefaultResumeToken($memberId);
            //设置默认值
            $resumeAttachmentList  = [];
            $resumeCompletePercent = 0;
            $info['resumeStatus']  = BaseResume::STATUS_UN_COMPLETE_BASE_INFO;

            $info['userStatus'] = Member::USER_STATUS_UN_LOGIN;

            if (!empty($memberId)) {
                $resumeAttachmentList = ResumeAttachment::getList($memberId);

                //判断用户当前的状态（是否完成简历前三步）
                $info['userStatus'] = Member::getUserResumeStatus($memberId);
                if ($info['userStatus'] == Member::USER_STATUS_COMPLETE_RESUME) {
                    //如果简历已经完成了，获取简历完成度
                    $resumeCompletePercent = BaseResume::getComplete($memberId);
                    $info['resumeStatus']  = Resume::STATUS_COMPLETE_BASE_INFO;
                }
            }

            //判断用户是否已经投递了该职位
            $isApply = false;
            if (BaseJobApply::checkJobApplyStatus($memberId, $id) == BaseJob::JOB_APPLY_STATUS_YES) {
                //用户已经投递，暂时不可投递
                $isApply = true;
            }
            //获取推荐职位
            // $recommendJobList = Job::getRecommendList(['pageSize' => Yii::$app->params['recommendJobCount']]);
            $searchData = [
                'jobCategoryId' => $info['jobCategoryId'],
                'companyId'     => $info['companyId'],
                'cityId'        => $info['cityId'],
                'count'         => Yii::$app->params['recommendJobCount'],
                'jobId'         => $id,
            ];
            // $recommendJobList = Job::getRecommendList($searchData);

            //推荐公告
            $recommendService    = new RecommendService();
            $recommendSearchData = [
                'memberId' => $memberId ?: '',
                'jobId'    => $id,
                'limit'    => 6,
            ];
            $recommendList       = $recommendService->setData($recommendSearchData)
                ->getRecommendList();

            $memberId = Yii::$app->user->id ?? 0;
            BaseJobClickLog::create($id, $memberId);

            $seoConfig = Yii::$app->params['seo']['jobDetail'];
            $title     = str_replace('【职位名称】', $info['jobName'], $seoConfig['title']);
            $title     = str_replace('【单位名称】', $info['companyName'], $title);

            $keywords = str_replace('【职位名称】', $info['jobName'], $seoConfig['keywords']);
            $keywords = str_replace('【单位名称】', $info['companyName'], $keywords);

            $description = str_replace('【职位名称】', $info['jobName'], $seoConfig['description']);
            $description = str_replace('【单位名称】', $info['companyName'], $description);

            $this->setSeo([
                'title'       => $title,
                'keywords'    => $keywords,
                'description' => $description,
            ]);

            $showcase = HomePosition::getShowCaseList('detail_HF_m', 'jobDetail');

            $detail_HF_m = $this->renderPartial('/home/<USER>/detail_HF_m.html', [
                'showcase' => $showcase,
            ]);

            $template = 'detail.html';

            // 这里找一下公告的模板?
            if ($info['announcementId']) {
                $templateId = Announcement::findOneVal(['id' => $info['announcementId']], 'template_id');
                if ($templateId == Announcement::TEMPLATE_DOUBLE_MEETING_ACTIVITY) {
                    $template = 'doubleMeetingActivityDetail.html';
                    //$info['applyTypeText'] = '站内报名';
                }
            }

            BaiduTimeFactor::create($info['refresh_time']);
            ToutiaoTimeFactor::create($info['refresh_time']);

            // 当前职位是否已经查看过
            $resumeId  = Resume::findOneVal(['member_id' => $memberId], 'id');
            $hasRecord = ResumeJobReportRecord::checkReportRecord($resumeId, $id);
            // 获取职位匹配度
            $matchInfo['hasRecord'] = $hasRecord;
            $matchInfo['reportUrl'] = Job::getReportUrl($resumeId, $id);
            if ($hasRecord) {
                //$jobApplyTotal     = JobReport::getJobApplyTotal($id);
                $matchInfo['data'] = JobReport::getJopMatchInfo($id, $resumeId, MatchCompleteService::PLATFORM_H5);
            } else {
                $matchInfo['data'] = [];
            }

            $info = (new JobInformationService())->handelJobDetail($info);
            // 2.4 更多查询逻辑
            $info['more_search_params'] = [
                'majorId'       => $info['majorId'],
                'majorIxt'      => explode(',', $info['majorIxt'])[0] ?? '',
                'educationIype' => $info['educationType'],
                'education'     => $info['education'],
                'jobCategoryId' => $info['jobCategoryId'],
                'jobCategory'   => $info['jobCategory'],
                'cityId'        => $info['cityId'],
                'city'          => $info['city'],
                'jobName'       => $info['jobName'],
            ];

            return $this->render($template, [
                //判断进入详情是否是直接还是有父级页面跳转进入
                'referrerBool'          => Yii::$app->request->referrer ? 1 : 0,
                'info'                  => $info,
                'detail_HF_m'           => $detail_HF_m,
                'resumeAttachmentList'  => $resumeAttachmentList,
                'resumeCompletePercent' => $resumeCompletePercent,
                'recommendJobList'      => $recommendList,
                'defaultResumeToken'    => $defaultResumeToken,
                'isApply'               => $isApply,
                'matchInfo'             => $matchInfo,
                'chatPersonSchemeUrl'   => WxMiniApp::getPersonSchemeUrl('chat'),
                'jobSearchResult'       => '/job/search-result?' . http_build_query([
                        'jobCategoryId' => $info['jobCategoryId'],
                        'areaId'        => $info['cityId'],
                        'educationType' => $info['educationType'],
                    ]),
            ]);
        } catch (Exception $exception) {
            //$exception->getMessage() 进这里说明有错误
            //直接报错404
            $this->notFound();
        }
    }

    /**
     * 获取推荐职位列表
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionGetRecommendList()
    {
        $searchData = Yii::$app->request->get();

        // $recommendJobList = Job::getRecommendList($searchData);

        return $this->success($recommendJobList);
    }

    /**
     * 收藏职位
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionCollect()
    {
        try {
            $ids      = Yii::$app->request->post('jobId');
            $memberId = Yii::$app->user->id;
            $msg      = '';
            //获取返回文案
            if (count(explode(',', $ids)) == 1) {
                $isCollect = BaseJobCollect::checkIsCollect($memberId, $ids);
                if ($isCollect) {
                    //如是收藏状态，那此处就是取消收藏
                    $msg = '取消收藏成功';
                } else {
                    $msg = '收藏成功';
                }
            }
            //操作收藏/取消收藏
            $server = new \common\service\person\CollectService();
            $data   = [
                'type' => $server::TYPE_JOB,
                'ids'  => $ids,
            ];
            $server->init($data)
                ->run();

            return $this->success($msg);
        } catch (Exception $e) {
            return $this->result($e->getMessage());
        }
    }

    /**
     * 获取收藏职位列表
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionGetCollectList()
    {
        try {
            $memberId = Yii::$app->user->id;

            $searchData = Yii::$app->request->get();

            $server = new \common\service\person\CollectService();
            $data   = [
                'memberId' => $memberId,
                'resumeId' => $this->getResumeId(),
                'page'     => $searchData['page'],
                'pageSize' => $searchData['pageSize'],
            ];
            $list   = $server->getH5JobList($data);

            return $this->success($list);
        } catch (Exception $e) {
            return $this->result($e->getMessage());
        }
    }

    /**
     * 申请职位
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionApply()
    {
        $transaction = \Yii::$app->db->beginTransaction();
        try {
            //调用服务
            $service = new DeliveryService();
            $result  = $service->setPlatform(CommonService::PLATFORM_H5)
                ->setOparetion(DeliveryService::OPERATION_TYPE_APPLY)
                ->init()
                ->run();

            $transaction->commit();
            $data = [
                'toastType'            => $result['toast_type'] ?? '',
                'link'                 => $result['link'] ?? '',
                'successContentUp'     => $result['apply_success_tips'] ?? '',
                'successContentDown'   => $result['apply_success_qrcode_tips'] ?? '',
                'qrcodeLink'           => $result['qrcode_link'] ?? '',
                'applyStatus'          => $result['apply_status'],
                'wxBindQrCodeImageUrl' => $result['wxBindQrCodeImageUrl'] ?? '',
                'applySuccessMsg'      => $result['applySuccessMsg'] ?? '',
                'applyId'              => $result['applyId'] ?? '',
            ];

            return $this->success($data);
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 职位检测
     */
    public function actionCheckUserApplyStatus()
    {
        try {
            $service = new DeliveryService();
            $result  = $service->setPlatform(CommonService::PLATFORM_H5)
                ->setOparetion(DeliveryService::OPERATION_TYPE_CHECK_APPLY)
                ->init()
                ->run();

            return $this->success($result);
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 用于生成报告前的检查
     */
    public function actionCheckGenerateReport()
    {
        try {
            // 获取职位id
            $jobId = Yii::$app->request->get('jobId');
            // 是否需要确认,非必要参数
            $isConfirm = Yii::$app->request->get('isConfirm', 0);

            if (empty($jobId)) {
                throw new Exception('jobId 不能为空');
            }
            $resumeId = $this->getResumeId();
            if (empty($resumeId)) {
                throw new Exception('系统异常');
            }
            // 校验
            $res = ResumeJobReportRecord::checkGenerateReport($resumeId, $jobId, $isConfirm);

            return $this->success($res);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 生成报告
     * 记录查看记录
     * 记录权益消耗
     */
    public function actionCreateReport()
    {
        try {
            // 获取职位id
            $jobId = Yii::$app->request->get('jobId');
            if (empty($jobId)) {
                throw new Exception('jobId 不能为空');
            }
            $resumeId = $this->getResumeId();
            if (empty($resumeId)) {
                throw new Exception('系统异常');
            }
            // 校验
            $res = ResumeJobReportRecord::checkGenerateReport($resumeId, $jobId);
            if ($res['jump_type'] == 1) {
                // 写记录
                ResumeJobReportRecord::saveReportAndEquityActionRecord($resumeId, $jobId);

                return $this->success(['jump_url' => Job::getReportUrl($resumeId, $jobId)]);
            } else {
                throw new Exception('操作异常');
            }
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 职位报告详情页
     */
    public function actionReport()
    {
        $jobId            = Yii::$app->request->get('id');
        $memberId         = Yii::$app->user->id;
        $resumeId         = Resume::findOneVal(['member_id' => $memberId], 'id');
        $token            = Yii::$app->request->get('token', 0);
        $url              = Job::getDetailUrl($jobId);
        $permissionDenied = "<script>alert('您暂无权限访问该页面');window.location.href='$url';</script>";

        if (empty($resumeId) || empty($token)) {
            exit($permissionDenied);
        }

        $jobRow = Job::find()
            ->select('status,is_show')
            ->where([
                'id' => $jobId,
            ])
            ->asArray()
            ->one();

        if (empty($jobRow)) {
            exit($permissionDenied);
        }
        if ($jobRow['status'] == BaseJob::STATUS_DELETE) {
            exit($permissionDenied);
        }

        if ($jobRow['status'] == BaseJob::STATUS_WAIT) {
            exit($permissionDenied);
        }

        if ($jobRow['is_show'] == BaseJob::IS_SHOW_NO) {
            exit($permissionDenied);
        }

        // 是否查看过
        $hasRecord = ResumeJobReportRecord::checkReportRecord($resumeId, $jobId, $token);
        if (!$hasRecord) {
            exit($permissionDenied);
        }

        // 输出分析报告
        // exit('职位报告');
        $data = BaseJobReport::getReport($jobId, $resumeId, MatchCompleteService::PLATFORM_H5);

        return $this->render('report.html', [
            'data'      => $data,
            'isMiniapp' => HtmlHelper::isMiniapp(),
        ]);
    }
}