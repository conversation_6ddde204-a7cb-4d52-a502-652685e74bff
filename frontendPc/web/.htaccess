#Options +FollowSymLinks
#IndexIgnore */*
#RewriteEngine on
#
## if a directory or a file exists, use it directly
#RewriteCond %{REQUEST_FILENAME} !-f
#RewriteCond %{REQUEST_FILENAME} !-d
#
## otherwise forward it to index.php
#
##RewriteRule .*\.(jpg|jpeg|gif|png|bmp|js|css|swf)$ /index.php/site/index [R,NC]
#RewriteRule . index.php

<IfModule mod_rewrite.c>
Options +FollowSymlinks -Multiviews
RewriteEngine on

RewriteCond %{REQUEST_URI} ^/member/company/
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_FILENAME} !-f
RewriteRule ^member/company/(.*)$ dist/company/index.html [QSA,PT,L]

RewriteCond %{REQUEST_URI} ^/member/person/
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_FILENAME} !-f
RewriteRule ^member/person/(.*)$ dist/person/index.html [QSA,PT,L]

#RewriteCond %{REQUEST_URI} ^/member/
#RewriteCond %{REQUEST_FILENAME} !-d
#RewriteCond %{REQUEST_FILENAME} !-f
#RewriteRule ^member/(.*)$ member/index.html [QSA,PT,L]

RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_FILENAME} !-f
RewriteRule ^(.*)$ index.php?/$1 [QSA,PT,L]
</IfModule>