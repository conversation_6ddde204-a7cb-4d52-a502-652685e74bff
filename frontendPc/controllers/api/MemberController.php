<?php

namespace frontendPc\controllers\api;

use common\base\models\BaseBuriedPointLog;
use common\base\models\BaseCompany;
use common\base\models\BaseCompanyMemberInfo;
use common\base\models\BaseCompanyMemberMessageConfig;
use common\base\models\BaseCompanyMemberWxBind;
use common\base\models\BaseMember;
use common\base\models\BaseMemberLoginForm;
use common\base\models\BaseResume;
use common\helpers\DebugHelper;
use common\helpers\FormatConverter;
use common\libs\Cache;
use common\libs\Captcha;
use common\libs\EmailQueue;
use common\libs\ImgCaptcha;
use common\libs\JwtAuth;
use common\libs\SmsQueue;
use common\libs\WxMiniApp;
use common\libs\WxPublic;
use common\service\chat\ChatApplication;
use common\service\messageCenter\MessageCenterApplication;
use frontendPc\models\Member;
use frontendPc\models\MemberActionLog;
use frontendPc\models\MemberLoginForm;
use Yii;
use yii\base\Exception;
use yii\web\Response;

class MemberController extends BaseFrontPcApiController
{

    /**
     * 获取登录的验证码
     * @return \yii\console\Response|Response
     */
    public function actionSendMobileLoginCode()
    {
        $mobile     = Yii::$app->request->post('mobile');
        $mobileCode = Yii::$app->request->post('mobileCode');
        $type       = Yii::$app->request->post('type', BaseMember::TYPE_PERSON);
        // 这里先做一个图形校验
        $ticket  = Yii::$app->request->post('ticket');
        $randStr = Yii::$app->request->post('randstr');
        if (!$randStr || !$ticket) {
            return $this->fail('图形验证码验证失败');
        }

        $captcha = new Captcha();
        if (!$captcha->check($ticket, $randStr)) {
            return $this->fail('图形验证码验证失败');
        }

        $loginForm = new MemberLoginForm();

        $loginForm->mobileCode = $mobileCode;
        $loginForm->mobile     = $mobile;
        $loginForm->loginType  = BaseMemberLoginForm::LOGIN_TYPE_MOBILE;
        $loginForm->type       = $type;
        $loginForm->smsType    = SmsQueue::TYPE_LOGIN;

        try {
            $loginForm->sendMobileCode();
            //写入日志
            if ($type == BaseMember::TYPE_PERSON) {
                $logData = [
                    'mobile'     => $mobile,
                    'mobileCode' => $mobileCode,
                ];
                BaseBuriedPointLog::dataLog($logData, BaseBuriedPointLog::ACTION_TYPE_SEND_CODE_DATA);
            }

            return $this->success('验证码已发送，请注意查收');
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 验证登录验证码
     * @return \yii\console\Response|Response
     */
    public function actionValidateMobileLoginCode()
    {
        $mobile     = Yii::$app->request->post('mobile');
        $mobileCode = Yii::$app->request->post('mobileCode') ?: BaseMemberLoginForm::DEFAULT_MOBILE_CODE;
        $code       = Yii::$app->request->post('code');
        $type       = Yii::$app->request->post('type', BaseMember::TYPE_PERSON);
        $redirect   = Yii::$app->request->post('redirect', '/job');
        $isNeedStep = Yii::$app->request->post('isNeedStep', '1');
        $token      = Yii::$app->request->post('token');
        //是否绑定0不是1是绑定，绑定则一定要有token参数

        $loginForm = new MemberLoginForm();

        $loginForm->mobileCode = $mobileCode;
        $loginForm->mobile     = $mobile;
        $loginForm->code       = $code;
        $loginForm->loginType  = BaseMemberLoginForm::LOGIN_TYPE_MOBILE;
        $loginForm->type       = $type;
        //是单位且绑定，那么就要验证token不为空
        if ($type == BaseMember::TYPE_COMPANY && $token) {
            if (empty($token)) {
                return $this->fail('扫码失效，请重新扫码');
            }
            $loginForm->token  = $token;
            $loginForm->isBind = true;
        }

        try {
            $data                = $loginForm->validateMobileCode();
            $data['redirectUrl'] = BaseMemberLoginForm::getRedirectUrl($data['resumeStep'], $redirect, $isNeedStep);
            //写入日志
            if ($type == BaseMember::TYPE_PERSON) {
                $logData = [
                    'mobile'     => $mobile,
                    'mobileCode' => $mobileCode,
                    'code'       => $code,
                ];
                BaseBuriedPointLog::dataLog($logData, BaseBuriedPointLog::ACTION_TYPE_SUBMIT_MOBILE_DATA);
            }

            return $this->success($data);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 验证邮箱格式，验证是否存在
     * @return \yii\console\Response|Response
     */
    public function actionValidateEmail()
    {
        $email = Yii::$app->request->post('email');
        $type  = Yii::$app->request->post('type');

        $loginForm = new MemberLoginForm();

        $loginForm->email     = $email;
        $loginForm->type      = $type;
        $loginForm->emailType = EmailQueue::EMAIL_TYPE_REGISTER;

        if (empty(Yii::$app->user->id)) {
            //登录情况下，不校验了
            // 图形校验
            $ticket  = Yii::$app->request->post('ticket');
            $randStr = Yii::$app->request->post('randstr');
            if (!$randStr || !$ticket) {
                return $this->fail('图形验证码验证失败');
            }

            $captcha = new Captcha();
            if (!$captcha->check($ticket, $randStr)) {
                return $this->fail('图形验证码验证失败');
            }
        }

        try {
            $loginForm->validateEmail();
            //写入日志
            if ($type == BaseMember::TYPE_PERSON) {
                $logData = [
                    'email' => $email,
                ];
                BaseBuriedPointLog::dataLog($logData, BaseBuriedPointLog::ACTION_TYPE_SUBMIT_ACCOUNT_DATA);
            }

            return $this->success();
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 发送邮箱注册验证码
     */
    public function actionSendEmailRegisterCode()
    {
        $email     = Yii::$app->request->post('email');
        $type      = Yii::$app->request->post('type', BaseMember::TYPE_PERSON);
        $emailType = EmailQueue::EMAIL_TYPE_REGISTER;

        $memberLoginForm            = new MemberLoginForm();
        $memberLoginForm->email     = $email;
        $memberLoginForm->type      = $type;
        $memberLoginForm->emailType = $emailType;

        if ($type == BaseMember::TYPE_COMPANY) {
            // 图形校验
            $ticket  = Yii::$app->request->post('ticket');
            $randStr = Yii::$app->request->post('randstr');
            if (!$randStr || !$ticket) {
                return $this->fail('图形验证码验证失败');
            }

            $captcha = new Captcha();
            if (!$captcha->check($ticket, $randStr)) {
                return $this->fail('图形验证码验证失败');
            }
        }

        try {
            $memberLoginForm->validateEmail();
            $memberLoginForm->sendEmailCode();

            return $this->success('验证码已发送，请注意查收');
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 通过邮件激活码激活用户
     * @return \yii\console\Response|Response
     */
    public function actionActivateAccount()
    {
        $code  = Yii::$app->request->post('code');
        $email = Yii::$app->request->post('email');

        $loginForm = new MemberLoginForm();

        $loginForm->loginType = BaseMemberLoginForm::LOGIN_TYPE_EMAIL;
        $loginForm->email     = $email;
        $loginForm->code      = $code;
        $loginForm->type      = BaseMember::TYPE_PERSON;

        $transaction = Yii::$app->db->beginTransaction();
        try {
            $data = $loginForm->validateEmailCode();

            $transaction->commit();

            return $this->success($data);
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 账号密码登录
     * @return \yii\console\Response|Response
     */
    public function actionAccountLogin()
    {
        $account    = Yii::$app->request->post('account');
        $password   = Yii::$app->request->post('password');
        $type       = Yii::$app->request->post('type');
        $redirect   = Yii::$app->request->post('redirect', '/job');
        $isNeedStep = Yii::$app->request->post('isNeedStep', '1');
        $token      = Yii::$app->request->post('token');

        $loginForm = new MemberLoginForm();

        $loginForm->account  = $account;
        $loginForm->password = $password;
        $loginForm->type     = $type;

        // 这里先做一个图形校验
        // $ticket  = Yii::$app->request->post('ticket');
        // $randStr = Yii::$app->request->post('randstr');
        // if (!$randStr || !$ticket) {
        //     return $this->fail('图形验证码验证失败');
        // }
        //
        // $captcha = new Captcha();
        // if (!$captcha->check($ticket, $randStr)) {
        //     return $this->fail('图形验证码验证失败');
        // }

        //是否绑定0不是1是绑定，绑定则一定要有token参数
        if ($type == BaseMember::TYPE_COMPANY && $token) {
            if (empty($token)) {
                return $this->fail('扫码失效，请重新扫码');
            }
            $loginForm->token  = $token;
            $loginForm->isBind = true;
        }

        try {
            $data                = $loginForm->accountLogin();
            $data['redirectUrl'] = BaseMemberLoginForm::getRedirectUrl($data['resumeStep'], $redirect, $isNeedStep);
            //写入日志
            if ($type == BaseMember::TYPE_PERSON) {
                $logData = [
                    'account' => $account,
                ];
                BaseBuriedPointLog::dataLog($logData, BaseBuriedPointLog::ACTION_TYPE_SUBMIT_ACCOUNT_DATA);
            }

            return $this->success($data);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取图片验证码
     * @return Response|\yii\console\Response
     */
    public function actionGetCaptcha()
    {
        $captcha = new ImgCaptcha();
        $info    = $captcha->getCaptcha();

        return $this->success($info);
    }

    /**
     * 验证图片验证码是否正确拼接
     * @return \yii\console\Response|Response
     */
    public function actionCheckCaptcha()
    {
        $token     = Yii::$app->request->post('token');
        $pointJson = Yii::$app->request->post('pointJson');

        $captcha = new ImgCaptcha();

        try {
            $captcha->checkCaptcha($token, $pointJson);

            return $this->success();
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 发送找回密码验证码(短信/邮件)
     */
    public function actionSendChangePasswordCode()
    {
        $mobile     = Yii::$app->request->post('mobile');
        $mobileCode = Yii::$app->request->post('mobileCode');
        $email      = Yii::$app->request->post('email');
        $type       = Yii::$app->request->post('type', BaseMember::TYPE_PERSON);

        $memberLoginForm             = new MemberLoginForm();
        $memberLoginForm->mobile     = $mobile;
        $memberLoginForm->mobileCode = $mobileCode;
        $memberLoginForm->email      = $email;
        $memberLoginForm->type       = $type;

        $transaction = Yii::$app->db->beginTransaction();

        // if (empty(Yii::$app->user->id)) {
        //
        // }

        try {
            if ($mobile) {
                // 手机号的加滑块验证
                $ticket  = Yii::$app->request->post('ticket');
                $randStr = Yii::$app->request->post('randstr');
                if (!$randStr || !$ticket) {
                    return $this->fail('图形验证码验证失败');
                }

                $captcha = new Captcha();
                if (!$captcha->check($ticket, $randStr)) {
                    return $this->fail('图形验证码验证失败');
                }
                $memberLoginForm->smsType = SmsQueue::TYPE_CHANGE_PASSWORD;

                $memberLoginForm->sendMobileCode();
            } elseif ($email) {
                $memberLoginForm->emailType = EmailQueue::EMAIL_TYPE_CHANGE_PASSWORD;
                $memberLoginForm->validateEmail();
                $memberLoginForm->sendEmailCode();
            } else {
                return $this->fail();
            }

            $transaction->commit();

            return $this->success('验证码已发送，请注意查收');
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 找回密码提交
     */
    public function actionChangePassword()
    {
        $password   = Yii::$app->request->post('password');
        $mobile     = Yii::$app->request->post('mobile');
        $mobileCode = Yii::$app->request->post('mobileCode');
        $email      = Yii::$app->request->post('email');
        $code       = Yii::$app->request->post('code');
        $type       = Yii::$app->request->post('type', BaseMember::TYPE_PERSON);

        $memberLoginForm             = new MemberLoginForm();
        $memberLoginForm->password   = $password;
        $memberLoginForm->mobile     = $mobile;
        $memberLoginForm->mobileCode = $mobileCode;
        $memberLoginForm->code       = $code;
        $memberLoginForm->email      = $email;
        $memberLoginForm->type       = $type;

        $transaction = Yii::$app->db->beginTransaction();
        try {
            $memberLoginForm->validateChangePasswordCode();

            $transaction->commit();

            return $this->success('密码已重新设置');
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取会员的信息
     */
    public function actionGetInfo()
    {
        return $this->success(Member::getBaseInfo());
    }

    /**
     * 退出登录
     */
    public function actionLogout()
    {
        Yii::$app->user->logout();

        return $this->success();
    }

    /**
     * 获取用户设置信息
     * @return array|\yii\console\Response|\yii\db\ActiveRecord|Response|null
     */
    public function actionGetSettingInfo()
    {
        $memberId = \Yii::$app->user->id;
        try {
            return $this->success(Member::getSettingInfo($memberId));
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 修改用户名
     * @return \yii\console\Response|Response
     */
    public function actionChangeUsername()
    {
        $username = Yii::$app->request->post('username');
        if (empty($username) || empty($type)) {
            $this->fail('缺失必填参数');
        }
        $memberId = Yii::$app->user->id;
        try {
            Member::changeUsername($memberId, $username);

            return $this->success();
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 发送修改邮箱验证码
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionSendChangeEmailCode()
    {
        $email = Yii::$app->request->post('email');
        $type  = Yii::$app->request->post('type');

        $memberId = Yii::$app->user->id;

        $memberLoginForm            = new MemberLoginForm();
        $memberLoginForm->email     = $email;
        $memberLoginForm->type      = $type;
        $memberLoginForm->emailType = EmailQueue::EMAIL_TYPE_CHANGE_EMAIL;
        $memberLoginForm->memberId  = $memberId;
        try {
            $memberLoginForm->validateEmail();
            $memberLoginForm->sendEmailCode();

            return $this->success('验证码已发送，请注意查收');
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 验证邮箱更改验证码
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionValidateChangeEmailCode()
    {
        $data = Yii::$app->request->post();

        $loginForm = new MemberLoginForm();

        $loginForm->email = $data['email'];
        $loginForm->code  = $data['code'];
        $loginForm->type  = $data['type'];
        try {
            $loginForm->validateChangeEmailCode();

            return $this->success($data);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 修改手机——发送验证身份验证码
     * @return \yii\console\Response|Response
     */
    public function actionSendVerifyMobileCode()
    {
        $data            = \Yii::$app->request->post();
        $member          = Member::findOne(Yii::$app->user->id);
        $memberLoginForm = new BaseMemberLoginForm();

        $memberLoginForm->mobileCode = $member->mobile_code;
        $memberLoginForm->mobile     = $member->mobile;
        $memberLoginForm->smsType    = SmsQueue::TYPE_VERIFY_MOBILE;
        $memberLoginForm->type       = $data['type'];

        // 如果是求职者修改，就要滑块
        // 图形校验
        $ticket  = Yii::$app->request->post('ticket');
        $randStr = Yii::$app->request->post('randstr');
        if (!$randStr || !$ticket) {
            return $this->fail('图形验证码验证失败');
        }

        $captcha = new Captcha();
        if (!$captcha->check($ticket, $randStr)) {
            return $this->fail('图形验证码验证失败');
        }

        try {
            (new BaseMemberLoginForm())->checkResumeCancelRestrictionExits(intval($member->mobile_code), $member->mobile);
            $memberLoginForm->sendMobileCode();

            return $this->success('验证码已发送，请注意查收');
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 修改手机——验证身份验证码是否正确
     * @return \yii\console\Response|Response
     */
    public function actionValidateVerifyMobileCode()
    {
        $data = \Yii::$app->request->post();

        $member          = Member::findOne(Yii::$app->user->id);
        $memberLoginForm = new BaseMemberLoginForm();

        $memberLoginForm->mobileCode = $member->mobile_code;
        $memberLoginForm->mobile     = $member->mobile;
        $memberLoginForm->code       = $data['code'];
        $memberLoginForm->type       = $data['type'];

        try {
            return $this->success($memberLoginForm->validateVerifyMobileCode());
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 发送修改手机号验证码
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionSendChangeMobileCode()
    {
        $data = \Yii::$app->request->post();

        $memberLoginForm = new BaseMemberLoginForm();

        $memberLoginForm->mobile     = $data['mobile'];
        $memberLoginForm->mobileCode = $data['mobileCode'];
        $memberLoginForm->smsType    = SmsQueue::TYPE_CHANGE_MOBILE;
        $memberLoginForm->type       = $data['type'];

        // 加入滑块验证码
        // 图形校验
        $ticket  = Yii::$app->request->post('ticket');
        $randStr = Yii::$app->request->post('randstr');
        if (!$randStr || !$ticket) {
            return $this->fail('图形验证码验证失败');
        }
        $captcha = new Captcha();
        if (!$captcha->check($ticket, $randStr)) {
            return $this->fail('图形验证码验证失败');
        }

        try {
            $memberLoginForm->sendMobileCode();

            return $this->success('验证码已发送，请注意查收');
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 验证修改手机验证码
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionValidateChangeMobileCode()
    {
        $data     = Yii::$app->request->post();
        $memberId = Yii::$app->user->id;
        $authKey  = $data['authKey'];
        if (!$authKey) {
            return $this->fail();
        }

        // 图像验证
        // $ticket  = Yii::$app->request->post('ticket');
        // $randStr = Yii::$app->request->post('randstr');
        // if (!$randStr || !$ticket) {
        //     return $this->fail('图形验证码验证失败');
        // }
        // $captcha = new Captcha();
        // if (!$captcha->check($ticket, $randStr)) {
        //     return $this->fail('图形验证码验证失败');
        // }

        $CacheAuthKey = Cache::get(Cache::PC_MEMBER_MOBILE_BIND_KEY . ':' . $memberId);
        if ($CacheAuthKey != $authKey) {
            return $this->fail('验证失效,请重新验证！');
        }

        $loginForm = new BaseMemberLoginForm();

        $loginForm->mobileCode = $data['mobileCode'] ?: BaseMemberLoginForm::DEFAULT_MOBILE_CODE;
        $loginForm->mobile     = $data['mobile'];
        $loginForm->code       = $data['code'];
        $loginForm->type       = $data['type'];

        try {
            $loginForm->validateChangeMobileCode();

            return $this->success();
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 通过旧密码修改密码
     * @return \yii\console\Response|Response
     */
    public function actionChangePasswordByOldPassword()
    {
        $data = \Yii::$app->request->post();

        $loginForm = new BaseMemberLoginForm();

        $loginForm->password    = $data['oldPassword'];
        $loginForm->newPassword = $data['newPassword'];
        $loginForm->type        = $data['type'];
        $loginForm->memberId    = Yii::$app->user->id;
        try {
            $loginForm->changePasswordByOldPassword();

            return $this->success();
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 发送绑定邮箱验证码
     * @return \yii\console\Response|Response
     */
    public function actionSendBindEmailCode()
    {
        $email = Yii::$app->request->post('email');
        $type  = Yii::$app->request->post('type');

        $memberId = Yii::$app->user->id;

        $memberLoginForm            = new MemberLoginForm();
        $memberLoginForm->email     = $email;
        $memberLoginForm->type      = $type;
        $memberLoginForm->emailType = EmailQueue::EMAIL_TYPE_BIND_EMAIL;
        $memberLoginForm->memberId  = $memberId;
        try {
            $memberLoginForm->validateEmail();
            (new BaseMemberLoginForm())->checkResumeCancelRestrictionExits('', '', $email);
            $memberLoginForm->sendEmailCode();

            return $this->success('验证码已发送，请注意查收');
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 验证绑定邮箱验证码
     * @return \yii\console\Response|Response
     */
    public function actionValidateBindEmailCode()
    {
        $data = Yii::$app->request->post();

        $loginForm = new MemberLoginForm();

        $loginForm->email = $data['email'];
        $loginForm->code  = $data['code'];
        $loginForm->type  = $data['type'];
        try {
            $loginForm->validateBindEmailCode();

            return $this->success($data);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取图形验证码的配置
     * @return \yii\console\Response|Response
     */
    public function actionGetCaptchaConfig()
    {
        $config = Yii::$app->params['tencentCloud'];

        return $this->success(['captchaAppId' => $config['captcha']['CaptchaAppId']]);
    }

    public function actionValidationCaptcha()
    {
        $data    = Yii::$app->request->post();
        $captcha = new Captcha();
        if ($captcha->check($data['ticket'], $data['randstr'])) {
            return $this->success();
        }
    }

    /**
     * 设置密码
     * @return \yii\console\Response|Response
     */
    public function actionSavePassword()
    {
        $password = Yii::$app->request->post('password');

        try {
            Member::SavePassword(Yii::$app->user->id, $password);

            return $this->success();
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 登陆日志
     * @return \yii\console\Response|Response
     */
    public function actionLoginLogList()
    {
        $params = Yii::$app->request->get();

        $params['memberType'] = BaseMember::TYPE_COMPANY;

        return $this->success(MemberActionLog::getLoginLogList($params));
    }

    /**
     * 获取微信登录二维码
     * @return \yii\console\Response|Response
     */
    public function actionGetLoginQrcode()
    {
        $type = Yii::$app->request->get('type', BaseMember::TYPE_PERSON);
        try {
            return $this->success(WxPublic::getInstance($type)
                ->createLoginQrcode());
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 创建小程序登录二维码
     * @return \yii\console\Response|Response
     */
    public function actionGetMiniLoginQrcode()
    {
        try {
            $app  = WxMiniApp::getInstance();
            $info = $app->createLoginQrCode();

            return $this->success($info);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 检查小程序登录二维码状态
     * @return \yii\console\Response|Response
     */
    public function actionCheckMiniLoginQrcode()
    {
        $scene      = Yii::$app->request->post('scene');
        $redirect   = Yii::$app->request->post('redirect', '');
        $isNeedStep = Yii::$app->request->post('isNeedStep', '1');

        try {
            $app    = WxMiniApp::getInstance();
            $result = $app->checkLoginQrCodeStatus($scene);

            if ($result['status'] == WxMiniApp::LOGIN_QRCODE_STATUS_LOGIN) {
                $loginForm                         = new MemberLoginForm();
                $loginForm->memberId               = $result['memberId'];
                $result['userInfo']                = $loginForm->loginByWxScan();
                $result['userInfo']['redirectUrl'] = BaseMemberLoginForm::getRedirectUrl($result['userInfo']['resumeStep'],
                    $redirect, $isNeedStep);
                //这里调用一下微信消息通知,逻辑要改
                $resumeId = BaseMember::getMainId($result['memberId']);
                (new MessageCenterApplication())->wxSignIn($resumeId);

                //写入日志
                BaseBuriedPointLog::dataLog($result, BaseBuriedPointLog::ACTION_TYPE_SCAN);
            }

            return $this->success($result);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 检查公众号登录二维码是否被扫描
     */
    public function actionCheckLoginQrcode()
    {
        $ticket     = Yii::$app->request->post('ticket');
        $token      = Yii::$app->request->post('token');
        $type       = Yii::$app->request->post('type', BaseMember::TYPE_PERSON);
        $redirect   = Yii::$app->request->post('redirect', '');
        $isNeedStep = Yii::$app->request->post('isNeedStep', '1');

        try {
            $result = WxPublic::getInstance($type)
                ->checkLoginQrcode($ticket, $token);

            // 这里直接触发成功
            $loginForm       = new MemberLoginForm();
            $loginForm->type = $type;
            //这里我们区分一下是单位还是求职者
            //根据类型type就好了
            if ($type == BaseMember::TYPE_PERSON) {
                //求职者端
                //登录绑定
                if ($result['status'] == WxPublic::LOGIN_QRCODE_STATUS_BIND) {
                    $memberId                          = $result['memberId'];
                    $loginForm->memberId               = $memberId;
                    $result['userInfo']                = $loginForm->loginByWxScan();
                    $result['userInfo']['redirectUrl'] = BaseMemberLoginForm::getRedirectUrl($result['userInfo']['resumeStep'],
                        $redirect, $isNeedStep);
                    //这里调用一下微信消息通知
                    $cacheKey = Cache::PC_ALL_RESUME_ID_KEY . ':' . $memberId;
                    $resumeId = Cache::get($cacheKey);
                    (new MessageCenterApplication())->wxSignIn($resumeId);

                    //写入日志
                    BaseBuriedPointLog::dataLog($result, BaseBuriedPointLog::ACTION_TYPE_SCAN);

                    return $this->success($result);
                }
                //绑定
                if ($result['status'] == WxPublic::LOGIN_QRCODE_STATUS_WAIT_BIND) {
                    $openid = $result['openid'];
                    unset($result['openid']);
                    $result['sno'] = $loginForm->createWxScanRegisterToken($openid);

                    //写入日志
                    BaseBuriedPointLog::dataLog($result, BaseBuriedPointLog::ACTION_TYPE_SCAN);

                    return $this->success($result);
                }
            } else {
                //单位端
                //登录绑定--直接登录就好了
                if ($result['status'] == WxPublic::LOGIN_QRCODE_STATUS_BIND) {
                    $memberId                          = $result['memberId'];
                    $loginForm->memberId               = $memberId;
                    $result['userInfo']                = $loginForm->loginByWxScan();
                    $result['userInfo']['redirectUrl'] = $redirect;
                    //这里调用一下微信消息通知---先注释掉看看后续
                    (new MessageCenterApplication())->wxSignInCompany($memberId);

                    return $this->success($result);
                }
                //绑定-这里不能直接登录，因为这里openid与账号还没有产生关系
                if ($result['status'] == WxPublic::LOGIN_QRCODE_STATUS_WAIT_BIND) {
                    $openid = $result['openid'];
                    unset($result['openid']);
                    $result['sno'] = $loginForm->createWxScanRegisterToken($openid);

                    return $this->success($result);
                }
            }

            return $this->success($result);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取微信绑定的验证码
     * @return \yii\console\Response|Response
     */
    public function actionSendWxBindMobileCode()
    {
        $mobile     = Yii::$app->request->post('mobile');
        $mobileCode = Yii::$app->request->post('mobileCode');
        $type       = Yii::$app->request->post('type', BaseMember::TYPE_PERSON);
        $token      = Yii::$app->request->post('token');
        // 这里先做一个图形校验
        // $ticket  = Yii::$app->request->post('ticket');
        // $randStr = Yii::$app->request->post('randstr');
        // if (!$randStr || !$ticket) {
        //     return $this->fail('图形验证码验证失败');
        // }
        //
        // $captcha = new Captcha();
        // if (!$captcha->check($ticket, $randStr)) {
        //     return $this->fail('图形验证码验证失败');
        // }
        if (!$token) {
            return $this->fail('token不能为空');
        }

        $loginForm = new MemberLoginForm();

        $loginForm->mobileCode = $mobileCode;
        $loginForm->mobile     = $mobile;
        $loginForm->loginType  = BaseMemberLoginForm::LOGIN_TYPE_WX_SCAN;
        $loginForm->type       = $type;
        $loginForm->smsType    = SmsQueue::TYPE_RESUME_WX_BIND_MOBILE;
        $loginForm->token      = $token;

        try {
            $loginForm->sendMobileCode();

            return $this->success('验证码已发送，请注意查收');
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 验证微信绑定的验证码
     * @return \yii\console\Response|Response
     */
    public function actionValidateWxBindMobileCode()
    {
        $mobile     = Yii::$app->request->post('mobile');
        $mobileCode = Yii::$app->request->post('mobileCode');
        $code       = Yii::$app->request->post('code');
        $type       = Yii::$app->request->post('type', BaseMember::TYPE_PERSON);
        $token      = Yii::$app->request->post('token');
        $redirect   = Yii::$app->request->post('redirect', '/job');
        $isNeedStep = Yii::$app->request->post('isNeedStep', '1');

        $loginForm = new MemberLoginForm();

        $loginForm->mobileCode = $mobileCode;
        $loginForm->mobile     = $mobile;
        $loginForm->code       = $code;
        $loginForm->loginType  = BaseMemberLoginForm::LOGIN_TYPE_MOBILE;
        $loginForm->type       = $type;
        $loginForm->token      = $token;
        $loginForm->redirect   = $redirect;

        try {
            $data = $loginForm->validateWxMobileCode($isNeedStep);

            return $this->success($data);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    public function actionCreateBindQrcode()
    {
        // 这里有两种可能,一种是求职者,一种是单位端
        $type = Yii::$app->user->identity->type;
        try {
            if (!$type) {
                throw new \Exception('用户类型不能为空');
            }
            if ($type == BaseMember::TYPE_PERSON) {
                $mainId = BaseResume::findOneVal(['member_id' => Yii::$app->user->id], 'id');
            } else {
                $mainId = Yii::$app->user->id;
                // 找到绑定信息
                // $wxBindModel = BaseCompanyMemberInfo::findOne(['company_member_id' => $mainId]);
                //$mainId = BaseCompany::findOneVal(['member_id' => Yii::$app->user->id], 'id');
            }

            return $this->success(WxPublic::getInstance($type)
                ->createBindQrCode($mainId));
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    public function actionCheckBindQrcode()
    {
        $type = Yii::$app->user->identity->type;

        $ticket = Yii::$app->request->post('ticket');
        $token  = Yii::$app->request->post('token');

        if (!$ticket || !$token) {
            return $this->fail('参数错误');
        }

        try {
            if ($type == BaseMember::TYPE_PERSON) {
                $mainId = BaseResume::findOneVal(['member_id' => Yii::$app->user->id], 'id');
            } else {
                $mainId = Yii::$app->user->id;
                // 这里检查一下,如果这个memberId绑定过了,就直接回成功就可以了
                if (BaseCompanyMemberWxBind::find()
                    ->where(['company_member_id' => $mainId])
                    ->exists()) {
                    return $this->success(['status' => WxPublic::BIND_QRCODE_STATUS_SCAN_WAIT_BIND]);
                }
                $mainId = BaseCompany::findOneVal(['member_id' => Yii::$app->user->id], 'id');
            }
            $result = WxPublic::getInstance($type)
                ->checkBindQrcode($ticket, $token);

            return $this->success($result);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 解绑弹窗内容
     * @return \yii\console\Response|Response
     */
    public function actionGetCancelPopInfo()
    {
        $data = [
            'title'      => '解除绑定',
            'content'    => '解绑后，您将不能使用微信和小程序快速登录，且无法在微信服务号接收投递反馈等重要信息，确定解绑吗？',
            'buttonList' => [
                [
                    'type'  => 'default',
                    'text'  => '确定',
                    'value' => 'confirm',
                ],
                [
                    'type'  => 'primary',
                    'text'  => '考虑一下',
                    'value' => 'cancel',
                ],
            ],
        ];

        return $this->success($data);
    }

    /**
     * 解绑微信绑定
     */
    public function actionCancelWxBind()
    {
        $type = Yii::$app->user->identity->type;
        if ($type == BaseMember::TYPE_PERSON) {
            $mainId = BaseResume::findOneVal(['member_id' => Yii::$app->user->id], 'id');
        } else {
            $mainId = Yii::$app->user->id;
            //$mainId = BaseCompany::findOneVal(['member_id' => Yii::$app->user->id], 'id');
        }

        if (!$mainId) {
            return $this->fail('用户暂未登录');
        }

        try {
            $result = WxPublic::getInstance($type)
                ->cancelWxBind($mainId);

            return $this->success($result);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取当前登录账号的消息配置信息
     * @return \yii\console\Response|Response
     */
    public function actionGetMessageConfigInfo()
    {
        $memberId = Yii::$app->user->id;
        try {
            return $this->success(BaseCompanyMemberMessageConfig::findOne(['member_id' => $memberId]));
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 修改单位账号消息配置-根据参数key修改配置
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionEditMessageConfig()
    {
        try {
            //获取参数
            $params = Yii::$app->request->post();
            //转换成下划线
            $params   = FormatConverter::convertHump($params);
            $memberId = Yii::$app->user->id;
            $info     = BaseCompanyMemberMessageConfig::findOne(['member_id' => $memberId]);
            if ($params['message_id'] != $info->id) {
                return $this->fail('参数错误');
            }

            $res = BaseCompanyMemberMessageConfig::editMessageConfig($params);

            return $this->success($res);
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取当前登录账号绑定码还是关注码
     * @return \yii\console\Response|Response
     */
    public function actionGetQrcode()
    {
        $memberId = Yii::$app->user->id;
        $type     = Yii::$app->user->identity->type;
        try {
            return $this->success(WxPublic::getInstance($type)
                ->getBindQrCode($memberId));
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    public function actionChatDownload()
    {
        $memberId = Yii::$app->user->id;

        $messageId = Yii::$app->request->get('messageId');

        // 找这条消息是否超过了30天

        try {
            $app = ChatApplication::getInstance();

            // 这里直接是文件流了
            return $app->download($memberId, $messageId);
        } catch (\Exception $e) {
            // 这里是直接访问链接的,所以报错是需要调用js的window的alter来提示,点击确定关闭当前页面
            echo "<script>alert('{$e->getMessage()}')</script>";
            echo "<script>window.close();</script>";
        }
    }

    /**
     * 获取聊天室信息
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionGetChatInfo()
    {
        $chatId   = Yii::$app->request->get('chatId');
        $memberId = Yii::$app->user->id;
        try {
            $app  = ChatApplication::getInstance();
            $data = $app->getChatInfo($chatId, $memberId);

            return $this->success($data);
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取历史聊天记录
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionGetHistoryList()
    {
        $chatId    = Yii::$app->request->get('chatId');
        $messageId = Yii::$app->request->get('messageId');
        $pageLimit = Yii::$app->request->get('pageLimit');
        $memberId  = Yii::$app->user->id;
        try {
            $app  = ChatApplication::getInstance();
            $data = $app->getHistoryList($chatId, $memberId, $messageId, $pageLimit);

            return $this->success($data);
        } catch (\yii\db\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 修改账号是否开启直聊开关--暂时不实现
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionEditMemberIsChat()
    {
        try {
            $params   = Yii::$app->request->post();
            $memberId = Yii::$app->user->id;
            if (!$memberId) {
                return $this->fail('请先登录！');
            }
            $params['memberId'] = $memberId;

            return $this->success();
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 设置聊天室置顶
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionSetTop()
    {
        $transaction = Yii::$app->db->beginTransaction();
        $chatId      = Yii::$app->request->post('chatId');
        $memberId    = Yii::$app->user->id;
        try {
            $app = ChatApplication::getInstance();
            $app->setTop($chatId, $memberId);
            $transaction->commit();

            return $this->success();
        } catch (\yii\db\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 删除房间
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionDelRoom()
    {
        $transaction = Yii::$app->db->beginTransaction();
        $chatId      = Yii::$app->request->post('chatId');
        $memberId    = Yii::$app->user->id;
        try {
            $app = ChatApplication::getInstance();
            $app->delRoom($chatId, $memberId);
            $transaction->commit();

            return $this->success();
        } catch (\yii\db\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取聊天列表
     * @return \yii\console\Response|Response
     */
    public function actionGetChatList()
    {
        // 这里有些查询很多的，把内存开到4g
        ini_set('memory_limit', '4096M');

        $memberId   = Yii::$app->user->id;
        $keyword    = Yii::$app->request->get('keyword') ?: '';
        $readStatus = Yii::$app->request->get('readStatus') ?: '';
        $type       = Yii::$app->request->post('type') ?: '';
        $memberRole = BaseMember::findOneVal(['id' => $memberId], 'type');
        $page       = Yii::$app->request->get('page', 1);
        try {
            $app = ChatApplication::getInstance();
            //根据用户类型获取聊天列表
            if ($memberRole == BaseMember::TYPE_COMPANY) {
                $data = $app->getCompanyChatList($memberId, $type, $keyword, $readStatus, $page);
            } elseif ($memberRole == BaseMember::TYPE_PERSON) {
                $data = $app->getPersonChatList($memberId, $keyword, $readStatus);
            }

            return $this->success($data);
        } catch (\Exception $e) {
            //如果有错误数组，返回数组消息
            $errorInfo = $e->getMessage();

            return $this->fail($errorInfo);
        }
    }

    // 重载token
    public function actionRefreshToken()
    {
        $memberId = Yii::$app->user->id;

        $jwt = new JwtAuth();

        $jwt->createToken($memberId);

        return $this->success([
            'token'      => $jwt->token,
            'expireTime' => $jwt->expireTime,
        ]);
    }
}