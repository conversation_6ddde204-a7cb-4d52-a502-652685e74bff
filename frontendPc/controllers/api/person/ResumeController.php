<?php

namespace frontendPc\controllers\api\person;

use common\base\models\BaseCompany;
use common\base\models\BaseCompanyResumePvTotal;
use common\base\models\BaseHomePosition;
use common\base\models\BaseJob;
use common\base\models\BaseMember;
use common\base\models\BaseMemberLoginForm;
use common\base\models\BaseResume;
use common\base\models\BaseResumeCancelLog;
use common\base\models\BaseResumeAttachment;
use common\base\models\BaseResumeComplete;
use common\base\models\BaseResumeEquityPackage;
use common\base\models\BaseResumeEquityPackageSetting;
use common\base\models\BaseResumeEquitySetting;
use common\base\models\BaseResumeRefresh;
use common\base\models\BaseResumeSetting;
use common\base\models\BaseResumeTemplateConfig;
use common\base\models\BaseResumeTemplateDownloadRecord;
use common\base\models\BaseShowcase;
use common\base\models\BaseUploadForm;
use common\helpers\FileHelper;
use common\helpers\IpHelper;
use common\helpers\StringHelper;
use common\libs\Captcha;
use common\libs\ResumeHandle;
use common\service\CommonService;
use common\service\match\PersonToJobNewService;
use common\service\match\PersonToJobService;
use common\service\memberCancel\ResumeCancelService;
use common\service\memberCancel\ResumeCancelSnapshotService;
use common\service\memberCancel\ResumeCancelDataCleanService;
use common\service\resume\ResumeService;
use common\service\resume\ResumeTopService;
use frontendPc\models\Company;
use frontendPc\models\Member;
use frontendPc\models\Resume;
use frontendPc\models\ResumeAttachment;
use frontendPc\models\ResumeComplete;
use frontendPc\models\ResumeEquity;
use frontendPc\models\ResumeEquitySetting;
use frontendPc\models\ResumeSetting;
use frontendPc\models\ShieldCompany;
use Mpdf\MpdfException;
use Yii;
use yii\base\Exception;

class ResumeController extends BaseFrontPcApiPersonController
{

    /**
     * 获取用户基本信息
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionGetUserBaseInfo()
    {
        $service = new ResumeService();

        $data = $service->setPlatform(CommonService::PLATFORM_WEB_PERSON)
            ->setOparetion(ResumeService::OPERATION_TYPE_GET_RESUME_STEP_ONE)
            ->init()
            ->run();

        return $this->success($data);
    }

    /**
     * 获取用户求职动态
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionGetJobTrend()
    {
        $memberId = Yii::$app->user->id;

        return $this->success(Resume::getJobTrend($memberId));
    }

    /**
     * 保存用户基本信息
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionSaveUserBaseInfo()
    {
        $transaction = \Yii::$app->db->beginTransaction();
        try {
            $service = new ResumeService();
            $service->setPlatform(CommonService::PLATFORM_WEB_PERSON)
                ->setOparetion(ResumeService::OPERATION_TYPE_SAVE_RESUME_STEP_ONE)
                ->init()
                ->run();

            $transaction->commit();

            return $this->success();
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取简历第三步下拉框字典
     */
    public function actionGetStepThreeParams()
    {
        $service = new ResumeService();
        $info    = $service->setPlatform(CommonService::PLATFORM_WEB_PERSON)
            ->setOparetion(ResumeService::OPERATION_TYPE_GET_RESUME_STEP_THREE_DROP_LIST)
            ->init()
            ->run();

        return $this->success($info);
    }

    /**
     * 获取简历第二步资料
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionGetStepTwoInfo()
    {
        $service = new ResumeService();
        $info    = $service->setPlatform(CommonService::PLATFORM_WEB_PERSON)
            ->setOparetion(ResumeService::OPERATION_TYPE_GET_WEB_RESUME_STEP_TWO)
            ->init()
            ->run();

        return $this->success($info);
    }

    /**
     * 保存简历第二步资料
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionSaveStepTwoInfo()
    {
        $transaction = \Yii::$app->db->beginTransaction();
        try {
            $service = new ResumeService();
            $service->setPlatform(CommonService::PLATFORM_WEB_PERSON)
                ->setOparetion(ResumeService::OPERATION_TYPE_SAVE_WEB_RESUME_STEP_TWO)
                ->init()
                ->run();
            $transaction->commit();

            return $this->success();
        } catch (Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取简历第三步内容
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionGetStepThreeInfo()
    {
        $service = new ResumeService();
        $info    = $service->setPlatform(CommonService::PLATFORM_WEB_PERSON)
            ->setOparetion(ResumeService::OPERATION_TYPE_GET_WEB_RESUME_STEP_THREE)
            ->init()
            ->run();

        return $this->success($info);
    }

    /**
     * 保存简历第三步内容
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionSaveStepThreeInfo()
    {
        $transaction = \Yii::$app->db->beginTransaction();
        try {
            $service = new ResumeService();
            $service->setPlatform(CommonService::PLATFORM_WEB_PERSON)
                ->setOparetion(ResumeService::OPERATION_TYPE_SAVE_WEB_RESUME_STEP_THREE)
                ->init()
                ->run();
            $transaction->commit();

            return $this->success();
        } catch (Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取简历第四步信息
     * @return \yii\console\Response|\yii\web\Response
     * @throws \Exception
     */
    public function actionGetStepFourInfo()
    {
        return $this->success(Resume::getStepFourInfo());
    }

    /**
     * 保存简历第四步信息
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionSaveStepFourInfo()
    {
        $data = \Yii::$app->request->post();

        $transaction = \Yii::$app->db->beginTransaction();
        try {
            Resume::saveStepFourInfo($data);

            $transaction->commit();

            return $this->success();
        } catch (Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取简历完成度百分比
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionGetCompletePercent()
    {
        try {
            $memberId = \Yii::$app->user->id;
            $percent  = BaseResume::getComplete($memberId);
            //获取简历显示完成百分比（解锁所有功能百分比，只做展示，没有现实意义）
            $viewCompletePercent = Yii::$app->params['completeResumePercent'];
            // 热门模块,这个是读取配置文件的
            $hotRecommendedModule = Yii::$app->params['hotRecommendedModule'];

            return $this->success([
                'percent'              => $percent,
                'completePercent'      => $viewCompletePercent,
                'hotRecommendedModule' => $hotRecommendedModule,
            ]);
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    //    /**
    //     * 更新用户简历状态
    //     * @return \yii\console\Response|\yii\web\Response
    //     */
    //    public function actionUpdateUserResumeStatus()
    //    {
    //        try {
    //            Resume::updateUserRealStatus();
    //
    //            return $this->success();
    //        } catch (Exception $e) {
    //            return $this->fail($e->getMessage());
    //        }
    //    }

    /**
     * 编辑简历用户信息
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionSaveUserInfo()
    {
        $data = \Yii::$app->request->post();

        try {
            Resume::saveUserInfo($data);

            return $this->success(BaseResume::getUserInfo(\Yii::$app->user->id));
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取用户简历编辑页面信息
     * @throws \Exception
     */
    public function actionGetInfo()
    {
        $memberId = \Yii::$app->user->getId();

        $data = BaseResume::getInfo($memberId);

        // 在个人中心里面会有一个信息，交户籍国籍，要是这个值是0，需要重置为''，不然会报错
        if ($data['userInfo']['householdRegisterId'] === '0') {
            $data['userInfo']['householdRegisterId'] = '';
        }

        //处理一下手机号码
        $data['userInfo']['mobile']     = StringHelper::strMobileDes($data['userInfo']['mobile']);
        $data['userInfo']['fullMobile'] = StringHelper::strMobileDes($data['userInfo']['fullMobile']);

        return $this->success($data);
    }

    /**
     * 获取用户简历编辑页面信息
     * @throws \Exception
     */
    public function actionGetUpdateInfo()
    {
        $resumeId = Yii::$app->request->get('resumeId');
        if ($resumeId <= 0) {
            return $this->fail('参数错误');
        }

        return $this->success(BaseResume::getUpdateInfo($resumeId));
    }

    /**
     * 上传附件简历
     * @return \yii\console\Response|\yii\web\Response
     */
    //    public function actionUpload()
    //    {
    //        $model = new BaseUploadForm();
    //
    //        $transaction = \Yii::$app->db->beginTransaction();
    //        try {
    //            $transaction->commit();
    //            $path = 'resume';
    //            $data = $model->upload('file', $path);
    //
    //            //保存附件简历记录
    //            $resumeAttachmentId = ResumeAttachment::saveAttachment($data);
    //
    //            return $this->success([
    //                'id'                 => (string)$data['id'],
    //                'resumeAttachmentId' => (int)$resumeAttachmentId,
    //                'url'                => $data['path'],
    //                'fullUrl'            => FileHelper::getFullUrl($data['path']),
    //                'name'               => $data['name'],
    //            ]);
    //        } catch (\Exception $e) {
    //            $transaction->rollBack();
    //
    //            return $this->result($e->getMessage());
    //        }
    //    }

    public function actionUpload()
    {
        $transaction = \Yii::$app->db->beginTransaction();
        try {
            //            ResumeAttachment::checkLimit(Yii::$app->user->id);
            //
            //            $model = new BaseUploadForm();
            //
            //            $data = $model->resume(Yii::$app->user->id);
            //
            //            //保存附件简历记录
            //            $token = ResumeAttachment::saveAttachment($data)['token'];
            //
            //            $transaction->commit();
            //
            //            return $this->success([
            //                'name'  => $data['name'],
            //                'token' => $token,
            //            ]);
            $model = new BaseUploadForm();
            $res   = $model->resumeNew([
                'memberId' => Yii::$app->user->id,
                'resumeId' => $this->getResumeId(),
            ]);
            $transaction->commit();

            return $this->success($res);
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->result($e->getMessage());
        }
    }

    /**
     * 下载个人简历
     * @return void|\yii\console\Response|\yii\web\Response
     */
    public function actionDownloadAttachment()
    {
        $token = Yii::$app->request->get('token');

        try {
            //            ResumeAttachment::downloadMine(Yii::$app->user->id, $token);

            return ResumeHandle::previewOrDownload($token, ResumeHandle::TYPE_FILE_DOWNLOAD,
                ResumeHandle::DOWNLOAD_URL);
        } catch (Exception $e) {
            return $this->result($e->getMessage());
        }
    }

    /**
     * 获取附件简历列表
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionGetResumeAttachmentList()
    {
        $memberId = \Yii::$app->user->id;
        try {
            $list = ResumeAttachment::getInfoList($memberId);

            return $this->success($list);
        } catch (Exception $e) {
            return $this->result($e->getMessage());
        }
    }

    /**
     * 新增附件简历备注
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionSaveResumeAttachmentNote()
    {
        $data     = \Yii::$app->request->post();
        $memberId = \Yii::$app->user->id;
        //判断附件简历是否属于用户
        $resumeAttachmentInfo = ResumeAttachment::find()
            ->where(['token' => $data['token']])
            ->select('member_id')
            ->asArray()
            ->one();
        if (empty($data['token'])) {
            return $this->fail('缺失必填参数');
        }
        if (empty($resumeAttachmentInfo)) {
            return $this->fail('附件简历不存在');
        }
        if ($resumeAttachmentInfo['member_id'] != $memberId) {
            return $this->fail('附件简历与用户不对应');
        }
        try {
            ResumeAttachment::saveNote($data);

            return $this->success();
        } catch (Exception $e) {
            return $this->result($e->getMessage());
        }
    }

    /**
     * 修改附件简历名称
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionChangeResumeAttachmentName()
    {
        $memberId = \Yii::$app->user->id;
        $data     = \Yii::$app->request->post();

        if (empty($data['token']) || empty($data['name'])) {
            return $this->fail('缺失必填参数');
        }

        try {
            ResumeAttachment::changeFileName($data['token'], $data['name'], $memberId, true);

            return $this->success();
        } catch (Exception $e) {
            return $this->result($e->getMessage());
        }
    }

    /**
     * 删除附件简历
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionDelResumeAttachment()
    {
        $token    = \Yii::$app->request->post('token');
        $memberId = \Yii::$app->user->id;
        //获取附件简历信息
        $resumeAttachmentInfo = ResumeAttachment::find()
            ->where(['token' => $token])
            ->select([
                'status',
                'member_id',
            ])
            ->asArray()
            ->one();
        if (empty($resumeAttachmentInfo)) {
            return $this->fail('附件简历不存在');
        }
        if ($resumeAttachmentInfo['status'] == ResumeAttachment::STATUS_DELETE) {
            return $this->fail('附件简历状态错误');
        }
        if ($resumeAttachmentInfo['member_id'] != $memberId) {
            return $this->fail('附件简历不属于当前用户');
        }
        try {
            ResumeAttachment::del($token, $memberId);

            return $this->success();
        } catch (Exception $e) {
            return $this->result($e->getMessage());
        }
    }

    /**
     * 修改简历显示状态
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionChangeShowStatus()
    {
        $memberId = \Yii::$app->user->id;
        $resumeId = BaseResume::findOneVal(['member_id' => $memberId], 'id');

        try {
            ResumeSetting::changeHideStatus($resumeId);

            return $this->success();
        } catch (Exception $e) {
            return $this->result($e->getMessage());
        }
    }

    /**
     * 修改个人优势
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionSaveAdvantage()
    {
        $advantage = \Yii::$app->request->post('advantage');
        try {
            if (!empty($advantage) && mb_strlen($advantage, 'UFT-8') > 500) {
                throw new Exception('参数错误');
            }
            Resume::saveAdvantage($advantage);

            return $this->success();
        } catch (Exception $e) {
            return $this->result($e->getMessage());
        }
    }

    /**
     * 保存求职意向的到岗时间和求职状态
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionSaveIntentionInfo()
    {
        $data = \Yii::$app->request->post();

        try {
            Resume::saveIntentionInfo($data);

            return $this->success(BaseResume::getUserInfo(\Yii::$app->user->id));
        } catch (Exception $e) {
            return $this->result($e->getMessage());
        }
    }

    /**
     * 获取隐私设置
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionGetPrivacyInfo()
    {
        $memberId = \Yii::$app->user->id;
        try {
            $info = ResumeSetting::getPrivacyInfo($memberId);

            return $this->success($info);
        } catch (Exception $e) {
            return $this->result($e->getMessage());
        }
    }

    /**
     * 获取隐私设置
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionGetPrivacyCard()
    {
        $memberId = \Yii::$app->user->id;
        try {
            $info = ResumeSetting::getPrivacyInfo($memberId);

            // 其实和上面差不多,但是这边只需要两个参数
            $data = [
                'isHideResume'       => $info['isHideResume'],
                'shieldCompanyCount' => (string)count($info['shieldCompanyList']),
            ];

            return $this->success($data);
        } catch (Exception $e) {
            return $this->result($e->getMessage());
        }
    }

    /**
     * 屏蔽/取消屏蔽单位
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionSaveShieldCompanyInfo()
    {
        $companyIds = Yii::$app->request->post('companyIds');
        $companyArr = explode(',', $companyIds);
        $memberId   = Yii::$app->user->id;

        try {
            if (!$memberId) {
                return $this->fail('用户登录状态错误');
            }
            $resumeId = Resume::findOneVal(['member_id' => $memberId], 'id');
            if (!$resumeId) {
                return $this->fail('简历信息错误');
            }
            if (empty($companyArr)) {
                return $this->fail('请求参数错误');
            }
            foreach ($companyArr as $companyId) {
                //判断职位是否存在
                $companyInfo = Company::find()
                    ->where(['id' => $companyId])
                    ->select(['status'])
                    ->asArray()
                    ->one();
                if (!$companyInfo) {
                    return $this->fail('单位不存在');
                }
                if ($companyInfo['status'] != BaseCompany::STATUS_ACTIVE) {
                    return $this->fail('单位状态错误');
                }
                ShieldCompany::saveInfo($companyId, $resumeId);
            }

            return $this->success();
        } catch (Exception $e) {
            return $this->result($e->getMessage());
        }
    }

    /**
     * 获取我还没屏蔽的单位
     */
    public function actionGetNotShieldCompanyList()
    {
        $resumeId = $this->getResumeId();

        $list = ShieldCompany::getNotCompanyList($resumeId, Yii::$app->request->get());

        return $this->success(['list' => $list]);
    }

    /**
     * 修改匿名显示状态
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionChangeAnonymousStatus()
    {
        $memberId = \Yii::$app->user->id;
        try {
            ResumeSetting::changeAnonymousStatus($memberId);

            return $this->success();
        } catch (Exception $e) {
            return $this->result($e->getMessage());
        }
    }

    /**
     * 修改简历代投状态
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionChangeProxyDeliverStatus()
    {
        $memberId = \Yii::$app->user->id;
        try {
            ResumeSetting::changeProxyDeliverStatus($memberId);

            return $this->success();
        } catch (Exception $e) {
            return $this->result($e->getMessage());
        }
    }

    /**
     * 获取消息设置配置信息
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionGetMessageSettingInfo()
    {
        $memberId = \Yii::$app->user->id;
        try {
            $info = ResumeSetting::getMessageSettingInfo($memberId);

            return $this->success($info);
        } catch (Exception $e) {
            return $this->result($e->getMessage());
        }
    }

    /**
     * 修改求职反馈状态
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionChangeJobFeedbackStatus()
    {
        $memberId = \Yii::$app->user->id;
        try {
            ResumeSetting::changeJobFeedbackStatus($memberId);

            return $this->success();
        } catch (Exception $e) {
            return $this->result($e->getMessage());
        }
    }

    /**
     * 修改系统通知状态
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionChangeSystemMessageStatus()
    {
        $memberId = \Yii::$app->user->id;
        try {
            ResumeSetting::changeSystemMessageStatus($memberId);

            return $this->success();
        } catch (Exception $e) {
            return $this->result($e->getMessage());
        }
    }

    /**
     * 修改待办通知状态
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionChangeTodoNoticeStatus()
    {
        $memberId = \Yii::$app->user->id;
        try {
            ResumeSetting::changeTodoNoticeStatus($memberId);

            return $this->success();
        } catch (Exception $e) {
            return $this->result($e->getMessage());
        }
    }

    /**
     * 修改职位邀请通知状态
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionChangeJobInviteStatus()
    {
        $memberId = \Yii::$app->user->id;
        try {
            ResumeSetting::changeJobInviteStatus($memberId);

            return $this->success();
        } catch (Exception $e) {
            return $this->result($e->getMessage());
        }
    }

    /**
     * 修改单位看过我通知状态
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionChangeCompanyViewMeStatus()
    {
        $memberId = \Yii::$app->user->id;
        try {
            ResumeSetting::changeCompanyViewMeStatus($memberId);

            return $this->success();
        } catch (Exception $e) {
            return $this->result($e->getMessage());
        }
    }

    /**
     * 预览自己简历
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionPreview()
    {
        $userId   = Yii::$app->user->id;
        $resumeId = Member::getMainId();
        // 首先根据

        if (!$resumeId) {
            return $this->fail('参数错误');
        }

        try {
            $model = new ResumeHandle();
            $model->setOperator($userId, ResumeHandle::OPERATOR_TYPE_SELF)
                ->setData($resumeId)
                ->preview();

            exit;

            return $this->success();
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 简历下载
     * @return \yii\console\Response|\yii\web\Response
     * @throws Exception
     */
    public function actionDownload()
    {
        $userId   = Yii::$app->user->id;
        $resumeId = Member::getMainId();

        if (!$resumeId) {
            return $this->fail();
        }
        $transaction = Yii::$app->db->beginTransaction();
        try {
            $resumeDown = new ResumeHandle();
            $resumeDown->setOperator($userId, ResumeHandle::OPERATOR_TYPE_SELF)
                ->setResumeTemplateType(ResumeHandle::RESUME_TEMPLATE_TYPE, '')
                ->setData($resumeId)
                ->download();

            $transaction->commit();
            exit();
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取简历完成度
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionGetResumeComplete()
    {
        $memberId = Yii::$app->user->id;
        try {
            return $this->success(['resumeCompletePercent' => (int)BaseResume::getComplete($memberId)]);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取个人主页推荐职位列表
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionRecommendJobList()
    {
        try {
            // 这里开始临时被修改了 需要的时候就更换注释部分  ---------------------

            // $service = new PersonToJobService();
            // $params  = [
            //     'intentionId' => Yii::$app->request->get('intentionId'),
            //     'page'        => Yii::$app->request->get('page'),
            //     'type'        => Yii::$app->request->get('type'),
            //     'pageSize'    => Yii::$app->request->get('pageSize'),
            // ];
            // $data    = $service->setPlatform(CommonService::PLATFORM_WEB_PERSON)
            //     ->init($params)
            //     ->run();

            $service = new PersonToJobNewService();
            $params  = [
                'intentionId' => Yii::$app->request->get('intentionId'),
                'page'        => Yii::$app->request->get('page'),
                'type'        => Yii::$app->request->get('type'),
                'pageSize'    => Yii::$app->request->get('pageSize'),
                'platform'    => CommonService::PLATFORM_WEB_PERSON,
                'memberId'    => Yii::$app->user->id,
            ];
            $data    = $service->getList($params);
            //获取职位列表广告插入位置
            if ($params['page'] == 1 || !$params['page']) {
                $data['showcaseInfo'] = BaseJob::getListShowcaseInfo(count($data['list']), Yii::$app->user->id,
                    BaseJob::VIP_SHOWCASE_POSITION_TYPE_PC_PERSON);
            }

            // 这里开始临时被修改了 ---------------------

            return $this->success($data);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 简历刷新按钮
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionRefresh()
    {
        $resumeId = $this->getResumeId();
        $data     = BaseResumeRefresh::create($resumeId, BaseResumeRefresh::PLATFORM_WEB_PERSON);

        return $this->success($data);
    }

    public function actionGetHomeCard()
    {
        $resumeId = $this->getResumeId();
        $data     = Resume::getHomeCard($resumeId);

        return $this->success($data);
    }

    public function actionClickGlobalPopover()
    {
        $resumeId = $this->getResumeId();
        BaseResume::clickGlobalPopover($resumeId);

        return $this->success();
    }

    /**
     * 获取简历模板配置
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionGetResumeTemplateConfig()
    {
        $data = BaseResumeTemplateConfig::getListNotLimit();

        //处理数据
        foreach ($data as &$value) {
            $value['template_image'] = FileHelper::getFullUrl($value['template_image']);
            unset($value['fileInfo'], $value['file_id'], $value['id']);
        }

        return $this->success($data);
    }

    /**
     * 获取简历模板PDF下载
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionResumeTemplatePdfDownload()
    {
        try {
            $code = Yii::$app->request->get('code');
            if (!$code) {
                throw new Exception('参数错误');
            }
            $resume_id = $this->getResumeId();

            BaseResumeTemplateConfig::resumeTemplatePdfDownload($resume_id, $code,
                BaseResumeTemplateDownloadRecord::PLATFORM_H5);
            exit;
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取简历模板PDF预览
     */
    public function actionResumeTemplatePdfPreview()
    {
        try {
            $code = Yii::$app->request->get('code');
            if (!$code) {
                throw new Exception('参数错误');
            }
            //验证code
            $config = BaseResumeTemplateConfig::findOne([
                'code'      => $code,
                'is_delete' => BaseResumeTemplateConfig::IS_DELETE_NO,
                'is_show'   => BaseResumeTemplateConfig::IS_SHOW_YES,
            ]);
            if (!$config) {
                throw new Exception('模板已经下架');
            }
            //验证一下是否是会员模板
            $resume_id = $this->getResumeId();
            //            if ($config->is_vip == BaseResumeTemplateConfig::IS_VIP_YES && ResumeEquity::checkEquity($resume_id,
            //                    ResumeEquitySetting::ID_RESUME_TEMPLATE) === false) {
            //                throw new Exception('您没有简历模板VIP权益，请先开通模板权益');
            //            }
            //预览
            BaseResumeTemplateConfig::previewPdf($resume_id, $code);

            exit;
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 模板权益
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionResumeTemplateEquity()
    {
        $code = Yii::$app->request->get('code');
        if (!$code) {
            return $this->fail('参数错误');
        }
        //验证code
        $config = BaseResumeTemplateConfig::findOne([
            'code'      => $code,
            'is_delete' => BaseResumeTemplateConfig::IS_DELETE_NO,
            'is_show'   => BaseResumeTemplateConfig::IS_SHOW_YES,
        ]);
        if (!$config) {
            throw new Exception('模板已经下架');
        }
        //验证一下是否是会员模板
        $is_vip   = false;
        $resumeId = $this->getResumeId();
        if ($config->is_vip == BaseResumeTemplateConfig::IS_VIP_YES && ResumeEquity::checkEquity($resumeId,
                ResumeEquitySetting::ID_RESUME_TEMPLATE) === false) {
            $is_vip = true;
        }

        return $this->success(['is_vip' => $is_vip]);
    }

    /**
     * 获取简历模板DOC下载
     */
    public function actionResumeTemplateDocDownload()
    {
        try {
            $code = Yii::$app->request->get('code');
            if (!$code) {
                throw new Exception('参数错误');
            }
            //验证code
            $config = BaseResumeTemplateConfig::findOne([
                'code'      => $code,
                'is_delete' => BaseResumeTemplateConfig::IS_DELETE_NO,
                'is_show'   => BaseResumeTemplateConfig::IS_SHOW_YES,
            ]);
            if (!$config) {
                throw new Exception('模板已经下架');
            }
            //验证一下是否是会员模板
            $resume_id = $this->getResumeId();
            if ($config->is_vip == BaseResumeTemplateConfig::IS_VIP_YES && ResumeEquity::checkEquity($resume_id,
                    ResumeEquitySetting::ID_RESUME_TEMPLATE) === false) {
                throw new Exception('您没有简历模板VIP权益，请先开通模板权益');
            }
            //写一个日志
            $data = [
                'resume_id'   => $resume_id,
                'template_id' => $config->id,
                'type'        => BaseResumeTemplateDownloadRecord::TYPE_DOC,
                'platform'    => BaseResumeTemplateDownloadRecord::PLATFORM_PC,
            ];
            BaseResumeTemplateDownloadRecord::writeRecord($data);
            //加下载次数
            $config->updateCounters(['doc_download_number' => 1]);
            //下载
            $download_doc = BaseResumeTemplateConfig::downloadDoc($code);

            return $this->success(['download_doc' => $download_doc]);
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 返回简历模板页的广告位
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionGetShowcaseList()
    {
        //获取广告列表
        $key = 'jianliyulanye_ad';
        //获取position_id
        $position_id = BaseHomePosition::findOneval(['number' => $key], 'id');
        $data        = BaseShowcase::getByPositionConfig($position_id, $key);

        return $this->success($data);
    }

    /**
     * 返回简历中心页的广告位
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionGetResumeCenterShowcase()
    {
        //获取广告列表
        $key = 'jianlizhongxinye_ad';
        //获取position_id
        $position_id = BaseHomePosition::findOneval(['number' => $key], 'id');
        $data        = BaseShowcase::getByPositionConfig($position_id, $key);

        return $this->success($data);
    }

    /**
     * 检查简历置顶
     */
    public function actionCheckResumeTop()
    {
        try {
            $service = new ResumeTopService();
            $result  = $service->setPlatform(CommonService::PLATFORM_WEB_PERSON)
                ->setOparetion(ResumeTopService::OPERATION_TYPE_CHECK)
                ->init()
                ->run();

            return $this->success($result);
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 简历置顶数据的合法性验证
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionValidateResumeTop()
    {
        try {
            $params  = Yii::$app->request->post();
            $service = new ResumeTopService();
            $result  = $service->setPlatform(CommonService::PLATFORM_WEB_PERSON)
                ->setOparetion(ResumeTopService::OPERATION_TYPE_VALIDATE)
                ->init($params)
                ->run();

            return $this->success($result);
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 添加简历置顶
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionAddResumeTop()
    {
        $params = Yii::$app->request->post();

        //开启事务
        $transaction = Yii::$app->db->beginTransaction();
        try {
            $service = new ResumeTopService();
            $result  = $service->setPlatform(CommonService::PLATFORM_WEB_PERSON)
                ->setOparetion(ResumeTopService::OPERATION_TYPE_ADD)
                ->init($params)
                ->run();
            $transaction->commit();

            return $this->success($result);
        } catch (Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取PV曝光数据
     */
    public function actionPvExposure()
    {
        $resume_id = $this->getResumeId();
        $data      = BaseCompanyResumePvTotal::getPvExposure($resume_id);

        return $this->success($data);
    }

    /**
     * 获取登录弹窗提示内容
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionGetLoginPopTips()
    {
        try {
            $memberId = Yii::$app->user->id;
            if ($memberId) {
                return $this->success();
            }
            $ip     = IpHelper::getIpInt();
            $isShow = Resume::isShowLoginTipsAmount($ip);
            if (!$isShow) {
                return $this->success();
            }

            $info = Resume::getLoginTipsInfo();
            BaseMemberLoginForm::addDailyLoginTipsAmount($ip);

            return $this->success($info);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 新增弹窗统计个数
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionAddLoginPopTipsAmount()
    {
        try {
            $memberId = Yii::$app->user->id;
            if ($memberId) {
                return $this->success();
            }
            $ip     = IpHelper::getIpInt();
            $amount = BaseMemberLoginForm::getDailyLoginTipsAmount($ip);

            BaseMemberLoginForm::addDailyLoginTipsAmount($ip, $amount + 1);

            return $this->success();
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取未完善模块信息
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionGetUnCompleteModuleInfo()
    {
        try {
            $memberId = Yii::$app->user->id;

            $data = BaseResumeComplete::getUnCompleteList($memberId, 'PC');

            return $this->success($data);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    // ==================== 注销相关接口 ====================

    /**
     * 获取注销配置信息
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionGetCancelConfig()
    {
        try {
            $resumeCancelService = new ResumeCancelService();
            $result              = $resumeCancelService->getCancelConfig(Yii::$app->user->id);

            return $this->success($result);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 检查注销资格（包含求职服务检查）
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionCheckCancelEligibility()
    {
        try {
            $memberId = Yii::$app->user->id;
            $resumeId = BaseMember::getMainId($memberId);

            if (!$resumeId) {
                return $this->fail('简历不存在');
            }

            $resumeCancelService = new ResumeCancelService();

            $data = $resumeCancelService->checkEligibility($resumeId);

            return $this->success($data);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 发送注销短信验证码
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionSendCancelSms()
    {
        try {
            $ticket  = Yii::$app->request->post('ticket');
            $randStr = Yii::$app->request->post('randstr');
            // if (!$randStr || !$ticket) {
            //     return $this->fail('图形验证码验证失败');
            // }
            //
            // $captcha = new Captcha();
            // if (!$captcha->check($ticket, $randStr)) {
            //     return $this->fail('图形验证码验证失败');
            // }

            $memberId = Yii::$app->user->id;
            $member   = BaseMember::findOne($memberId);

            if (!$member) {
                return $this->fail('用户不存在');
            }

            if (!$member->mobile) {
                return $this->fail('用户未绑定手机号');
            }

            $resumeCancelService = new ResumeCancelService();
            // 发送短信验证码
            $resumeCancelService->sendCancelSms($member->mobile, $member->mobile_code);

            return $this->success([], '验证码发送成功');
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 提交注销申请
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionApplyCancel()
    {
        // 开启数据库事务
        $transaction = Yii::$app->db->beginTransaction();

        try {
            $params   = Yii::$app->request->post();
            $memberId = Yii::$app->user->id;
            $resumeId = BaseMember::getMainId($memberId);

            if (!$resumeId) {
                return $this->fail('简历不存在');
            }

            // 基础参数验证
            if (empty($params['cancelReasonType'])) {
                return $this->fail('请选择注销原因');
            }

            if (!array_key_exists($params['cancelReasonType'], BaseResumeCancelLog::CANCEL_REASON_TYPE_LIST)) {
                return $this->fail('注销原因类型无效');
            }

            if ($params['cancelReasonType'] == BaseResumeCancelLog::CANCEL_REASON_TYPE_OTHER && empty($params['cancelReasonDetail'])) {
                return $this->fail('请填写详细的注销原因');
            }

            if (empty($params['smsCode'])) {
                return $this->fail('请输入短信验证码');
            }

            // 准备服务层参数
            $serviceParams = [
                'resumeId'           => $resumeId,
                'cancelReasonType'   => $params['cancelReasonType'],
                'cancelReasonDetail' => $params['cancelReasonDetail'] ?? '',
                'ip'                 => Yii::$app->request->userIP,
                'smsCode'            => $params['smsCode'],
            ];

            $resumeCancelService = new ResumeCancelService();
            $result              = $resumeCancelService->applyCancel($serviceParams);

            // 提交事务
            $transaction->commit();

            return $this->success($result, '注销申请提交成功');
        } catch (\Exception $e) {
            // 回滚事务
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 取消注销申请
     */
    public function actionCancelApplyCancel()
    {
        $transaction = Yii::$app->db->beginTransaction();

        try {
            $token = Yii::$app->request->post('token');
            if (!$token) {
                return $this->fail('参数错误');
            }

            $resumeCancelService = new ResumeCancelService();
            $resumeCancelService->withdrawCancel($token);

            // 提交事务
            $transaction->commit();

            return $this->success([], '您的放弃注销已申请，请5分钟后重新登录');
        } catch (\Throwable $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

}