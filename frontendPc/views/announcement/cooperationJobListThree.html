<link rel="stylesheet" href="/static/css/list.css?v=20250114">

<div id="component">
    <div class="el-main">
        <div class="detail-container template-three">
            <div class="detail-header-template-three">
                <div class="detail-header">
                    <div class="detail-header-container">
                        <div class="breadcrumb">
                            位置：
                            <a href="/">高校人才网</a>＞
                            <?php foreach($info['columnInfo'] as $item):?>
                            <a href="<?= $item['url'] ?>"><?=$item['name'] ?></a>＞
                            <?php endforeach;?>
                            <a href="<?= $info['announcementUrl']?>"><?=$info['title']?></a>＞
                            职位列表
                        </div>

                        <div class="main">
                            <div class="detail">
                                <h1 class="title" title="<?=\common\helpers\StringHelper::changeQuotationMark($info['title'])?>">
                                    <?= $info['title']?>
                                </h1>

                                <div class="job-info">
                                    <div class="amount">共计 <span class="color-main"><?=$info['jobAmount']?></span> 个岗位，招 <span class="color-main"><?=$info['jobRecruitAmount']?></span> 人</div>

                                    <div class="other">
                                        <span class="time">发布时间：<?=$info['refreshTime']?> | 截止时间：<?=$info['periodDate']?> |</span>

                                        <span class="address"><?=$info['cityName']?></span>
                                    </div>
                                </div>
                                <?php if($info['establishmentTypeText'] || $info['allWelfareLabel']):?>
                                <div class="welfare-tag" id="welfareTag">
                                    <?php if($info['establishmentTypeText']):?><span class="establishment-tag boon"><?= $info['establishmentTypeText']?></span><?php endif?>
                                    <?php if(count($info['allWelfareLabel']) > 0):?>
                                    <?php foreach($info['allWelfareLabel'] as $k => $item):?>
                                    <?php if($k < $welfareLabelViewAmount):?>
                                    <span class="boon"><?=$item?></span>
                                    <?php endif?>
                                    <?php endforeach?>
                                    <?php if(count($info['allWelfareLabel']) > $welfareLabelViewAmount):?>
                                    <el-popover placement="bottom" :width="307" trigger="hover" v-cloak>
                                        <template #reference>
                                            <i class="el-icon boon-more">
                                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024">
                                                    <path
                                                            fill="currentColor"
                                                            d="M176 416a112 112 0 1 1 0 224 112 112 0 0 1 0-224zm336 0a112 112 0 1 1 0 224 112 112 0 0 1 0-224zm336 0a112 112 0 1 1 0 224 112 112 0 0 1 0-224z"
                                                    ></path>
                                                </svg>
                                            </i>
                                        </template>
                                        <?php foreach($info['allWelfareLabel'] as $k=> $item):?>
                                        <?php if($k >= $welfareLabelViewAmount):?>
                                        <span class="boon"><?=$item?></span>
                                        <?php endif;?>
                                        <?php endforeach?>
                                    </el-popover>
                                    <?php endif?>
                                    <?php endif?>
                                </div>
                                <?php endif?>
                                
                                <div class="detail-bottom">
                                    <a class="view-job" href="<?=$announcementUrl?>">查看公告详情</a>
                                    <div class="share-mini-code-container">
                                        <div class="share-mini-code-trigger share-mini-code-trigger--info">分享</div>

                                        <div class="share-mini-code-popup">
                                            <div class="share-mini-code">
                                                <img src="<?=$shareUrlCode?>" alt="" class="share-mini-code-img" />
                                            </div>
                                            <div class="share-mini-code-title">微信扫一扫</div>
                                            <div class="share-mini-code-tips">分享给你的朋友吧</div>
                                        </div>
                                    </div>
                                    <?php if ($info['isCollect'] == 1): ?>
                                    <div class="el-button--collect collected">已收藏</div>
                                    <?php else: ?>
                                    <div class="el-button--collect">收藏</div>
                                    <?php endif; ?>
                                    <div class="el-button--analyse see-heat">公告热度</div>
                                    <a class="company-home" href="<?='/company/detail/'.$info['companyId'].'.html' ?>" target="_blank">单位主页</a>
                                </div>
                            </div>
                            <div class="cover">
                                <img src="<?=$info['headerImage']?>" alt="" />
                            </div>
                        </div>
                    </div>

                    <div class="detail-header-container--fixed">
                        <div class="detail">
                            <div class="title" title="<?=$info['title']?>"><?=$info['title']?></div>
                            <div class="bottom">
                                <?php if ($info['establishmentTypeText']): ?>
                                <span class="tag"><?=$info['establishmentTypeText']?></span>
                                <?php endif; ?>
                                <div>招<span class="bold"><?=$info['jobRecruitAmount']?></span></span>人，共计<span class="bold"><?=$info['jobAmount']?></span></span>个岗位</div>
                                <a class="view-button" href="<?=$announcementUrl?>">查看公告详情</a>
                            </div>
                        </div>
                        <div class="aside">
                            <div class="detail-button">
                                <div class="share-mini-code-container">
                                    <div class="share-mini-code-trigger share-mini-code-trigger--info font-color">分享</div>

                                    <div class="share-mini-code-popup">
                                        <div class="share-mini-code">
                                            <img src="<?=$shareUrlCode?>" alt="" class="share-mini-code-img" />
                                        </div>
                                        <div class="share-mini-code-title">微信扫一扫</div>
                                        <div class="share-mini-code-tips">分享给你的朋友吧</div>
                                    </div>
                                </div>
                                <?php if ($info['isCollect'] == 1): ?>
                                <div class="el-button--collect collected">已收藏</div>
                                <?php else: ?>
                                <div class="el-button--collect">收藏</div>
                                <?php endif; ?>
                                <div class="el-button--analyse see-heat">公告热度</div>
                            </div>
                            <div class="bottom">
                                <?php if($info['status'] == 2): ?>
                                <button class="offline">已下线</button>
                                <?php endif; ?>
                                <a class="company-home" href="<?='/company/detail/'.$info['companyId'].'.html' ?>" target="_blank">单位主页</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="detail-main">
                <?= \frontendPc\components\AnnouncementJobListWidget::widget() ?>
<!--                <section>-->
<!--                    <div class="tips">-->
<!--                        声明：本站部分公告与职位内容由本站根据官方招聘公告进行整理编辑。由于用人单位需求专业、学历学位、资格条件、备注内容等内容情况复杂且有变化可能，是否符合招聘条件以用人单位公告为准或请联系用人单位确认。本站整理编辑的职位信息仅供求职者参考，如因此造成的损失本站不承担任何责任！-->
<!--                    </div>-->

<!--                    <table class="el-table">-->
<!--                        <tr>-->
<!--                            <th class="el-col el-col-10">职位信息</th>-->
<!--                            <th class="el-col el-col-8">需求专业</th>-->
<!--                            <th class="el-col el-col-6">操作</th>-->
<!--                        </tr>-->
<!--                        <?php foreach($jobList as $k=>$item):?>-->
<!--                        <tr data-href="<?=$item['url']?>">-->
<!--                            <td class="data-name el-col el-col-10">-->
<!--                                <a href="<?=$item['url']?>" target="_blank"><?=$item['jobName']?></a>-->
<!--                                <p>-->
<!--                                    <strong class="salary"><?=$item['wage']?></strong>-->
<!--                                    <?php if($item['area']):?><span><?=$item['area']?></span><?php endif;?>-->
<!--                                    <?php if($item['education']):?><span><?= $item['education']?></span><?php endif;?>-->
<!--                                    <?php if($item['nature']):?><span><?=$item['nature']?></span><?php endif;?>-->
<!--                                </p>-->

<!--                            </td>-->

<!--                            <td class="data-major el-col el-col-8">-->
<!--                                <span><?=$item['major']?></span>-->
<!--                            </td>-->

<!--                            <td class="data-operate el-col el-col-6">-->
<!--                                <?php if($item['collectStatus'] == 1): ?>-->
<!--                                <button class="el-button el-button&#45;&#45;collect job-collect-button collected"-->
<!--                                        data-id="<?=$item['jobId']?>">-->
<!--                                    <span>已收藏</span>-->
<!--                                </button>-->
<!--                                <?php else:?>-->
<!--                                <button class="el-button el-button&#45;&#45;collect job-collect-button"-->
<!--                                        data-id="<?=$item['jobId']?>">-->
<!--                                    <span>收藏</span>-->
<!--                                </button>-->
<!--                                <?php endif;?>-->

<!--                                <?php if($item['status'] == 0): ?>-->
<!--                                <button class="el-button el-button&#45;&#45;primary is-disabled offline" disabled>-->
<!--                                    <span>已下线</span>-->
<!--                                </button>-->
<!--                                <?php else:?>-->
<!--                                <?php if($item['applyStatus'] == 1):?>-->
<!--                                <button class="el-button el-button&#45;&#45;primary is-disabled" disabled>-->
<!--                                    <span>已申请</span>-->
<!--                                </button>-->
<!--                                <?php else:?>-->
<!--                                <button class="el-button el-button&#45;&#45;primary job-apply-button"-->
<!--                                        data-id="<?=$item['jobId']?>">-->
<!--                                    <span>申请职位</span>-->
<!--                                </button>-->
<!--                                <?php endif;?>-->
<!--                                <?php endif;?>-->
<!--                            </td>-->
<!--                        </tr>-->

<!--                        <?php endforeach?>-->
<!--                    </table>-->

<!--                    <div id="paginationTemplate">-->
<!--                        <el-pagination background :layout="'total, sizes, prev, pager, next, jumper'"-->
<!--                                       :current-page="page" :page-size="pageSize"-->
<!--                                       :total="count"-->
<!--                                       :total="count" @size-change="(val) => handleFilter(val, 'pageSize')"-->
<!--                                       @current-change="(val) => handleFilter(val, 'page')">-->
<!--                        </el-pagination>-->
<!--                    </div>-->
<!--                </section>-->
                <aside>
                    <?= frontendPc\components\DetailRightCompanyForm::widget(['companyId'=>$info['companyId']]) ?>

                    <!--<div class="job">
                        <div class="title">
                            <h5>专享推荐</h5>
                            <a href="<?=$recommendJobUrl?>">更多 ></a>
                        </div>

                        <div class="job-list">
                            <?php foreach($recommendJob as $job):?>
                            <div class="job-item">
                                <div class="job-item-title">
                                    <h5>
                                    <a href="<?=$job['url']?>"><?=$job['name']?></a></h5>
                                    <span><?=$job['wage']?></span>
                                </div>

                                <div class="job-item-tips">
                                    <?php if($job['education']):?><span><?=$job['education']?></span><?php endif?>
                                    <?php if($job['experience']):?><span><?=$job['experience']?></span><?php endif?>
                                    <?php if($job['releaseTime']):?><span><?=$job['releaseTime']?>发布</span><?php endif?>
                                </div>

                                <div class="job-item-data">
                                    <h6><?=$job['companyName']?></h6>
                                    <span><?=$job['areaName']?></span>
                                </div>
                            </div>
                            <?php endforeach;?>
                        </div>
                    </div>-->

                    <!--                    //登录窗口-->
                    <?php if(empty(Yii::$app->params['user'])):?>
                    <?= frontendPc\components\RightLoginForm::widget() ?>
                    <?php else:?>
                    <?= frontendPc\components\DetailGuideCard::widget() ?>
                    <?php endif;?>
                </aside>
            </div>
        </div>
    </div>
</div>

<script src="/static/js/detailService.js"></script>
<script src="/static/lib/popper/popper.min.js?v=2"></script>
<script>
    $(function () {
        Vue.createApp({}).use(ElementPlus).mount('#welfareTag')

        var id = "<?=$info['id']?>"
        var $headerCollectButtons = $('.detail-header .el-button--collect')
        var $jobCollectButtons = $('.job-collect-button')
        var $jobApplyButtons = $('.job-apply-button')
        var $noticeJobList = $('.notice-job-list-btn')
        var seeHeat = $('.see-heat')

        var params = {
            apiPull: '/api/person/announcement/check-generate-report',
            apiCreate: '/api/person/announcement/create-report',
            param: { announcementId: '<?=$info["id"]?>' }
        }

        seeHeat.on('click', function () {
            window.globalComponents.PromptDialogComponent.pull(params)
        })

        $headerCollectButtons.on('click', function () {
            var isCollected = $headerCollectButtons.hasClass('collected')

            httpPost('/api/person/announcement/collect', { id }).then(function () {
                $headerCollectButtons.toggleClass('collected').text(isCollected ? '收藏' : '已收藏')
            })
        })

        $jobCollectButtons.on('click', function (e) {
            e.stopPropagation()
            var $this = $(this)
            var jobId = $this.attr('data-id')
            var isCollected = $this.hasClass('collected')

            httpPost('/api/person/job/collect', {jobId: jobId}).then(function () {
                $this.toggleClass('collected').find('span').text(isCollected ? '收藏' : '已收藏')
            })
        })

        $noticeJobList.on('click', function () {
            window.globalComponents.applyDialogComponent.announcementApply(id)
        })

        $jobApplyButtons.on('click', function (e) {
            e.stopPropagation()
            var $this = $(this)
            var jobId = $this.attr('data-id')

            window.globalComponents.applyDialogComponent.beforeApply(jobId, function () {
                $this.prop('disabled', true).addClass('is-disabled').find('span').text('已申请')
            })
        })

        // $('body').on('click', '.detail-main .el-table tr', function () {
        //     var href = $(this).data('href')
        //     if (href) {
        //         window.location.href = href
        //     }
        // })

        // 职位需求专业气泡
        !function majorPopper() {
            const el = document.createElement('div')
            el.className = "major-popper"
            el.id = "custom-popper"
            el.innerHTML = '<span class="arrow"></span><div class="content"></div>'
            document.body.appendChild(el)

            let popover = null

            $('body').on('mouseover', '.data-major', function () {
                const $major = $(this)
                const $majorSpan = $major.find('span')
                const isOverflow = $majorSpan.height() > $major.height()

                if (isOverflow) {
                    const $popperEl = $('.major-popper')

                    $('.major-popper .content').text($majorSpan.text())

                    popover = Popper.createPopper($major[0], $popperEl[0], {
                        placement: 'top',
                        modifiers: [{
                            name: 'offset',
                            options: {
                                offset: [0, -10]
                            }
                        }]
                    })
                }
            })

            $('body').on('mouseout', '.data-major', function () {
                if (popover) popover.destroy()
            })
        }()
    })
</script>