<link rel="stylesheet" href="/static/css/news.css?v=0.3">

<!-- 当前位置 -->
<?= frontendPc\components\CurrentLocationWidget::widget(['columnId'=>$columnId]) ?>

<div class="main">
    <div class="left">
        <div class="new">
            <div class="common-title top">
                <h2><?=$name?></h2>
<!--                <a class="more" href="" target="_blank">更多>></a>-->
            </div>
            <div class="tabs-new-content">
                <ul class="list">
                    <?php foreach($newsList as $k => $v) { ?>
                    <li>
                        <a href="<?= $v['url'] ?>" target="_blank">
                            <div class="cover">
                                <img src="<?= $v['cover_thumb'] ?>" alt="">
                            </div>
                            <div class="detail">
                                <h3><?= $v['title'] ?></h3>
                                <div class="info">
                                    <div class="intro">
                                        <?= $v['abstract'] ?>
                                    </div>
                                </div>
                                <div class="bottom">
                                    <div class="time"><?= $v['time'] ?></div>
                                    <div class="amount"><?= $v['click'] ?> 浏览 · <?= $v['collectAmount'] ?> 收藏</div>
                                </div>
                            </div>
                        </a>
                    </li>
                    <?php } ?>
                </ul>
            </div>
            <div class="pagination" id="paginationComponent">
                <el-pagination background :layout="'prev, pager, next'"
                               :current-page="page" :page-size="pageSize" :total="count"
                               @size-change="(val) => handleFilter(val, 'pageSize')"
                               @current-change="(val) => handleFilter(val, 'page')">
                </el-pagination>
            </div>
        </div>
    </div>
    <div class="right">
        <!--热门专题-->
        <?= frontendPc\components\HotTopicWidget::widget() ?>
        <!--圈子-->
        <?= frontendPc\components\GroupWidget::widget() ?>

        <div class="recommend">
            <!--推荐资讯-->
            <?=$recommendNews?>
            <!--热点资讯-->
            <?=$hotList?>
        </div>
    </div>
</div>
<script src="/static/js/news.js"></script>

<script>
    $(function () {
        var swiper = new Swiper(".banner", {
            pagination: {
                el: ".banner-pagination",
                clickable: true
            },
            autoplay: {
                pauseOnMouseEnter: true,
                disableOnInteraction: false,
                delay: 5000
            },
            loop: true,
            simulateTouch: true,
        });


        var TabsSwitch = function () {
            var tabsMap = ['.tabs-new', '.tabs-circle']
            tabsMap.forEach(function (item, index) {
                $(item + ' li').hover(function () {
                    var i = $(this).index()
                    $(this).addClass('active').siblings().removeClass('active')
                    $(item + '-content .list').eq(i).show().siblings().hide()
                }, function () {
                })
            })
        }
        TabsSwitch()


        const component = {
            data() {
                return {
                    page: 1,
                    pageSize: 20,
                    count: <?=$total?>,
                    columnId:<?=$columnId?>,
                }
            },
            methods:{
                handleFilter(page) {
                    this.page = page
                    this.getList()
                },
                getList(){
                   // 拼接url
                    var url = '/home/<USER>' + this.page  + '&columnId=' + this.columnId
                    var _that = this
                        // 请求数据
                    $.ajax({
                        url: url,
                        type: 'get',
                        dataType: 'json',
                        success: function (res) {
                            if (res.result==1) {
                                $('.tabs-new-content').html(res.data.html)
                                _that.count = res.data.total * 1
                                // 回到头部
                                scrollTo(0,0);
                            }
                        }
                    })
                }
            },
        }

        Vue.createApp(component).use(ElementPlus, {
            locale: {
                name: 'zh-cn',
                el: {
                    pagination: {
                        goto: '前往',
                        pagesize: '条/页',
                        total: '共 {total} 条',
                        pageClassifier: '页',
                    }
                }
            }
        }).mount('#paginationComponent')
    })


</script>

<script src="/static/js/column.js?v=2"></script>