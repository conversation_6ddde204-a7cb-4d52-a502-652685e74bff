    <link rel="stylesheet" href="/static/css/job.css?v=0.1">
    <div id="component" v-cloak>
        <div class="el-main">
            <div class="job-container">
                <div class="search-container">
                    <div class="search-content">
                        <div class="search-main">
                            <div class="el-input el-input-group el-input--prefix el-input-group--append">
                                <input v-model.trim="keyword" class="search-input el-input__inner"
                                    :class="recordVisible ? 'active' : ''" type="text" autocomplete="off" placeholder="请输入职位/公告/单位等关键词搜索"
                                    @focus="recordVisible = true" @blur="handleRecord" @keydown.enter="handleSearch">

                                <span class="search-options el-input__prefix">
                                    <span class="el-input__suffix-inner">
                                        <!-- <el-cascader class="search-job-type" v-model="jobType" :options="jobOptions"
                                            placeholder="职位类型" :clearable="true" :show-all-levels="false"
                                            :props="{ checkStrictly: false, emitPath: false, expandTrigger: 'hover' }" /> -->
                                        <div @mouseenter="handleJobTypeMouseenter"
                                        @mouseleave="handleJobTypeMouseleave"
                                        @click="handleOpenCascader('jobType')" class="search-job-type-content">
                                            <span class="job-type-label">{{jobTypeLabel}}</span>

                                            <span class="el-input__suffix">
                                                <span class="el-input__suffix-inner">
                                                    <i v-show="isJobTypeClearable"
                                                    @click="clearJobType" class="el-input__icon el-icon-circle-close"></i>
                                                    <i v-show="!isJobTypeClearable" class="el-input__icon el-icon-arrow-down"></i>
                                                </span>
                                            </span>
                                        </div>
                                    </span>
                                </span>

                                <div class="el-input-group__append">
                                    <button class="search-button el-button el-button--primary" @click="handleSearch"></button>
                                </div>

                                <div class="search-record" :class="recordVisible ? 'active' : ''">
                                    <ul class="list">
                                        <li class="tips" v-show="searchRecord.length">历史搜索记录</li>

                                        <li class="item" v-for="(item, index) in searchRecord" :key="index"
                                            @click="keyword = item">
                                            <span>{{ item }}</span>
                                            <i class="el-icon el-icon-close" @click.stop="removeRecord(item)"></i>
                                        </li>

                                        <li class="item" @click="keyword = '高才信息科技有限公司'">
                                            <span>高才信息科技有限公司</span>
                                            <i>热门推荐</i>
                                        </li>

                                        <li class="remove" v-show="searchRecord.length">
                                            <span @click="removeRecord([])">删除全部历史</span>
                                        </li>
                                    </ul>
                                </div>
                            </div>

                            <div class="search-hot">
                                热门搜索：
                                <?php foreach ($jobHotSearchList as $i => $hot) { ?>
                                <a href="<?php echo $hot['url'];?>" class="showcase-browse"
                                   data-showcase-number="<?php echo $hot['number'] ?>"
                                   data-showcase-id="<?php echo $hot['id'] ?>"><?php echo $hot['title'];?></a>
                                <?php }?>
                            </div>
                        </div>

<!--                        <div class="recommend-company">-->
<!--                            <div class="company-pane">-->
<!--                                <?php foreach($showcaseList as $k => $item):?>-->
<!--                                <div class="pane <?php if($k == 0):?>active<?php endif;?>">-->
<!--                                    <div class="swiper slide-recommend-company-<?php echo $k+1;?>">-->
<!--                                        <div class="swiper-wrapper">-->
<!--                                            <?php foreach($item['list'] as $showcase):?>-->
<!--                                            <div class="swiper-slide">-->
<!--                                                <a href="<?=$showcase['target_link']?>" target="_blank" data-showcase-number="<?=$showcase['number'] ?>" data-showcase-id="<?=$showcase['id'] ?>" class="showcase-browse">-->
<!--                                                    <img src="<?=$showcase['image_link']?>" alt="<?=$showcase['title']?>" />-->
<!--                                                </a>-->
<!--                                            </div>-->
<!--                                            <?php endforeach;?>-->
<!--                                        </div>-->
<!--                                    </div>-->
<!--                                    <div class="swiper-pagination recommend-pagination-<?php echo $k+1;?>"></div>-->
<!--                                </div>-->
<!--                                <?php endforeach;?>-->
<!--                            </div>-->

<!--                            <div class="recommend-tabs">-->
<!--                                <div class="label">-->
<!--                                    <span>HOT</span>-->
<!--                                    推荐单位：-->
<!--                                </div>-->
<!--                                <div class="tabs" @click="handleRecommend">-->
<!--                                    <?php foreach($showcaseList as $k => $item):?>-->
<!--                                    <span <?php if($k == 0):?>class='active'<?php endif;?>><?= $item['name']?></span>-->
<!--                                    <?php endforeach;?>-->
<!--                                </div>-->
<!--                            </div>-->
<!--                        </div>-->
                        <div class="swiper mySwiper recommend-company" v-cloak>
                            <div class="swiper-wrapper company-pane">
                                <?php foreach ($showcaseList as $k => $v): ?>
                                <div class="swiper-slide pane" :data-swiper-autoplay="delayTime[<?=$k?>]">
                                    <el-carousel :height="paneHeight" trigger="click" arrow="never"
                                                 pause-on-hover="true" indicator-position="outside" :interval="runTime"
                                                 :autoplay="running[<?=$k?>]">
                                        <?php foreach ($v['list'] as $k1 => $item): ?>

                                        <?php if ($k1%8==0): ?>
                                        <el-carousel-item>
                                        <?php endif; ?>
                                            <a href="<?=$item['target_link']?>" target="_blank" data-showcase-number="<?=$item['number'] ?>" data-showcase-id="<?=$item['id'] ?>" class="showcase-browse">
                                                <img src="<?=$item['image_link']?>" alt="<?=$item['title']?>" />
                                            </a>
                                            <?php if ($k1%8==7): ?>
                                        </el-carousel-item>
                                        <?php endif; ?>
                                        <?php endforeach; ?>

                                    </el-carousel>
                                </div>
                                <?php endforeach; ?>


                            </div>

                            <div class="recommend-tabs">
                                <div class="label">
                                    <span>HOT</span>推荐单位：
                                </div>
                                <div class="swiper-pagination tabs" @click="handleRecommend"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="job-content">
                    <div class="search-filter">
                        <div class="filter-pane area special is-show">
                            <h6 class="filter-label">工作地点</h6>
                            <div class="filter-value">
                                <a href="" class="el-tag" @click.prevent="handleFilter(null, 'areaId')">
                                    全部<i class="el-icon el-icon-close el-tag__close"></i>
                                </a>
                                <?php foreach ($cityList as $k => $v): ?>
                                <a href="<?= $v['url'] ?>" class="el-tag  <?= $v['class'] ?>"
                                   data-param="areaId-<?= $k ?>">
                                    <?= $v['name'] ?><i class="el-icon el-icon-close el-tag__close"></i>
                                </a>
                                <?php endforeach; ?>


                            </div>
                            <span class="filter-more is-reverse" @click="handleShow">更多</span>
                        </div>

                        <div class="filter-pane nature">
                            <h6 class="filter-label">单位性质</h6>
                            <div class="filter-value">
                                <a href="" class="el-tag " @click.prevent="handleFilter(null, 'companyNature')">
                                    全部<i class="el-icon el-icon-close el-tag__close" ></i>
                                </a>

                                <?php foreach ($companyNatureList as $k => $v): ?>
                                <a href="<?= $v['url'] ?>" class="el-tag <?= $v['class'] ?>"><?= $v['name'] ?><i class="el-icon el-icon-close el-tag__close" ></i>
                                </a>
                                <?php endforeach; ?>
                            </div>
                        </div>

                        <div class="filter-pane type">
                            <h6 class="filter-label">单位类型</h6>
                            <div class="filter-value">
                                <a href="" class="el-tag " >
                                    全部<i class="el-icon el-icon-close el-tag__close" ></i>
                                </a>

                                <?php foreach ($companyTypeList as $k => $v): ?>
                                <a href="<?= $v['url'] ?>" class="el-tag <?= $v['class'] ?>"
                                   data-param="companyNature-<?= $k ?>"><?= $v['name'] ?><i class="el-icon el-icon-close el-tag__close" ></i>
                                </a>
                                <?php endforeach; ?>

                            </div>
                        </div>

                        <div class="filter-pane major">
                            <h6 class="filter-label">学科分类</h6>
                            <div class="filter-value">
                                <a href="" class="el-tag"  @click.prevent="handleFilter(null, 'majorId')">
                                    全部<i class="el-icon el-icon-close el-tag__close"></i>
                                </a>
                                <?php if(!empty($majorId)&&$popover==2):?>
                                <a href="" class="el-tag is-level is-show"
                                    @click.prevent="handleFilter(<?=$parentMajorId?$parentMajorId:0?>, 'majorId')">上级</a>
                                <?php endif;?>

                                <?php foreach ($majorList as $k => $v): ?>
                                <a href="<?= $v['url'] ?>" class="el-tag <?= $v['class'] ?>"
                                   data-param="majorId-<?= $v['id'] ?>"><?= $v['name'] ?>
                                    <i class="el-icon el-icon-close el-tag__close" ></i>
                                </a>
                                <?php endforeach; ?>

                            </div>
                            <span class="filter-multiple" :class="{active: majorId.length>0}"
                                @click="handleOpenCascader('major')">{{majorId.length ?
                                '选项·'+majorId.length:'多选'}}</span>
                        </div>

<!--                        <div class="filter-pane weflare special">-->
<!--                            <h6 class="filter-label">职位福利</h6>-->
<!--                            <div class="filter-value">-->
<!--                                <a href="" class="el-tag " @click.prevent="handleFilter(null, 'welfareLabelId')">-->
<!--                                    全部<i class="el-icon el-icon-close el-tag__close"></i>-->
<!--                                </a>-->


<!--                                <?php foreach ($welfareLabelList as $k => $v): ?>-->
<!--                                <a href="<?= $v['url'] ?>" class="el-tag <?= $v['class'] ?>"-->
<!--                                   data-param="welfareLabelId-<?= $v['id'] ?>"><?= $v['name'] ?><i class="el-icon el-icon-close el-tag__close" ></i>-->
<!--                                </a>-->
<!--                                <?php endforeach; ?>-->

<!--                            </div>-->
<!--                            <span class="filter-more" @click="handleShow">更多</span>-->
<!--                        </div>-->

                        <div class="filter-pane filter-pane-more">
                            <h6 class="filter-label">更多筛选</h6>
                            <div class="filter-value">
                                <el-select v-model="educationType" class="education" multiple collapse-tags
                                           placeholder="学历要求" @change="(val) => handleFilter(val, 'educationType')">
                                    <el-option v-for="{label, value} in educationOptions" :key="value" :label="label"
                                               :value="value">
                                    </el-option>
                                </el-select>

                                <el-cascader v-model="industryId" :options="industryOptions" placeholder="行业类别"
                                    :clearable="true" :show-all-levels="false"
                                    :props="{ checkStrictly: true, emitPath: false, expandTrigger: 'hover' }"
                                    @change="(val) => handleFilter(val, 'industryId')">
                                </el-cascader>

                                <el-select v-model="experienceType" placeholder="工作年限" clearable="true"
                                    @change="(val) => handleFilter(val, 'experienceType')">
                                    <el-option v-for="{label, value} in experienceOptions" :key="value" :label="label"
                                        :value="value">
                                    </el-option>
                                </el-select>

                                <el-select v-model="releaseTimeType" placeholder="发布时间" clearable="true"
                                    @change="(val) => handleFilter(val, 'releaseTimeType')">
                                    <el-option v-for="{label, value} in releaseOptions" :key="value" :label="label"
                                        :value="value">
                                    </el-option>
                                </el-select>

                                <el-select v-model="wageId" placeholder="薪资范围" clearable="true"
                                    @change="(val) => handleFilter(val, 'wageId')">
                                    <el-option v-for="{label, value} in wageOptions" :key="value" :label="label"
                                        :value="value">
                                    </el-option>
                                </el-select>

                                <el-select v-model="companyScaleType" placeholder="单位规模" clearable="true"
                                    @change="(val) => handleFilter(val, 'companyScaleType')">
                                    <el-option v-for="{label, value} in scaleOptions" :key="value" :label="label"
                                        :value="value">
                                    </el-option>
                                </el-select>

                                <el-select v-model="natureType" placeholder="职位性质" clearable="true"
                                    @change="(val) => handleFilter(val, 'natureType')">
                                    <el-option v-for="{label, value} in natureOptions" :key="value" :label="label"
                                        :value="value">
                                    </el-option>
                                </el-select>

                                <el-select v-model="titleType" placeholder="职称类型" clearable="true"
                                    @change="(val) => handleFilter(val, 'titleType')">
                                    <el-option v-for="{label, value} in titleOptions" :key="value" :label="label"
                                        :value="value">
                                    </el-option>
                                </el-select>
                            </div>
                            <span class="filter-clear" @click="handleClear">清空筛选条件</span>
                        </div>
                    </div>

                    <div class="search-result">
                        <div class="result-header">
                            <h4 class="result-title">职位列表</h4>

                            <div class="result-sort">
                                <span :class="sort === 'default' ? 'active' : ''" @click="handleSort('default')">综合排序</span>
                                <span :class="sort === 'new' ? 'active' : ''"
                                    @click="handleSort('new')">最新职位</span>
                            </div>
                        </div>

                        <div class="result-list">
                            <?php foreach ($jobList as $k => $job): ?>
                            <div class="result-item">
                                <div class="job-data">
                                    <div class="title el-row">
                                        <h3>
                                            <a target="_blank" href="<?= $job['url'] ?>" title="<?= \common\helpers\StringHelper::changeQuotationMark($job['jobNameTitle']) ?>"><?= $job['jobName'] ?></a>
                                        </h3>
<!--                                        <?php if($job['topType']):?>-->
<!--                                        <span>急聘</span>-->

<!--                                        <?php endif?>-->
                                        <?php if($job['shortRefreshTime']):?><span><?=$job['shortRefreshTime']?> 发布</span><?php endif?>

                                    </div>

                                    <div class="basic el-row">
                                        <?php if($job['wage']):?> <span class="salary"><?= $job['wage'] ?></span><?php endif?>
                                        <?php if($job['areaName']):?>  <span class="area"><?= $job['areaName'] ?></span><?php endif?>
                                        <?php if($job['experience']):?> <span><?= $job['experience'] ?></span><?php endif?>
                                        <?php if($job['education']):?> <span><?= $job['education'] ?></span><?php endif?>
                                        <?php if($job['amount']):?><span><?= $job['amount'] ?>人</span><?php endif?>
                                    </div>

                                    <?php if($job['announcementName']):?>
                                        <div class="tips el-row">
                                            <a target="_blank" title="<?=$job['announcementName']?>" href="<?=$job['announcementUrl']?>"><?=$job['announcementName']?></a>
                                        </div>
                                    <?php endif?>
                                </div>

                                <div class="unit-data">
                                    <div class="title el-row">
                                        <h3>
                                            <a target="_blank" href="<?= $job['companyUrl'] ?>"
                                                title="<?php echo \common\helpers\StringHelper::changeQuotationMark($job['companyName']); ?>"><?= $job['companyName'] ?></a>
                                        </h3>
                                    </div>

                                    <div class="basic el-row">
                                        <?= $job['companyTypeName'] ?>/<?= $job['companyNatureName'] ?>
                                    </div>

                                    <?php if($job['welfareTagArr']):?>
                                        <div class="tips el-row">
                                            <?php foreach ($job['welfareTagArr'] as $key => $welfareTag): ?>
                                            <span><?= $welfareTag ?></span>
                                            <?php endforeach; ?>
                                        </div>
                                    <?php endif?>
                                </div>

                                <div class="operate">
                                    <?php if ($job['status'] == 0):?>
                                        <button class="el-button el-button--info is-plain is-disabled" disabled="disabled">
                                            <span>已下线</span>
                                        </button>
                                    <?php else:?>
                                        <?php if ($job['applyStatus'] == 2): ?>
                                            <button class="el-button el-button--primary job-apply-button" data-id="<?= $job['jobId'] ?>" data-template-id="<?= $job['templateId'] ?>">
                                                <span>申请职位</span>
                                            </button>
                                        <?php else: ?>
                                            <button class="el-button el-button--primary is-disabled" disabled="disabled">
                                                <span>已申请</span>
                                            </button>
                                        <?php endif; ?>
                                    <?php endif;?>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>

                        <el-pagination background
                            :total="count" :page-size="pageSize" @size-change="(val) => handleFilter(val, 'pageSize')"
                                       :current-page="page"
                                       layout="prev, pager, next"
                            @current-change="(val) => handleFilter(val, 'page')">
                        </el-pagination>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?= frontendPc\components\DialogCascaderJobTypeWidget::widget()?>
    <?= frontendPc\components\DialogCascaderMajorWidget::widget()?>
    <script>
        $(function () {
            const component = {
                data() {
                    return {
                        keyword: '',
                        searchType: 1,
                        typeOptions: [{ label: '职位', value: 1 }, { label: '公告', value: 2 }],
                        jobType: '',
                        jobTypeLabel: '<?= $jobTypeTxt;?>' || '职位类型',
                        isJobTypeClearable: false,
                        jobOptions: [
                            <?php foreach($categoryJobList as $k=>$v):?>
                                 { label: '<?=$v["v"]?>', value: <?=$v["k"]?>,children:[
                                <?php foreach($v['children'] as $key=>$val):?>
                                 { label: '<?=$val["v"]?>', value: <?=$val["k"]?>},
                                 <?php endforeach; ?>
                              ]
                              },
                            <?php endforeach; ?>
                        ],
                        recordVisible: false,
                        searchRecord: [],
                        paneHeight: '70px',
                        runTime: 3000,
                        running: [
                            <?php foreach ($companyNatureList as $k => $v): ?>
                                <?php echo $k==0?true:false; ?>,,
                            <?php endforeach; ?>
                        ],
                        delayTime: [],
                        /* filter */
                        educationType: [],
                        educationOptions: [
                            <?php foreach($educationRequire as $k=>$v):?>
                            { label: '<?=$v["name"]?>', value: <?=$v["id"]?> },
                            <?php endforeach; ?>

                        ],
                        industryId: '',
                        industryOptions: [

                                     <?php foreach($tradeList as $k=>$v):?>
                             { label: '<?=$v["v"]?>', value: <?=$v["k"]?>,children:[
                                <?php foreach($v['children'] as $key=>$val):?>
                                 { label: '<?=$val["v"]?>', value: <?=$val["k"]?>},
                                 <?php endforeach; ?>
                              ]
                              },
                            <?php endforeach; ?>
                        ],
                        experienceType: '',
                        experienceOptions: [
                        <?php foreach($experienceList as $k=>$v):?>
                            { label: '<?=$v["name"]?>', value: <?=$v["id"]?> },
                        <?php endforeach; ?>
                        ],
                        releaseTimeType: '',
                        releaseOptions: [
                        <?php foreach($releaseTimeList as $k=>$v):?>
                        { label: '<?=$v["name"]?>', value: <?=$v["id"]?> },
                        <?php endforeach; ?>
                        ],
                        wageId: '',
                        wageOptions: [
                          <?php foreach($wageRangeList as $k=>$v):?>
                        { label: '<?=$v["name"]?>', value: <?=$v["id"]?> },
                        <?php endforeach; ?>
                        ],
                        companyScaleType: '',
                        scaleOptions: [

                        <?php foreach($companyScaleList as $k=>$v):?>
                        { label: '<?=$v["name"]?>', value: <?=$v["id"]?> },
                        <?php endforeach; ?>

                        ],
                        natureType: '',
                        natureOptions: [
                        <?php foreach($jobNatureList as $k=>$v):?>
                        { label: '<?=$v["name"]?>', value: <?=$v["id"]?> },
                        <?php endforeach; ?>
                        ],
                        titleType: '',
                        titleOptions: [
                        <?php foreach($titleTypeList as $k=>$v):?>
                        { label: '<?=$v["v"]?>', value: <?=$v["code"]?> },
                        <?php endforeach; ?>
                        ],
                        sort: 'recommend',
                        page: 1,
                        pageSize: 20,
                        count: <?=$totalNum?>,

                        popover: '', // 1多选，2其他
                        multipleVisible: false,
                        majorFirstIndex: 0,
                        majorChecked: [],
                        majorId: [],
                        majorOption:[
                        <?php foreach($majorAllList as $k=>$v):?>
                            {
                                "k": '<?=$v["k"]?>',
                                "v": '<?=$v["v"]?>',
                                "children": [
                                <?php foreach($v['children'] as $kk=>$vv):?>
                                {
                                    "k": '<?=$vv["k"]?>',
                                    "v": '<?=$vv["v"]?>',
                                },
                                <?php endforeach; ?>
                            ]
                            },
                        <?php endforeach; ?>
                    ]
                    }
                },

                mounted() {
                    let search = window.location.search
                    // 把?p=换成?page=
                    search = search.replace('p=', 'page=')
                    const [mark, query] = search.split('?')


                    const getParams = (key, feature = '') => {
                        if (new RegExp(`${key}=([^&]*)`).test(query)) {
                            const value = decodeURIComponent(RegExp.$1)
                            const toNumber = (val) => /^-?\d+$/.test(val) ? val * 1 : val
                            if (feature) {
                                this[key] = value.split(feature).map(item => toNumber(item))
                            } else {
                                this[key] = toNumber(value)
                            }
                        }
                    }

                    if (query) {
                        getParams('keyword')
                        getParams('searchType')
                        getParams('jobType')
                        getParams('educationType', '_')
                        getParams('industryId')
                        getParams('experienceType')
                        getParams('releaseTimeType')
                        getParams('wageId')
                        getParams('companyScaleType')
                        getParams('natureType')
                        getParams('titleType')
                        getParams('sort')
                        getParams('page')
                        getParams('pageSize')
                        getParams('popover')
                        if (this.popover === 1) {
                            getParams('majorId', '_')
                        }
                    }

                    this.searchRecord = this.getRecord()

                    var $panes = $('.recommend-company .pane')

                    // 获取每个分类的页数，赋予对应轮播时间
                    for (var i = 0; i < $panes.length; i++) {
                        let time = 0
                        let length = $panes.eq(i).find('.el-carousel__item').length

                        // 当分类的页数超过2页才显示轮播点
                        if (length > 1) {
                            $panes.eq(i).find('.el-carousel__indicators').css('visibility', 'visible')
                        }

                        time = length * this.runTime
                        this.delayTime.push(time)
                    }

                },

                methods: {
                    getRecord() {
                        return (JSON.parse(window.localStorage.getItem('searchRecord')) || [])
                    },

                    setRecord(val) {
                        window.localStorage.setItem('searchRecord', JSON.stringify(val))
                    },

                    updRecord(val) {
                        const record = this.getRecord()
                        record.unshift(val)

                        const result = Array.from(new Set(record)).slice(0, 5)

                        this.searchRecord = result
                        this.setRecord(result)
                    },

                    updQuery(data) {
                        const base = window.location.href
                        // 把data里面的page换成p
                        data.p = data.page
                        delete data.page
                        const hasParams = base.indexOf('?') > -1
                        const baseUrl = base + (hasParams ? '' : '?')
                        const keys = Object.keys(data)

                        const result = keys.reduce((previous, current) => {
                            const value = data[current]
                            const isValid = value === null ? false : (value !== '')
                            const isExist = new RegExp(`(${current}=[^&]*)`).test(previous)
                            const keyValue = isExist ? RegExp.$1 : ''

                            if (isValid) {
                                if (isExist) {
                                    previous = previous.replace(keyValue, `${current}=${value}`)
                                } else {
                                    previous += `&${current}=${encodeURIComponent(value)}`
                                }
                            } else {
                                previous = previous.replace(new RegExp(`&?${keyValue}`), '')
                            }

                            return previous.replace(/\?&/, '?')
                        }, baseUrl)

                        return result.replace(/\?$/, '')
                    },

                    handleSearch() {
                        const { keyword, searchType, jobType } = this

                        this.recordVisible = false
                        if ((keyword + '').trim().length) {
                            this.updRecord(keyword)
                        }
                        window.location.href = this.updQuery({ keyword, searchType, jobType, page: 1 })
                    },

                    handleRecord() {
                        setTimeout(() => {
                            this.recordVisible = false
                        }, 320)
                    },

                    removeRecord(val) {
                        if (Array.isArray(val)) {
                            this.searchRecord = []
                            this.setRecord([])
                        } else {
                            const index = this.searchRecord.findIndex(item => item === val)

                            this.searchRecord.splice(index, 1)
                            this.setRecord(this.searchRecord)
                        }
                        this.recordVisible = false
                    },

                    // 重置当前tab图片自动轮播状态
                    handleRecommend(e) {
                        const { nodeName } = e.target
                        const index = $(e.target).index()
                        const $recommend = $('.recommend-company .pane').eq(index)

                        if (nodeName === 'SPAN') {
                            if ($recommend.find('.el-carousel__indicator')[0]) {
                                $recommend.find('.el-carousel__indicator')[0].click()
                                this.running.fill(false)[$(e.target).index()] = true
                            }
                        }
                    },

                    handleShow(e) {
                        const $this = $(e.target)
                        const $parent = $this.parent()

                        $this.toggleClass('is-reverse')
                        $parent.toggleClass('is-show')
                    },

                    handleFilter(val, key) {
                        const query = { [key]: Array.isArray(val) ? val.join('_') : val }
                        if (key !== 'page') {
                            query.page = 1
                        }
                        window.location.href = this.updQuery(query)
                    },

                    handleSort(val) {
                        this.sort = val
                        this.handleFilter(val, 'sort')
                    },

                    handleClear() {
                        const { keyword, searchType, jobType } = this
                        window.location.href = `/job?keyword=${encodeURIComponent(keyword)}&searchType=${searchType}&jobType=${jobType}`
                    },

                    handleOpenCascader(type){
                        const _this = this
                        const { jobType, majorId, popover, keyword, searchType } = this

                        const jobTypes = jobType ? String(jobType).split('_') : []
                        const majorIds = majorId.map(item => item.toString())


                        if(type === 'jobType') {
                            window.globalComponents.cascaderDialogJobType.open(jobTypes,function (value) {
                                _this.jobType = value
                                const jobTypes = value.join('_')
                                const majorIds = majorId.join('_')
                                window.location.href = _this.updQuery({ keyword, searchType, jobType:jobTypes, majorId:majorIds, page: 1, popover })
                            })
                        }

                        if(type === 'major'){
                            window.globalComponents.cascaderDialogMajor.open(majorIds,function (value) {
                                _this.majorId = value
                                const majorIds = value.join('_')
                                window.location.href = _this.updQuery({ keyword, searchType, jobType, majorId:majorIds, page: 1, popover: 1 })
                            })
                        }
                    },

                    handleJobTypeMouseenter(){
                        this.isJobTypeClearable = !!this.jobType
                    },

                    handleJobTypeMouseleave(){
                        this.isJobTypeClearable = false
                    },

                    clearJobType(event){
                        event.stopPropagation()
                        this.jobType = ''
                        this.jobTypeLabel = '职位类型'
                    }
                }
            }

            Vue.createApp(component).use(ElementPlus, {
                locale: {
                    name: 'zh-cn',
                    el: {
                        pagination: {
                            goto: '前往',
                            pagesize: '条/页',
                            // total: '共 {total} 条',
                            pageClassifier: '页',
                            deprecationWarning: '你使用了一些已被废弃的用法，请参考 el-pagination 的官方文档',
                        }
                    }
                }
            }).mount('#component')

            var $applyButtons = $('.job-apply-button')

            $applyButtons.on('click', function () {
                var $this = $(this)
                var jobId = $this.attr('data-id')
                var jobTemplateId= $this.attr('data-template-id')
                let checkApi = '/api/person/member/check-user-apply-status'
                let applyApi = '/api/person/job/apply'

                if(jobTemplateId==='3'){
                     httpPost(checkApi, { jobId }).then(function ({ resumeStep }) {
                        if (resumeStep > 3) {
                            httpPost(applyApi, { jobId }).then((res) => {
                                window.globalComponents.successDialogApplyJob.showSuccessDialog(res, function () {
                                    $this.prop('disabled', true).addClass('is-disabled').find('span').text('已报名')
                                })
                            }).catch(error => { })
                        } else {
                            ElementPlus.ElMessage({
                                message: `当前简历信息不完善，请先完善简历！5S后自动帮您打开简历完善页面`,
                                type: 'warning',
                                duration: 4000,
                                onClose: () => { window.open('/member/person/resume') }
                            })
                        }
                    })

                }else{

                    window.globalComponents.applyDialogComponent.beforeApply(jobId, function () {
                        $this.prop('disabled', true).addClass('is-disabled').find('span').text('已申请')
                    })

                }
            })
        });

    </script>

<!--    <script>-->
<!--        $(function(){-->
<!--            var defaultDelay = 5000-->

<!--            const swiperArray = [-->
<!--            <?php foreach($showcaseList as $k => $item):?>-->
<!--                {-->
<!--                    swiper: '.slide-recommend-company-<?php echo $k+1;?>',-->
<!--                    pagination: '.recommend-pagination-<?php echo $k+1;?>'-->
<!--                },-->
<!--                <?php endforeach;?>-->
<!--            ]-->

<!--            swiperArray.forEach(function (item) {-->
<!--                new Swiper(item.swiper, {-->
<!--                    slidesPerView: 8,-->
<!--                    spaceBetween: 10,-->
<!--                    slidesPerGroup: 8,-->
<!--                    loop: true,-->
<!--                    loopFillGroupWithBlank: true,-->
<!--                    simulateTouch: false,-->
<!--                    autoplay: {-->
<!--                        pauseOnMouseEnter: true,-->
<!--                        disableOnInteraction: false,-->
<!--                        delay: defaultDelay-->
<!--                    },-->
<!--                    pagination: {-->
<!--                        el: item.pagination,-->
<!--                        clickable: true,-->
<!--                    },-->
<!--                });-->
<!--            })-->
<!--        })-->
<!--    </script>-->
        <script>
            $(function () {
                const companyArray = [
                    <?php foreach ($showcaseList as $k => $v): ?>
                        "<?php echo $v['name']; ?>",
                    <?php endforeach; ?>
                ]

                new Swiper('.mySwiper', {
                    loop: true,
                    simulateTouch: false,
                    autoplay: {
                        pauseOnMouseEnter: true,
                        disableOnInteraction: false
                    },
                    pagination: {
                        el: ".swiper-pagination",
                        clickable: true,
                        renderBullet: function (index, className) {
                            return `<span class="${className}">${companyArray[index]}</span>`
                        }
                    },
                    on: {
                        slideChangeTransitionStart: function () {
                            this.activeIndex = this.activeIndex > companyArray.length ? 1 : this.activeIndex
                            $('.tabs span').eq(this.activeIndex - 1).click()
                        }
                    }
                })
            })
        </script>
