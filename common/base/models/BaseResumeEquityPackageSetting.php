<?php

namespace common\base\models;

use common\libs\Cache;
use common\models\ResumeEquityPackageSetting;
use frontendPc\models\ResumeEquityPackageCategorySetting;
use yii\db\Expression;
use Yii;

class BaseResumeEquityPackageSetting extends ResumeEquityPackageSetting
{
    // 待审核
    const STATUS_AUDIT = 0;
    // 已上线
    const STATUS_ONLINE = 1;
    // 已下线
    const STATUS_OFFLINE = -1;

    const STATUS_LIST = [
        self::STATUS_AUDIT   => '待审核',
        self::STATUS_ONLINE  => '已上线',
        self::STATUS_OFFLINE => '已下线',
    ];

    // 购买类型
    const TYPE_BUY_1 = 1;
    const TYPE_BUY_2 = 2;
    const TYPE_BUY_3 = 3;
    const TYPE_BUY_4 = 4;
    const TYPE_BUY_5 = 5;
    const TYPE_BUY_6 = 6;

    const TYPE_BUY_LIST = [
        self::TYPE_BUY_1 => '体验版',
        self::TYPE_BUY_2 => '热销',
        self::TYPE_BUY_3 => '超值',
        self::TYPE_BUY_4 => '内测专享',
        self::TYPE_BUY_5 => '推荐',
        self::TYPE_BUY_6 => '75%用户选择',
    ];

    /** 权益包类型--基础包 */
    const EQUITY_PACKAGE_TYPE_BASICS      = 1;
    const EQUITY_PACKAGE_TYPE_BASICS_TEXT = '基础包';
    /** 权益包类型--活动包 */
    const EQUITY_PACKAGE_TYPE_ACTIVITY      = 2;
    const EQUITY_PACKAGE_TYPE_ACTIVITY_TEXT = '活动包';
    const EQUITY_PACKAGE_TYPE_LIST_TEXT     = [
        self::EQUITY_PACKAGE_TYPE_BASICS   => self::EQUITY_PACKAGE_TYPE_BASICS_TEXT,
        self::EQUITY_PACKAGE_TYPE_ACTIVITY => self::EQUITY_PACKAGE_TYPE_ACTIVITY_TEXT,
    ];

    const VIP_CID         = 1;
    const COMPETITIVE_CID = 2;

    /** 特殊套餐包 */
    const SPECIAL_PACKAGE = [
        3,
        8,
        9,
        10,
    ];

    // 这里的数据是用于给前端展示的购买的
    public static function getPackageListByCidForBuy($cid, $resumeId = 0, $isActivity = false)
    {
        if ($cid == self::COMPETITIVE_CID) {
            $orderBy = [// 将特定订单 ID 排在最前面
                        // new Expression("equity_package_id = 6 DESC"),
                        'days' => SORT_DESC,
                        // 还可以添加其他排序条件
            ];
        } else {
            $orderBy = [
                // 将特定订单 ID 排在最前面
                // new Expression("equity_package_id = 6 DESC"),
                'days' => SORT_ASC,
                // 还可以添加其他排序条件
            ];
        }

        $query = self::find()
            ->select([
                'id as equity_package_id',
                'equity_package_category_id',
                '0 + CAST(original_amount AS CHAR) AS original_amount',
                '0 + CAST(real_amount AS CHAR) AS real_amount',
                'name',
                'subname',
                'days',
                'buy_desc',
                'buy_type',
            ])
            ->where([
                'equity_package_category_id' => $cid,
                'status'                     => self::STATUS_ONLINE,
            ]);
        if ($isActivity) {
            $query->andWhere(['equity_package_type' => self::EQUITY_PACKAGE_TYPE_ACTIVITY]);
        } else {
            $query->andWhere(['equity_package_type' => self::EQUITY_PACKAGE_TYPE_BASICS]);
        }
        $list           = $query->orderBy($orderBy)
            ->asArray()
            ->all();
        $is_upgrade     = false;
        $is_gold_vip    = false;
        $is_diamond_vip = false;
        if ($resumeId > 0) {
            //不是游客
            //获取简历信息
            $resumeInfo = BaseResume::findOne($resumeId);
            if ($resumeInfo->vip_level == BaseResume::VIP_LEVEL_GOLD && $cid == BaseResumeEquityPackageCategorySetting::ID_DIAMOND_VIP) {
                //黄金会员且购买的是钻石会员
                $is_upgrade = true;
            }
            $is_gold_vip    = $resumeInfo->vip_type == BaseResume::VIP_TYPE_ACTIVE && $resumeInfo->vip_level == BaseResume::VIP_LEVEL_GOLD;
            $is_diamond_vip = $resumeInfo->vip_type == BaseResume::VIP_TYPE_ACTIVE && $resumeInfo->vip_level == BaseResume::VIP_LEVEL_DIAMOND;
        }

        // 这里需要有一个最低升级费用的参数
        $vipLowestUpgradeAmount = 0;
        $goldRemainDays         = 0;
        $jobFastMaxSaveAmount   = 0;
        foreach ($list as &$v) {
            $v['buy_type_txt'] = self::TYPE_BUY_LIST[$v['buy_type']] ?? '';
            //权益项
            $equity_list = BaseResumeEquitySetting::getEquityDetailList(BaseResumeEquityPackageCategorySetting::PACKAGE_EQUITY_CATEGORY_EQUITY_DETAIL[$v['equity_package_category_id']]);
            if (in_array($v['equity_package_id'], self::SPECIAL_PACKAGE)) {
                array_push($equity_list, BaseResumeEquitySetting::ID_GAOCAIYOUKE_ITEM);
            }
            foreach ($equity_list as &$item) {
                $item['description'] = BaseResumeEquitySetting::getEquityDescription($item['id'], $v['days']);

                // 1.9 版本添加字段,singlePrice和icon
                $item['icon'] = BaseResumeEquitySetting::getIcon($item['id']);
                // 计算单个价格
                $item['singlePrice'] = BaseResumeEquitySetting::getSinglePrice($item['id'], $v['days']);
                // 拼接
                $item['singlePrice'] = $item['singlePrice'] ? '¥' . $item['singlePrice'] : '';
            }

            $v['equity_list'] = $equity_list;
            //是否是升级资源包
            $v['is_upgrade']   = $is_upgrade;
            $v['upgrade_data'] = [];
            if ($is_upgrade) {
                //登录了 看一下是不是黄金会员且当前获取的是钻石套餐包
                $v['upgrade_data'] = self::goldConvertDiamond($resumeId, $v['equity_package_id']);
                if (!$vipLowestUpgradeAmount) {
                    $vipLowestUpgradeAmount = $v['upgrade_data']['real_amount'];
                } else {
                    $vipLowestUpgradeAmount = $v['upgrade_data']['real_amount'] < $vipLowestUpgradeAmount ? $v['upgrade_data']['real_amount'] : $vipLowestUpgradeAmount;
                }

                $goldRemainDays = $v['upgrade_data']['gold_remain_days'];
            }

            if ($cid == BaseResumeEquityPackageCategorySetting::ID_DIAMOND_VIP) {
                $v['resume_top_number']   = BaseResumeEquitySetting::ID_NEED_CONFIG_RESOURCES_NUM[BaseResumeEquitySetting::ID_RESUME_TOP][$v['days']];
                $v['delivery_top_number'] = BaseResumeEquitySetting::ID_NEED_CONFIG_RESOURCES_NUM[BaseResumeEquitySetting::ID_DELIVERY_TOP][$v['days']];
            }

            $v['daily_amount'] = floor($v['real_amount'] / $v['days'] * 100) / 100;
            //计算优惠金额 划线价-真实价格(处理小数点精度问题)
            $v['discount_amount'] = strval($v['original_amount'] - $v['real_amount']);
            if ($cid == BaseResumeEquityPackageCategorySetting::ID_JOB_FAST) {
                // 当前套餐可以节省的解挂
                $jobFastMaxSaveAmount = $v['discount_amount'] > $jobFastMaxSaveAmount ? $v['discount_amount'] : $jobFastMaxSaveAmount;
            }

            // 1.9版本添加字段discountTxt和timesAmount
            // discountTxt 折扣=售价/划线价*10，保留1位小数；
            $v['discountTxt'] = ((floor(($v['real_amount'] / $v['original_amount']) * 100)) / 10) . '折';
            // 单次价格=售价/(套餐天数*20)，保留2位小数；
            $v['timesAmount'] = number_format($v['real_amount'] / ($v['days'] * 20), 2);
        }

        // 这里有一串特别的逻辑,注意不要乱动这一块,会涉及到很多文案类的,并且PC和H5和小程序也不太一样(主要是用于挽留使用的)
        foreach ($list as &$ite) {
            $detainment = [];
            switch (PLATFORM) {
                case 'PC':
                    // 看类型
                    // 购买黄金
                    if ($cid == BaseResumeEquityPackageCategorySetting::ID_GOLD_VIP) {
                        $detainment['title']       = '您可以<span class="remind">¥' . $ite['daily_amount'] . '/天</span>开通黄金VIP特权';
                        $detainment['subTitle']    = '预计可省<span class="remind">¥' . $ite['discount_amount'] . '</span>，确认放弃该优惠吗？';
                        $detainment['lineTxt']     = '开通后您将获得';
                        $detainment['contentList'] = [
                            [
                                'icon'     => 'https://img.gaoxiaojob.com/uploads/resume_equity_package/detainment/competitiveness_analysis.png',
                                'title'    => '竞争力分析',
                                'subTitle' => '人岗智能匹配，洞悉你的竞争优势',
                            ],
                            [
                                'icon'     => 'https://img.gaoxiaojob.com/uploads/resume_equity_package/detainment/advanced_screening.png',
                                'title'    => '高级筛选',
                                'subTitle' => '一键筛选有编制、低热度岗，求职更高效',
                            ],
                            [
                                'icon'     => 'https://img.gaoxiaojob.com/uploads/resume_equity_package/detainment/resume_template.png',
                                'title'    => '简历模板',
                                'subTitle' => '专业模板免费用，让简历脱颖而出',
                            ],

                        ];
                    }

                    if ($cid == BaseResumeEquityPackageCategorySetting::ID_DIAMOND_VIP) {
                        // 黄金升级钻石
                        if ($is_upgrade) {
                            $detainment['title']       = '恭喜您，最低<span class="remind">¥' . $vipLowestUpgradeAmount . '</span>可升级钻石VIP';
                            $detainment['subTitle']    = '<span class="remind">' . $goldRemainDays . '</span>天后将失去该优惠';
                            $detainment['lineTxt']     = '钻石VIP还可享以下特权';
                            $detainment['contentList'] = [
                                [
                                    'icon'     => 'https://img.gaoxiaojob.com/uploads/resume_equity_package/detainment/resume_top.png',
                                    'title'    => '简历置顶',
                                    'subTitle' => '单位搜索优先展示，简历曝光提升6倍',
                                ],
                                [
                                    'icon'     => 'https://img.gaoxiaojob.com/uploads/resume_equity_package/detainment/delivery_top.png',
                                    'title'    => '投递置顶',
                                    'subTitle' => '应聘列表置顶曝光，求职更胜一筹',
                                ],
                                [
                                    'icon'     => 'https://img.gaoxiaojob.com/uploads/resume_equity_package/detainment/resume_refresh.png',
                                    'title'    => '简历刷新',
                                    'subTitle' => '保持简历活跃状态，收获更多邀约机会',
                                ],

                            ];
                        } else {
                            // 普通购买
                            $detainment['title']       = '您可以<span class="remind">¥' . $ite['daily_amount'] . '/天</span>开通钻石VIP特权';
                            $detainment['subTitle']    = '预计可省<span class="remind">¥' . $ite['discount_amount'] . '</span>，确认放弃该优惠吗？';
                            $detainment['lineTxt']     = '开通后您将获得';
                            $detainment['contentList'] = [
                                [
                                    'icon'     => 'https://img.gaoxiaojob.com/uploads/resume_equity_package/detainment/resume_exposure.png',
                                    'title'    => '简历曝光6倍提升',
                                    'subTitle' => '简历排名靠前，抢占更多单位关注',
                                ],
                                [
                                    'icon'     => 'https://img.gaoxiaojob.com/uploads/resume_equity_package/detainment/job_efficiency.png',
                                    'title'    => '求职效率提高210%',
                                    'subTitle' => '优质职位抢先投，单位反馈提升80%',
                                ],
                                [
                                    'icon'     => 'https://img.gaoxiaojob.com/uploads/resume_equity_package/detainment/job_competitiveness.png',
                                    'title'    => '求职竞争力加倍提升',
                                    'subTitle' => '多维人岗适配分析，洞悉竞争优势',
                                ],

                            ];
                        }
                    }

                    // 求职快
                    if ($cid == BaseResumeEquityPackageCategorySetting::ID_JOB_FAST) {
                        $detainment['title']        = '最高立减<span class="remind">¥' . $jobFastMaxSaveAmount . '</span>享求职快特权';
                        $detainment['subTitle']     = '特权加量更超值，确定放弃优惠吗？';
                        $detainment['contentImage'] = 'https://img.gaoxiaojob.com/uploads/resume_equity_package/detainment/job_hunting_fast.png';
                    }

                    // 竞争力洞察
                    if ($cid == BaseResumeEquityPackageCategorySetting::ID_INSIGHT) {
                        $detainment['title']        = '低至<span class="remind">¥' . $list[0]['daily_amount'] . '/天</span>可享竞争力洞察特权';
                        $detainment['subTitle']     = '确定放弃优惠吗？';
                        $detainment['contentImage'] = 'https://img.gaoxiaojob.com/uploads/resume_equity_package/detainment/job_hunting_competitiveness.png';
                    }

                    break;
                default:
                    break;
            }

            $ite['detainment'] = $detainment;
        }

        $data = [
            'list'           => $list,
            'is_gold_vip'    => $is_gold_vip,
            'is_diamond_vip' => $is_diamond_vip,
        ];

        if ($resumeId) {
            // 是否没有关闭过当前的权益包
            $data['isFirstClose'] = self::getIsFirstClose($v['equity_package_category_id'], $resumeId);
        }

        return $data;
    }

    public static function getMorePackageForInsight()
    {
        // 30天黄金的id
        $package30DaySettingId = BaseResumeEquityPackageCategorySetting::ID_SETTING_30_DAY_GOLD_VIP;
        // 拿信息
        $data = BaseResumeEquityPackageSetting::findOne($package30DaySettingId);

        $detainment                 = [];
        $detainment['title']        = '低至<span class="remind">¥2.6/天</span>可享竞争力洞察特权';
        $detainment['subTitle']     = '确定放弃优惠吗？';
        $detainment['contentImage'] = 'https://img.gaoxiaojob.com/uploads/resume_equity_package/detainment/job_hunting_fast.png';

        // 这里也是特殊处理
        $return = [
            'name'            => $data->name,
            // 保留两位小数,后面的0去掉
            'realAmount'      => floor($data->real_amount * 100) / 100,
            'equityPackageId' => $data->id,
            'desc'            => '享竞争力洞察、编制筛选、简历模板等8+权益',
            'popTextList'     => [
                '竞争力分析*30天',
                '公告热度分析*30天',
                '编制、热度高级筛选',
                '简历模板免费用',
                '浏览足迹',
                '收藏查看数量不限',
                '求职资源包',
                '会员专属标识',
            ],
            'detainment'      => $detainment,

        ];

        return $return;
    }

    public static function getDetainmentForMini($cid, $resumeId)
    {
        // 根据不同的商品id,获取到对应的套餐,再根据套餐来出信息
        $cModel = BaseResumeEquityPackageSetting::findOne($cid);
        // 获取套餐信息
        $packageCategoryId = $cModel->equity_package_category_id;

        $content = [
            'missTitle'               => '',
            'listContent'             => [],
            'isFirstClose'            => self::getIsFirstClose($packageCategoryId, $resumeId),
            'equityPackageCategoryId' => $packageCategoryId,
        ];
        switch ($packageCategoryId) {
            case BaseResumeEquityPackageCategorySetting::ID_GOLD_VIP:
                $content['missTitle']   = '提升求职效率';
                $content['listContent'] = [
                    [
                        'title'    => '知己知彼，掌握竞争情报',
                        'subTitle' => '一键获取职位竞争力分析报告',
                    ],
                    [
                        'title'    => '提升优质信息查找效率',
                        'subTitle' => '一键查编制、筛热度，求职快人一步',
                    ],
                    [
                        'title'    => '精美简历模板免费用',
                        'subTitle' => '专业吸睛，让你的简历脱颖而出',
                    ],

                ];
                break;
            case BaseResumeEquityPackageCategorySetting::ID_DIAMOND_VIP:
                $content['missTitle']   = '求职效果翻倍';
                $content['listContent'] = [
                    [
                        'title'    => '简历曝光6倍提升',
                        'subTitle' => '置顶提升排名，收获更多反馈',
                    ],
                    [
                        'title'    => '知己知彼，掌握竞争情报',
                        'subTitle' => '一键获取职位竞争力分析报告',
                    ],
                    [
                        'title'    => '提升优质信息查找效率',
                        'subTitle' => '一键查编制、筛热度，求职快人一步',
                    ],

                ];

                break;

            case BaseResumeEquityPackageCategorySetting::ID_INSIGHT:
                $content['missTitle']   = '提升求职胜算';
                $content['listContent'] = [
                    [
                        'title'    => '多维报告解析，挖掘自身优势',
                        'subTitle' => '人岗适配分析，一键获取职位匹配度',
                    ],
                    [
                        'title'    => '实时热度追踪，最佳时机早知道',
                        'subTitle' => '跟投热门岗or捡漏冷门岗，错峰投递机会大',
                    ],
                    [
                        'title'    => '知己知彼，掌握竞争情报',
                        'subTitle' => '洞悉竞争态势&同行动态，让投递更有底气',
                    ],
                ];
                break;

            case BaseResumeEquityPackageCategorySetting::ID_JOB_FAST:
                $content['missTitle']   = '曝光迅速提升';
                $content['listContent'] = [
                    [
                        'title'    => '简历排名靠前',
                        'subTitle' => '单位搜索优先展示，简历曝光提升6倍',
                    ],
                    [
                        'title'    => '更多投递反馈',
                        'subTitle' => '投递置顶，多渠道通知单位，反馈速度提升',
                    ],
                    [
                        'title'    => '职位邀约翻倍',
                        'subTitle' => '收获更多单位关注，求职效率提升210%',
                    ],

                ];
                break;
        }

        return $content;
    }

    /**
     * 通过组合分类获取组合列表
     */
    public static function getPackageListByCid($cid)
    {
        $list = self::find()
            ->select([
                'id',
                'equity_package_category_id',
                '0 + CAST(original_amount AS CHAR) AS original_amount',
                '0 + CAST(real_amount AS CHAR) AS real_amount',
                'name',
                'subname',
                'days',
                'buy_desc',
                'buy_type',
            ])
            ->where([
                'equity_package_category_id' => $cid,
                'status'                     => self::STATUS_ONLINE,
            ])
            ->with([
                'resumeEquityPackageRelationSetting.resumeEquitySetting',
            ])
            ->andWhere(['equity_package_type' => self::EQUITY_PACKAGE_TYPE_BASICS])
            ->orderBy('days asc')
            ->asArray()
            ->all();

        foreach ($list as &$v) {
            $v['buy_type_txt']      = self::TYPE_BUY_LIST[$v['buy_type']] ?? '';
            $v['equity_package_id'] = $v['id'];
            $v['daily_amount']      = floor($v['real_amount'] / $v['days'] * 100) / 100;
            if ($cid == BaseResumeEquityPackageCategorySetting::ID_DIAMOND_VIP) {
                $v['equity_text']['resume_top_number']   = BaseResumeEquitySetting::ID_NEED_CONFIG_RESOURCES_NUM[BaseResumeEquitySetting::ID_RESUME_TOP][$v['days']];
                $v['equity_text']['delivery_top_number'] = BaseResumeEquitySetting::ID_NEED_CONFIG_RESOURCES_NUM[BaseResumeEquitySetting::ID_DELIVERY_TOP][$v['days']];
            }
            unset($v['id']);
        }

        return $list;
    }

    /**
     * 通过组合分类获取组合列表
     * 活动
     */
    public static function getActivityPackageListByCid($cid)
    {
        $list = self::find()
            ->select([
                'id',
                'equity_package_category_id',
                '0 + CAST(original_amount AS CHAR) AS original_amount',
                '0 + CAST(real_amount AS CHAR) AS real_amount',
                'name',
                'subname',
                'days',
                'buy_desc',
                'buy_type',
            ])
            ->with([
                'resumeEquityPackageRelationSetting.resumeEquitySetting',
            ])
            ->where([
                'equity_package_category_id' => $cid,
                'status'                     => self::STATUS_ONLINE,
            ])
            ->andWhere(['equity_package_type' => self::EQUITY_PACKAGE_TYPE_ACTIVITY])
            ->orderBy('days asc')
            ->asArray()
            ->all();

        foreach ($list as &$v) {
            $v['buy_type_txt']      = self::TYPE_BUY_LIST[$v['buy_type']] ?? '';
            $v['equity_package_id'] = $v['id'];
            $v['daily_amount']      = floor($v['real_amount'] / $v['days'] * 100) / 100;
            if ($cid == BaseResumeEquityPackageCategorySetting::ID_DIAMOND_VIP) {
                $v['equity_text']['resume_top_number']   = BaseResumeEquitySetting::ID_NEED_CONFIG_RESOURCES_NUM[BaseResumeEquitySetting::ID_RESUME_TOP][$v['days']];
                $v['equity_text']['delivery_top_number'] = BaseResumeEquitySetting::ID_NEED_CONFIG_RESOURCES_NUM[BaseResumeEquitySetting::ID_DELIVERY_TOP][$v['days']];
            }
            unset($v['id']);
        }

        return $list;
    }

    /**
     * 通过组合分类获取组合列表id
     */
    public static function getPackageIdsByCid($cid)
    {
        $ids = self::find()
            ->select('id')
            ->where(['equity_package_category_id' => $cid])
            ->column();

        return $ids;
    }

    /**
     * 获取产品组合详情
     * @param $id
     * @return array|\yii\db\ActiveRecord|null
     */
    public static function getDetail($id)
    {
        return self::find()
            ->with([
                'resumeEquityPackageRelationSetting.resumeEquitySetting',
                'resumeEquityPackageCategorySetting',
            ])
            ->where(['id' => $id])
            ->asArray()
            ->one();
    }

    /**
     * 产品组合关系
     */
    public function getResumeEquityPackageRelationSetting()
    {
        return $this->hasMany(BaseResumeEquityPackageRelationSetting::class, ['equity_package_id' => 'id'])
            ->select([
                'id',
                'equity_package_id',
                'equity_id',
            ]);
    }

    /**
     * 产品与产品类型-一对一关系
     */
    public function getResumeEquityPackageCategorySetting()
    {
        return $this->hasOne(BaseResumeEquityPackageCategorySetting::class, ['id' => 'equity_package_category_id'])
            ->select([
                'id',
                'name',
            ]);
    }

    /**
     * 黄金升级钻石折算方式
     */
    public static function goldConvertDiamond($resumeId, $equityPackageId)
    {
        //获取黄金会员权益--利用黄金包过期时间都是一致的
        $goldEquityPackageRow = BaseResumeEquityPackage::find()
            ->where([
                'resume_id'           => $resumeId,
                'package_category_id' => BaseResumeEquityPackageCategorySetting::ID_GOLD_VIP,
                'expire_status'       => BaseResumeEquityPackage::STATUS_EXPIRE,
            ])
            ->orderBy('expire_time desc')
            ->one();
        //获取所有在生效的黄金会员权益
        $goldEquityPackageDetailAll = BaseResumeEquityPackageDetail::find()
            ->select([
                'resume_id',
                'package_category_id',
                'equity_id',
                'equity_id',
                'begin_time',
                'expire_time',
                'expire_status',
                'order_id',
            ])
            ->where([
                'resume_id'           => $resumeId,
                'package_category_id' => BaseResumeEquityPackageCategorySetting::ID_GOLD_VIP,
                'expire_status'       => BaseResumeEquityPackageDetail::STATUS_EXPIRE,
            ])
            ->orderBy('expire_time desc')
            ->asArray()
            ->all();
        if (count($goldEquityPackageDetailAll) <= 0) {
            return false;
        }
        $goldOrderIds = array_column($goldEquityPackageDetailAll, 'order_id');
        //获取黄金会员订单信息
        $goldOrderRows = BaseResumeOrder::find()
            ->select([
                'id',
                'real_amount',
            ])
            ->where([
                'id'     => $goldOrderIds,
                'status' => BaseResumeOrder::STATUS_PAID,
            ])
            ->orderBy('pay_time desc')
            ->all();
        //订单总金额
        $goldOrderAmount = array_sum(array_column($goldOrderRows, 'real_amount'));
        //订单服务快照-总天数
        $goldOrderSnapshotServiceDays = BaseResumeOrderSnapshot::find()
            ->where([
                'order_id' => $goldOrderIds,
            ])
            ->sum('service_days');
        //生效黄金会员每天单价，保留两位小数(直接舍弃小数点后第三位)
        $goldPrice = floor($goldOrderAmount / $goldOrderSnapshotServiceDays * 100) / 100;
        //计算剩余的黄金的时间(保留整数天)--注意：因为不存在预售，所以时间是连续的或者的交叉的
        $goldRemainDays = floor((strtotime($goldEquityPackageRow['expire_time']) - time()) / 86400);
        //升级到钻石VIP应当抵扣金额
        $convertPrice     = $goldRemainDays * $goldPrice;
        $realOfferPrice   = $convertPrice;
        $equityPackageRow = BaseResumeEquityPackageSetting::findOne(['id' => $equityPackageId]);
        $real_amount      = $equityPackageRow->real_amount;
        if ($convertPrice > 0) {
            //抵扣金额大于等于升级钻石VIP的价格时候会导致下单金额小于或等于0，下单会出错，所以需要特殊处理一下
            if ($real_amount <= $convertPrice) {
                $real_amount  = 0.01;
                $convertPrice = $equityPackageRow['real_amount'] - 0.01;
            } else {
                $real_amount = $equityPackageRow['real_amount'] - $convertPrice;
            }
        }

        return [
            //黄金会员剩余天数
            'gold_remain_days' => strval($goldRemainDays),
            //真实能抵扣的金额
            'real_offer_price' => strval($realOfferPrice),
            //实际抵扣金额
            'convert_price'    => strval($convertPrice),
            //真实价格
            'real_amount'      => strval($real_amount),
        ];
    }

    /**
     *  个人中心底部vip广告卡片
     * @param $resumeId
     * @return array
     */
    public static function getPersonCenterVipCardInfo($resumeId)
    {
        //、当用户每天首次访问【个人中心】页面时，满足一定条件时，按优先级显示提示栏：
        //（1）提示优先级：①>②>③>④
        //（2）提示内容：
        //① 【服务即将过期-黄金VIP提示】：当求职者的黄金VIP套餐有效天数<7天 时，显示该样式提示栏；
        //②【服务即将过期-其他服务 提示】：当求职者的钻石VIP、求职快套餐有效天数<7天，或竞争力洞察套餐有效天数<3天时，显示该样式提示栏；
        //③【服务过期召回 提示】当求职者最近一次过期的VIP套餐，过期天数=100以内7的倍数时，显示该样式提示栏；
        //④【VIP服务开通 提示】非情况①②③ 时，显示该样式提示栏；
        $info['showTime'] = 5;
        $info['title']    = '想让招聘单位优先查看您的消息？';
        $info['content']  = '升级VIP尊享<span>11+</span>求职权益，助您求职快人一步';
        $info['btnText']  = '了解详情';
        $info['label']    = '';
        $info['url']      = '/vip.html';
        if (!$resumeId) {
            return $info;
        }

        //判断求职者是否购买过vip服务
        $historyPackageRow = BaseResumeEquityPackage::find()
            ->where([
                'resume_id' => $resumeId,
            ])
            ->one();
        if (empty($historyPackageRow)) {
            //不存在历史记录，直接返回
            return $info;
        }

        //细分具体是购买了什么业务
        $memberVipType = BaseResume::findOneVal(['id' => $resumeId], 'vip_type');
        if ($memberVipType != BaseResume::VIP_TYPE_EXPIRE) {
            //1、 【服务即将过期-黄金VIP提示】：当求职者的黄金VIP套餐有效天数<7天 时，显示该样式提示栏；
            $goldEquityPackageRow = BaseResumeEquityPackage::find()
                ->select(['expire_time as expireTime'])
                ->where([
                    'resume_id'           => $resumeId,
                    'package_category_id' => BaseResumeEquityPackageCategorySetting::ID_GOLD_VIP,
                    'expire_status'       => BaseResumeEquityPackage::STATUS_EXPIRE,
                ])
                ->orderBy('expire_time desc')
                ->asArray()
                ->one();

            if ($goldEquityPackageRow) {
                $goldRemainDays = BaseResumeEquityPackage::getExpireDays(BaseResumeEquityPackageCategorySetting::ID_GOLD_VIP,
                    $resumeId, 7);
                if ($goldRemainDays) {
                    //1、N=当前黄金VIP的剩余有效天数；
                    //2、最低¥xx：即显示钻石VIP•30天套餐的补价金额（保留2位小数）：最低¥xx(即补价金额)=钻石VIP•30套餐售价 - 当前黄金VIP套餐抵扣余额；
                    //3、点击“去升级”，新页面打开【VIP介绍页】；
                    //获取钻石vip30天的packId
                    $diamondPackageId = self::findOneVal([
                        'status'                     => self::STATUS_ACTIVE,
                        'equity_package_category_id' => BaseResumeEquityPackageCategorySetting::ID_DIAMOND_VIP,
                        'days'                       => 30,
                    ], 'id');
                    $updateInfo       = self::goldConvertDiamond($resumeId, $diamondPackageId);
                    $info['title']    = '您的黄金VIP服务将在<span>' . $goldRemainDays . '天</span>后过期';
                    $info['content']  = '黄金VIP限时优惠，最低<span>¥' . $updateInfo['real_amount'] . '</span>升级钻石VIP';
                    $info['label']    = '升级特惠';
                    $info['btnText']  = '去升级';

                    return $info;
                }
            }
            //2、服务即将过期-其他服务 提示】：当求职者的钻石VIP、求职快套餐有效天数<7天，或竞争力洞察套餐有效天数<3天时，显示该样式提示栏；
            $otherEquityPackageRow = BaseResumeEquityPackage::find()
                ->select([
                    'expire_time as expireTime',
                    'package_category_id as packageCategoryId',
                ])
                ->where([
                    'resume_id'           => $resumeId,
                    'package_category_id' => [
                        BaseResumeEquityPackageCategorySetting::ID_INSIGHT,
                        BaseResumeEquityPackageCategorySetting::ID_DIAMOND_VIP,
                        BaseResumeEquityPackageCategorySetting::ID_JOB_FAST,
                    ],
                    'expire_status'       => BaseResumeEquityPackage::STATUS_EXPIRE,
                ])
                ->orderBy('expire_time desc')
                ->asArray()
                ->all();
            //默认排序
            $sortList          = [
                BaseResumeEquityPackageCategorySetting::ID_DIAMOND_VIP => 1,
                BaseResumeEquityPackageCategorySetting::ID_JOB_FAST    => 2,
                BaseResumeEquityPackageCategorySetting::ID_INSIGHT     => 3,
            ];
            $remainPackageList = [];
            foreach ($otherEquityPackageRow as $item) {
                //判断是否有钻石、求职快、竞争力洞察要过期的
                $remainDays         = BaseResumeEquityPackage::countResidueDaysByExpireTime($item['expireTime']);
                $isDiamondOrJobFast = in_array($item['packageCategoryId'], [
                    BaseResumeEquityPackageCategorySetting::ID_DIAMOND_VIP,
                    BaseResumeEquityPackageCategorySetting::ID_JOB_FAST,
                ]);
                if (($remainDays < 7 && $isDiamondOrJobFast) || ($remainDays < 3 && $item['packageCategoryId'] == BaseResumeEquityPackageCategorySetting::ID_INSIGHT)) {
                    $remainPackageList[] = [
                        'packageCategoryId' => $item['packageCategoryId'],
                        'remainDays'        => $remainDays,
                        'sort'              => $sortList[$item['packageCategoryId']],
                    ];
                }
            }

            //没有要过期的，返回空
            if (empty($remainPackageList)) {
                return [];
            }

            $sort = array_column($remainPackageList, 'sort');
            array_multisort($sort, SORT_ASC, $remainPackageList);
            foreach ($remainPackageList as $item) {
                $remainDaysText    = $item['remainDays'] == 0 ? '今' : $item['remainDays'];
                $packageCateName   = BaseResumeEquityPackageCategorySetting::VIP_TYPE_TEXT_LIST[$item['packageCategoryId']];
                $lowestPackageInfo = BaseResumeEquityPackageSetting::getLowestPricePackageInfo($item['packageCategoryId']);
                $url               = ResumeEquityPackageCategorySetting::VIP_URL_LIST[$item['packageCategoryId']];

                $info['title']   = '您的' . $packageCateName . '服务将在<span>' . $remainDaysText . '天</span>后过期，请及时使用资源';
                $info['content'] = '最低<span>¥' . $lowestPackageInfo['realAmount'] . '</span>可继续享受专属权益';
                $info['label']   = $lowestPackageInfo['discount'] . '折';
                $info['btnText'] = '查看详情';
                $info['url']     = $url;

                return $info;
            }
        }
        if ($memberVipType == BaseResume::VIP_TYPE_EXPIRE) {
            //【服务过期召回 提示】当求职者最近一次过期的VIP套餐，过期天数=100以内7的倍数时，显示该样式提示栏；
            $lastEquityPackageRow = BaseResumeEquityPackage::find()
                ->select([
                    'expire_time as expireTime',
                    'package_category_id as packageCategoryId',
                ])
                ->where([
                    'resume_id'           => $resumeId,
                    'package_category_id' => [
                        BaseResumeEquityPackageCategorySetting::ID_DIAMOND_VIP,
                        BaseResumeEquityPackageCategorySetting::ID_GOLD_VIP,
                    ],
                    'expire_status'       => BaseResumeEquityPackage::STATUS_EXPIRED,
                ])
                ->orderBy('expire_time desc')
                ->asArray()
                ->one();
            if ($lastEquityPackageRow) {
                $remainDays = BaseResumeEquityPackage::countExpiredDays($lastEquityPackageRow['expireTime']);
                if ($remainDays > 0 && $remainDays < 100 && $remainDays % 7 == 0) {
                    $vipType            = BaseResumeEquityPackageCategorySetting::VIP_TYPE_TEXT_LIST[$lastEquityPackageRow['packageCategoryId']];
                    $packageLowestPrice = self::getVipLowestDaysPrice($lastEquityPackageRow['packageCategoryId']);
                    $info['title']      = '您的' . $vipType . '已过期<span>' . $remainDays . '天</span>';
                    $info['content']    = '低至<span>¥' . $packageLowestPrice . '/天</span>开通VIP特权，继续享受专属权益';
                    $info['btnText']    = '去开通';

                    return $info;
                }
            }
        }

        return $info;
    }

    /**
     * 求某个类型套餐的日最低价
     * @param $packageCateId
     * @return int|mixed
     */
    public static function getVipLowestDaysPrice($packageCateId)
    {
        $list         = self::find()
            ->where([
                'equity_package_category_id' => $packageCateId,
                'status'                     => self::STATUS_ACTIVE,
                'equity_package_type'        => self::EQUITY_PACKAGE_TYPE_BASICS,
            ])
            ->select([
                'real_amount as realAmount',
                'days',
            ])
            ->asArray()
            ->all();
        $dayPrice     = 0;
        $dayPriceList = [];
        if (!empty($list)) {
            foreach ($list as $item) {
                $price = floor($item['realAmount'] / $item['days'] * 100) / 100;
                array_push($dayPriceList, $price);
            }
            $dayPrice = min($dayPriceList);
        }

        return $dayPrice;
    }

    /**
     * 获取最低价套餐信息
     * @param $packageCateId
     * @return array|\yii\db\ActiveRecord|null
     */
    public static function getLowestPricePackageInfo($packageCateId)
    {
        $info               = self::find()
            ->where([
                'equity_package_category_id' => $packageCateId,
                'status'                     => self::STATUS_ACTIVE,
                'equity_package_type'        => self::EQUITY_PACKAGE_TYPE_BASICS,
            ])
            ->select([
                'real_amount as realAmount',
                '0 + CAST(original_amount AS CHAR) AS originalAmount',
                'equity_package_category_id as equityPackageCategoryId',
                'days',
                'subname',
            ])
            ->orderBy('real_amount asc')
            ->asArray()
            ->one();
        $info['realAmount'] = floor($info['realAmount'] * 100) / 100;
        $info['discount']   = floor($info['realAmount'] / $info['originalAmount'] * 100) / 100 * 10;

        return $info;
    }

    /**
     * 求某个类型套餐的最低价
     * @param $packageCateId
     * @return mixed
     */
    public static function getPackageLowestPrice($packageCateId)
    {
        $info = self::find()
            ->where([
                'equity_package_category_id' => $packageCateId,
                'status'                     => self::STATUS_ACTIVE,
                'equity_package_type'        => self::EQUITY_PACKAGE_TYPE_BASICS,
            ])
            ->select([
                'real_amount as realAmount',
            ])
            ->orderBy('real_amount asc')
            ->asArray()
            ->one();

        return floor($info['realAmount'] * 100) / 100;
    }

    // 来源1.9版本需求,需要点击购买后,记录用户是否是首次关闭
    public static function getIsFirstClose($equityPackageCategoryId, $resumeId)
    {
        $baseCacheKey = Cache::RESUME_EQUITY_CLOSE_KEY;
        $today        = date('Y-m-d');
        $platform     = PLATFORM;
        $cacheKey     = $baseCacheKey . ':' . $resumeId . ':' . $platform . $equityPackageCategoryId . ':' . $today;
        // 先从缓存中获取
        $isFirstClose = Cache::get($cacheKey);

        if (!$isFirstClose) {
            return true;
        }

        return false;
    }

    public static function setIsFirstClose($equityPackageCategoryId, $resumeId)
    {
        $baseCacheKey = Cache::RESUME_EQUITY_CLOSE_KEY;
        $today        = date('Y-m-d');
        $platform     = PLATFORM;
        $cacheKey     = $baseCacheKey . ':' . $resumeId . ':' . $platform . $equityPackageCategoryId . ':' . $today;
        // 设置过期时间是今天的23:59:59
        $expireTime = strtotime($today . ' 23:59:59') - time();
        Cache::set($cacheKey, 1, $expireTime);
    }

    /**
     * 获取求职者某一类型生效套餐vip的抵扣余额，用于升级
     * @param $resumeId
     * @param $packageCateId
     * @return false|float|int
     */
    public static function getPackageDeductionPrice($resumeId, $packageCateId)
    {
        //获取黄金会员权益--利用黄金包过期时间都是一致的
        $equityPackageRow = BaseResumeEquityPackage::find()
            ->where([
                'resume_id'           => $resumeId,
                'package_category_id' => $packageCateId,
                'expire_status'       => BaseResumeEquityPackage::STATUS_EXPIRE,
            ])
            ->orderBy('expire_time desc')
            ->one();
        //获取所有在生效的黄金会员权益
        $equityPackageDetailAll = BaseResumeEquityPackageDetail::find()
            ->select([
                'resume_id',
                'package_category_id',
                'equity_id',
                'equity_id',
                'begin_time',
                'expire_time',
                'expire_status',
                'order_id',
            ])
            ->where([
                'resume_id'           => $resumeId,
                'package_category_id' => $packageCateId,
                'expire_status'       => BaseResumeEquityPackageDetail::STATUS_EXPIRE,
            ])
            ->orderBy('expire_time desc')
            ->asArray()
            ->all();
        if (count($equityPackageDetailAll) <= 0) {
            return false;
        }
        $orderIds = array_column($equityPackageDetailAll, 'order_id');
        //获取黄金会员订单信息
        $orderRows = BaseResumeOrder::find()
            ->select([
                'id',
                'real_amount',
            ])
            ->where([
                'id'     => $orderIds,
                'status' => BaseResumeOrder::STATUS_PAID,
            ])
            ->orderBy('pay_time desc')
            ->all();
        //订单总金额
        $orderAmount = array_sum(array_column($orderRows, 'real_amount'));
        //订单服务快照-总天数
        $orderSnapshotServiceDays = BaseResumeOrderSnapshot::find()
            ->where([
                'order_id' => $orderIds,
            ])
            ->sum('service_days');
        //生效黄金会员每天单价，保留两位小数(直接舍弃小数点后第三位)
        $dailyPrice = floor($orderAmount / $orderSnapshotServiceDays * 100) / 100;
        //计算剩余的黄金的时间(保留整数天)--注意：因为不存在预售，所以时间是连续的或者的交叉的
        $goldRemainDays = floor((strtotime($equityPackageRow['expire_time']) - time()) / 86400);
        //抵扣金额
        $convertPrice = $goldRemainDays * $dailyPrice;

        return $convertPrice;
    }

}
