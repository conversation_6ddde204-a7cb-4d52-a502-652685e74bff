<?php

namespace common\base\models;

use common\helpers\DebugHelper;
use common\models\JobApplyRecord;

class BaseJobApplyRecord extends JobApplyRecord
{
    //投递方式
    const DELIVERY_WAY_PLATFORM        = 1;
    const DELIVERY_WAY_EMAIL           = 2;
    const DELIVERY_WAY_LINK            = 3;
    const DELIVERY_WAY_EMAIL_LINK      = 66;
    const DELIVERY_WAY_CUSTOM          = 99;
    const DELIVERY_WAY_PLATFORM_NAME   = '平台投递';
    const DELIVERY_WAY_EMAIL_NAME      = '邮件投递';
    const DELIVERY_WAY_LINK_NAME       = '网址投递';
    const DELIVERY_WAY_EMAIL_LINK_NAME = '邮件&网址投递';
    const DELIVERY_WAY_NAME            = [
        self::DELIVERY_WAY_PLATFORM => self::DELIVERY_WAY_PLATFORM_NAME,
        self::DELIVERY_WAY_EMAIL    => self::DELIVERY_WAY_EMAIL_NAME,
        self::DELIVERY_WAY_LINK     => self::DELIVERY_WAY_LINK_NAME,
    ];
    const DELIVERY_WAY_SELECT          = [
        self::DELIVERY_WAY_PLATFORM   => self::DELIVERY_WAY_PLATFORM_NAME,
        self::DELIVERY_WAY_EMAIL_LINK => self::DELIVERY_WAY_EMAIL_LINK_NAME,
    ];
    //人才应聘详情搜索
    //站内
    const OUTER_DELIVERY_WAY = [
        self::DELIVERY_WAY_PLATFORM => self::DELIVERY_WAY_PLATFORM_NAME,
        self::DELIVERY_WAY_EMAIL    => self::DELIVERY_WAY_EMAIL_NAME,
    ];
    //站外
    const OUTSIDE_DELIVERY_WAY = [
        self::DELIVERY_WAY_EMAIL => self::DELIVERY_WAY_EMAIL_NAME,
        self::DELIVERY_WAY_LINK  => self::DELIVERY_WAY_LINK_NAME,
    ];

    //投递类型
    const DELIVERY_TYPE_OUTSIDE = 1;//站外投递
    const DELIVERY_TYPE_INSIDE  = 2;//站内投递
    const DELIVERY_TYPE         = [
        self::DELIVERY_TYPE_OUTSIDE,
        self::DELIVERY_TYPE_INSIDE,
    ];
    const DELIVERY_TYPE_NAME    = [
        self::DELIVERY_TYPE_OUTSIDE => '站外投递',
        self::DELIVERY_TYPE_INSIDE  => '站内投递',
    ];

    //资源
    const SOURCE_AUTO         = 1;
    const SOURCE_ENTRUST      = 2;
    const SOURCE_WRITE        = 3;
    const SOURCE_AUTO_NAME    = '自主投递';
    const SOURCE_ENTRUST_NAME = '委托投递';
    const SOURCE_WRITE_NAME   = '自主录入';
    const SOURCE_LIST         = [
        self::SOURCE_AUTO    => self::SOURCE_AUTO_NAME,
        self::SOURCE_ENTRUST => self::SOURCE_ENTRUST_NAME,
        self::SOURCE_WRITE   => self::SOURCE_WRITE_NAME,
    ];
    //限制多少天不能再次投递
    const LIMIT_APPLY_DAYS_ONE = 30;
    const LIMIT_APPLY_DAYS_TWO = 180;

    //平台
    const PLATFORM_PC   = 1;
    const PLATFORM_H5   = 2;
    const PLATFORM_MINI = 3;
    const PLATFORM_APP  = 4;

    const PLATFORM_LIST = [
        self::PLATFORM_PC   => 'PC',
        self::PLATFORM_H5   => 'H5',
        self::PLATFORM_MINI => '小程序',
    ];

    /**
     * 获取职位投递状态
     * @param $resumeId
     * @param $jobId
     * @return int
     */
    public static function checkJobApplyStatus($resumeId, $jobId)
    {
        if (!$resumeId) {
            return BaseJob::JOB_APPLY_STATUS_NO;
        }
        //获取当前职位信息
        $jobInfo = BaseJob::findOne($jobId);
        //获取投递方式
        $deliveryWay = 0;
        if ($jobInfo->delivery_way > 0) {
            $deliveryWay = $jobInfo->delivery_way;
        } else {
            //有公告则获取公告信息
            if ($jobInfo->announcement_id > 0) {
                $announcementInfo = BaseAnnouncement::findOne($jobInfo->announcement_id);
                $deliveryWay      = $announcementInfo->delivery_way;
            }
        }
        $query = self::find()
            ->andWhere([
                'resume_id' => $resumeId,
                'job_id'    => $jobId,
            ])
            ->select('id');
        if ($deliveryWay == BaseJobApplyRecord::DELIVERY_WAY_LINK) {
            $query->andWhere(['delivery_way' => BaseJobApplyRecord::DELIVERY_WAY_LINK]);
            $count = $query->count();
        } else {
            //获取限制天数
            $limitTime = date('Y-m-d', strtotime('-' . self::LIMIT_APPLY_DAYS_ONE . 'day'));
            $query->andWhere([
                '>',
                'add_time',
                $limitTime,
            ]);
            $count1 = $query->count();

            // 这里有条件2，已达该职位投递次数上限（近180天投递3次）
            $limitTime = date('Y-m-d', strtotime('-' . self::LIMIT_APPLY_DAYS_TWO . 'day'));
            $count2    = self::find()
                ->andWhere([
                    'resume_id' => $resumeId,
                    'job_id'    => $jobId,
                ])
                ->select('id')
                ->andWhere([
                    '>',
                    'add_time',
                    $limitTime,
                ])
                ->count();

            $count = $count1 || $count2 >= 3;
        }

        if (!empty($count)) {
            return $deliveryWay == BaseJobApplyRecord::DELIVERY_WAY_LINK ? BaseJob::JOB_APPLY_STATUS_NO : BaseJob::JOB_APPLY_STATUS_YES;
        } else {
            return BaseJob::JOB_APPLY_STATUS_NO;
        }
    }

    /**
     * 获取单位的简历投递数据
     * @param $companyId
     * @param $resumeId
     * @return array|\yii\db\ActiveRecord[]
     */
    public static function getCompanyApplyRecord($companyId, $resumeId)
    {
        return self::find()
            ->alias('jar')
            ->select([
                'ja.id as apply_id',
                'ja.status',
                'jar.add_time',
                'jar.resume_id',
                'j.name as job_name',
                'ja.resume_attachment_id',
                'ja.stuff_file_id',
            ])
            ->leftJoin(['ja' => BaseJobApply::tableName()], 'ja.id=jar.apply_id')
            ->leftJoin(['j' => BaseJob::tableName()], 'jar.job_id=j.id')
            ->andWhere([
                'jar.company_id' => $companyId,
                'jar.resume_id'  => $resumeId,
            ])
            ->andWhere([
                '>',
                'jar.apply_id',
                0,
            ])
            ->orderBy('jar.add_time desc')
            ->asArray()
            ->all();
    }

    /***
     * 获取30天内被该职位投递过最近的一条数据
     * @param $jobId
     * @param $resumeId
     * @return array|\yii\db\ActiveRecord|null
     */
    public static function getApplyInfo30($jobId, $resumeId)
    {
        return BaseJobApplyRecord::find()
            ->andWhere([
                'job_id'    => $jobId,
                'resume_id' => $resumeId,
            ])
            ->andWhere([
                '>',
                'add_time',
                date('Y-m-d H:i:s', strtotime('-30 day')),
            ])
            ->orderBy('add_time desc')
            ->asArray()
            ->one();
    }

    public static function getEmailCount($resumeId)
    {
        return self::find()
            ->andWhere([
                'resume_id'    => $resumeId,
                'delivery_way' => [
                    self::DELIVERY_WAY_EMAIL,
                ],
            ])
            ->count();
    }

    public static function getLinkCount($resumeId)
    {
        return self::find()
            ->andWhere([
                'resume_id'    => $resumeId,
                'delivery_way' => [
                    self::DELIVERY_WAY_LINK,
                ],
            ])
            ->count();
    }

    public static function updateInvite($id)
    {
        if (!$id) {
            return true;
        }
        $info = self::find()
            ->select('resume_id, job_id,add_time')
            ->where(['id' => $id])
            ->asArray()
            ->one();
        if (!$info) {
            return true;
        }

        try {
            // 找到对应的邀约全部更新？
            BaseAdminJobInvite::updateAll(['is_apply' => BaseAdminJobInvite::IS_APPLY_YES],
                'resume_id=:resume_id and job_id=:job_id and add_time<:add_time', [
                    ':resume_id' => $info['resume_id'],
                    ':job_id'    => $info['job_id'],
                    ':add_time'  => $info['add_time'],
                ]);

            BaseResumeLibraryInviteLog::updateAll(['is_apply' => BaseResumeLibraryInviteLog::IS_APPLY_YES],
                'resume_id=:resume_id and job_id=:job_id and add_time<:add_time', [
                    ':resume_id' => $info['resume_id'],
                    ':job_id'    => $info['job_id'],
                    ':add_time'  => $info['add_time'],
                ]);
        } catch (\Exception $e) {
            \Yii::error($e);
        }
    }
}