<?php

namespace common\base\models;

use common\libs\Cache;
use common\models\CompanyResumePvTotal;
use queue\Producer;

class BaseCompanyResumePvTotal extends CompanyResumePvTotal
{
    const WEIGHTING_EQUITY                = 31.1;
    const WEIGHTING_EQUITY_RAND_START     = 1.1;
    const WEIGHTING_EQUITY_RAND_END       = 3.1;
    const WEIGHTING_EQUITY_TOP            = 41.1;
    const WEIGHTING_EQUITY_TOP_RAND_START = 1.1;
    const WEIGHTING_EQUITY_TOP_RAND_END   = 4.1;

    /**
     * PV统计---按日维度统计
     */
    public static function updateDailyTotalPv($resumeId)
    {
        //修改成推进队列
        $queue = new Producer();
        $queue::companyResumePvTotal([
            'resumeId' => $resumeId,
            'date'     => date('Y-m-d'),
        ]);

        //        $date  = date('Y-m-d');
        //        $model = self::findOne([
        //            'resume_id' => $resumeId,
        //            'add_date'  => $date,
        //        ]);
        //        if (!$model) {
        //            $model              = new self();
        //            $model->resume_id   = $resumeId;
        //            $model->add_date    = $date;
        //            $model->update_time = date('Y-m-d H:i:s');
        //            $model->total       = 1;
        //            $model->save();
        //        } else {
        //            $model->updateCounters(['total' => 1]);
        //        }
    }

    /**
     * 获取指定时间段内的曝光数据
     * @param     $resumeId
     * @param int $days
     */
    public static function getPvExposure($resumeId, $days = 7)
    {
        //获取近一周的数据
        $cur_year        = date('Y');
        $cur_month       = date('m');
        $start_date      = date('Y-m-d', strtotime('-' . $days . ' day'));
        $start_timestamp = strtotime($start_date);
        $end_date        = date('Y-m-d', strtotime('-' . 1 . ' day'));

        $data = self::find()
            ->select([
                'add_date',
                'total',
            ])
            ->where(['resume_id' => $resumeId])
            ->andWhere([
                'between',
                'add_date',
                $start_date,
                $end_date,
            ])
            ->indexBy('add_date')
            ->asArray()
            ->all();

        $result = [
            'x_init'        => [],
            'x_date'        => [],
            'exposure_data' => [],
            'avg_data'      => [],
            'last_data'     => [],
        ];
        for ($i = 0; $i < $days; $i++) {
            $item_date = date('Y-m-d', strtotime('+' . $i . ' day', $start_timestamp));
            //做一个$days的缓存
            $key          = Cache::ALL_RESUME_PV_EXPOSURE . ':' . $resumeId . '_' . $item_date;
            $i_data_cache = Cache::get($key);
            if (!$i_data_cache) {
                $item_year  = date('Y', strtotime($item_date));
                $item_month = date('m', strtotime($item_date));
                if ($item_year != $cur_year) {
                    $x_date_item = date('Y年m月d日', strtotime($item_date));
                } elseif ($item_month != $cur_month) {
                    $x_date_item = date('m月d日', strtotime($item_date));
                } else {
                    $x_date_item = date('d日', strtotime($item_date));
                }
                $item_data = isset($data[$item_date]) ? $data[$item_date]['total'] : 0;
                //看看当天数据需不需要加权
                //查一下当天有没有使用置顶权益
                $is_top = BaseResumeTopConfig::isHasTop($resumeId, $item_date);
                //查看当天是否有权益
                $is_equity = BaseResumeEquityPackageDetail::isEquity($resumeId, BaseResumeEquitySetting::ID_RESUME_TOP,
                    $item_date);
                if ($is_top) {
                    //当天置顶
                    if ($item_data > 0) {
                        $item_data = round($item_data * self::WEIGHTING_EQUITY_TOP);
                    } else {
                        $item_data_rand = rand(2, 10);
                        $weight         = rand(self::WEIGHTING_EQUITY_TOP_RAND_START * 100,
                                self::WEIGHTING_EQUITY_TOP_RAND_END * 100) / 100;
                        $item_data      = round($item_data_rand * $weight);
                    }
                } elseif ($is_equity) {
                    //当天没有置顶，但是有权益
                    if ($item_data > 0) {
                        $item_data = round($item_data * self::WEIGHTING_EQUITY);
                    } else {
                        $item_data_rand = rand(2, 10);
                        $weight         = rand(self::WEIGHTING_EQUITY_RAND_START * 100,
                                self::WEIGHTING_EQUITY_RAND_END * 100) / 100;
                        $item_data      = round($item_data_rand * $weight);
                    }
                }
                $avg_item = rand(1, 5);
                $i_data   = [
                    'x_date_item' => $x_date_item,
                    'item_date'   => $item_date,
                    'item_data'   => $item_data,
                    'avg_item'    => $avg_item,
                    'last_data'   => [
                        'last_avg_data'      => $avg_item,
                        'last_exposure_data' => $item_data,
                        'x_init'             => $item_date,
                    ],
                ];
                //计算一下今天过了多少秒
                $day_second    = time() - strtotime(date('Y-m-d 00:00:00'));
                $expire_second = ($i + 1) * 86400 - $day_second;
                Cache::setex($key, $expire_second, json_encode($i_data));
            } else {
                $i_data = json_decode($i_data_cache, true);
            }
            array_push($result['x_date'], $i_data['x_date_item']);
            array_push($result['x_init'], $i_data['item_date']);
            array_push($result['exposure_data'], $i_data['item_data']);
            array_push($result['avg_data'], $i_data['avg_item']);
            if ($i == $days - 1) {
                $result['last_data'] = [
                    'last_avg_data'      => $i_data['avg_item'],
                    'last_exposure_data' => $i_data['item_data'],
                    'x_init'             => $i_data['item_date'],
                ];
            }
        }

        return $result;
    }
}