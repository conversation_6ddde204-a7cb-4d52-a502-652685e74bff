<?php

namespace common\service\announcement;

use common\base\models\BaseAnnouncement;
use common\base\models\BaseAnnouncementCollect;
use common\base\models\BaseAnnouncementEdit;
use common\base\models\BaseAnnouncementHandleLog;
use common\base\models\BaseArticle;
use common\base\models\BaseArticleAttribute;
use common\base\models\BaseCompany;
use common\base\models\BaseCompanyCollect;
use common\base\models\BaseJob;
use common\base\models\BaseJobEdit;
use common\base\models\BaseMemberMessage;
use common\helpers\ArrayHelper;
use common\helpers\IpHelper;
use common\helpers\TimeHelper;
use common\models\ArticleColumn;
use yii\base\Exception;

class AuditHandleService extends BaseService
{
    public $auditType;
    public $editorType;
    public $jobIds;
    public $opinion;
    public $templateId;
    public $homeColumnId;
    public $homeSubColumnIds;
    public $attribute;
    public $tagIds;
    public $backgroundImgFileId;
    public $backgroundImgFileId2;
    public $backgroundImgFileId3;
    public $backgroundImgFileType;
    public $incidental = 2;

    const TYPE_AUDIT_PASS   = 1;  //审核通过
    const TYPE_AUDIT_REFUSE = 2;  //审核拒绝并编辑

    /**
     * 执行
     */
    public function run()
    {
        $this->actionType = self::ACTION_TYPE_AUDIT;
        $this->beforeRun();
        switch ($this->auditType) {
            case self::TYPE_AUDIT_PASS:
            case self::TYPE_AUDIT_REFUSE:
                $this->audit();
                break;
            default:
                throw new Exception('审核类型错误');
        }

        $this->afterRun();
    }

    /**
     * 设置审核类型-通过
     * @return $this
     */
    public function setAuditPass(): AuditHandleService
    {
        $this->auditType = self::TYPE_AUDIT_PASS;

        return $this;
    }

    /**
     * 设置审核类型-拒绝
     * @return $this
     */
    public function setAuditRefuse(): AuditHandleService
    {
        $this->auditType = self::TYPE_AUDIT_REFUSE;

        return $this;
    }

    /**
     * 设置好要处理的数据
     * @param $data
     * @return $this
     * @throws Exception
     */
    public function setData($data): AuditHandleService
    {
        if (empty($data['id'])) {
            throw new Exception('公告id不能为空');
        }
        if (empty($data['auditStatus'])) {
            throw new Exception('审核类型不能为空');
        }
        if ($data['auditStatus'] == self::TYPE_AUDIT_REFUSE && empty($data['opinion'])) {
            throw new Exception('审核处理意见不能为空');
        }

        if (empty($data['editorType'])) {
            throw new Exception('修改类型不能为空');
        }
        if ($data['auditStatus'] == self::TYPE_AUDIT_PASS && empty($data['homeColumnId']) && $data['editorType'] == BaseAnnouncement::TYPE_EDITOR_OTHER) {
            throw new Exception('请选择所属栏目');
        }

        if ($data['templateId']) {
            $this->templateId = $data['templateId'];
            $this->incidental = 1;
        }
        if ($data['homeColumnId']) {
            $this->homeColumnId = $data['homeColumnId'];
            $this->incidental   = 1;
        }
        if ($data['homeSubColumnIds']) {
            $this->homeSubColumnIds = $data['homeSubColumnIds'];
            $this->incidental       = 1;
        }
        if ($data['comboAttribute'] || $data['overseasAttribute']) {
            $attribute       = array_unique(ArrayHelper::merge($data['comboAttribute'], $data['overseasAttribute']));
            $attributeData   = [
                'attribute'                => $attribute,
                'indexTopEndTime'          => $data['indexTopEndTime'] ?: '',
                'columnTopEndTime'         => $data['columnTopEndTime'] ?: '',
                'doctorPushEndTime'        => $data['doctorPushEndTime'] ?: '',
                'overseasIndexTopEndTime'  => $data['overseasIndexTopEndTime'] ?: '',
                'overseasColumnTopEndTime' => $data['overseasColumnTopEndTime'] ?: '',
            ];
            $this->attribute = BaseArticleAttribute::formatAttributeList($attributeData);

            $this->incidental = 1;
        }
        if ($data['tagIds']) {
            $this->tagIds     = $data['tagIds'];
            $this->incidental = 1;
        }
        if ($data['backgroundImgFileId']) {
            $this->backgroundImgFileId = $data['backgroundImgFileId'];
            $this->incidental          = 1;
        }

        if ($data['backgroundImgFileId2']) {
            $this->backgroundImgFileId2 = $data['backgroundImgFileId2'];
            $this->incidental           = 1;
        }

        if ($data['backgroundImgFileId3']) {
            $this->backgroundImgFileId3 = $data['backgroundImgFileId3'];
            $this->incidental           = 1;
        }

        if ($data['backgroundImgFileType']) {
            $this->backgroundImgFileType = $data['backgroundImgFileType'];
            $this->incidental            = 1;
        }

        $companyId = BaseAnnouncement::findOneVal(['id' => $data['id']], 'company_id');
        $this->setCompany($companyId);
        $this->setAnnouncement($data['id']);
        $this->setJob($data['id']);

        if ($data['auditStatus'] == self::TYPE_AUDIT_PASS) {
            if (strtotime($this->announcementModel->period_date) > 0 && !empty($data['comboAttribute'])) {
                $this->checkAttributeTime($this->attribute, $this->announcementModel->period_date);
            }
        }

        $announcementInfo = BaseAnnouncement::find()
            ->select('id,article_id,audit_status,company_id')
            ->where(['id' => $data['id']])
            ->asArray()
            ->one();
        if (!$announcementInfo) {
            throw new Exception('非法请求，公告数据不存在');
        }

        $auditStatus = $this->announcementModel->audit_status;
        if ($auditStatus != BaseAnnouncement::STATUS_AUDIT_AWAIT) {
            throw new Exception('公告审核状态有误');
        }

        $status = $this->articleModel->status;
        if ($status != BaseArticle::STATUS_ONLINE) {
            if (empty($data['jobIds'])) {
                throw new Exception('公告职位不能为空');
            }
        }

        $this->announcementData = $announcementInfo;
        $this->announcementId   = $data['id'];
        $this->editorType       = $data['editorType'];
        if ($data['jobIds']) {
            $this->jobIds = explode(',', $data['jobIds']) ?: '';
        }
        $this->opinion = $data['opinion'] ?: '';

        return $this;
    }

    /**
     * 审核
     * @throws Exception
     */
    private function audit()
    {
        $this->performAudit();
    }

    /**
     * 操作审核
     * @throws Exception
     */
    private function performAudit()
    {
        // 审核通过
        if ($this->auditType == self::TYPE_AUDIT_PASS) {
            // 判断修改类型
            switch ($this->editorType) {
                //===仅修改公告===
                case BaseAnnouncement::TYPE_EDITOR_ANNOUNCEMENT:
                    $this->editorAnnouncement();

                    break;
                //===仅新增职位===
                case BaseAnnouncement::TYPE_ADD_JOB:
                    $this->addJob();

                    break;
                //===仅编辑职位===
                case BaseAnnouncement::TYPE_EDITOR_JOB:
                    $this->editorJob();

                    break;
                //===编辑职位+新增职位===
                case BaseAnnouncement::TYPE_EDITOR_ADD_JOB:
                    $this->editorJob();
                    $this->addJob();

                    break;
                //===编辑公告+编辑职位===
                case BaseAnnouncement::TYPE_EDITOR_ANNOUNCEMENT_JOB:
                    $this->editorAnnouncement();
                    $this->editorJob();
                    break;
                //===编辑公告+新增职位===
                case BaseAnnouncement::TYPE_EDITOR_ANNOUNCEMENT_ADD_JOB:
                    $this->editorAnnouncement();
                    $this->addJob();

                    break;
                //===编辑公告+编辑职位+新增职位===
                case BaseAnnouncement::TYPE_EDITOR_ANNOUNCEMENT_JOB_ADD_JOB:
                    $this->editorAnnouncement();
                    $this->editorJob();
                    $this->addJob();

                    break;
                //===无审核通过历史===
                case BaseAnnouncement::TYPE_EDITOR_OTHER:
                    if ($this->articleModel->first_release_time == TimeHelper::ZERO_TIME) {
                        $title = '【新职位发布】您关注的单位“' . $this->companyModel->full_name . '”发布了新公告！';

                        $link_key    = BaseMemberMessage::LINK_TYPE_ANNOUNCEMENT_DETAIL;
                        $link_params = [
                            'id' => $this->announcementModel->id,
                        ];
                        $content     = '您关注的单位“' . $this->companyModel->full_name . '”发布了一条新公告：“' . $this->articleModel->title . '”，快去看看吧～';
                        BaseMemberMessage::send(BaseCompanyCollect::collectMemberIds($this->companyModel->id), BaseMemberMessage::TYPE_RESUME_SYSTEM, $title, $content, $link_key,
                            $link_params);
                    }
                    $this->articleModel->first_release_time    = CUR_DATETIME; //首次发布时间
                    $this->articleModel->refresh_time          = CUR_DATETIME; //刷新时间
                    $this->articleModel->refresh_date          = CUR_DATE; //刷新时间
                    $this->announcementModel->is_first_release = $this->companyModel->is_cooperation == BaseCompany::COOPERATIVE_UNIT_YES ? BaseAnnouncement::IS_FIRST_RELEASE_YES : BaseAnnouncement::IS_FIRST_RELEASE_NO;
                    break;
                default:
                    throw new Exception('修改类型非法');
            }

            // 审核通过--修改审核状态
            $this->announcementModel->audit_status = BaseAnnouncement::STATUS_AUDIT_PASS;
            if (!$this->announcementModel->save()) {
                throw new Exception($this->announcementModel->getFirstErrorsMessage());
            }

            // 审核通过--修改在线状态
            $this->articleModel->status       = BaseArticle::STATUS_ONLINE;
            $this->articleModel->release_time = CUR_DATETIME; //发布时间
            if (!$this->articleModel->save()) {
                throw new Exception($this->articleModel->getFirstErrorsMessage());
            }

            // 是否有操作职位
            if ($this->editorType != BaseAnnouncement::TYPE_EDITOR_ANNOUNCEMENT) {
                //设置公告下的职位审核状态
                $this->setJobAuditStatus(BaseJob::AUDIT_STATUS_PASS_AUDIT);
            }
            //            //通过后处理一下首发
            //            if ($this->articleModel->refresh_time == $this->articleModel->first_release_time && $this->articleModel->first_release_time > TimeHelper::dayToBeginTime(date('Y-m-d',
            //                    strtotime('-15 day')))) {
            //                $this->announcementModel->is_first_release = BaseAnnouncement::IS_FIRST_RELEASE_YES;
            //                $this->announcementModel->save();
            //            }

            //发送站内信--子账号版本去掉
            //$sendTitle = '【公告审核通过】您发布的公告已审核通过。';
            //BaseMemberMessage::send($this->companyModel->member_id, BaseMemberMessage::TYPE_COMPANY_SYSTEM, $sendTitle,
            //    '您发布的公告“' . $this->announcementModel->title . '”已审核通过，后续请留意简历接收情况。');
        } elseif ($this->auditType == self::TYPE_AUDIT_REFUSE) {
            //审核拒绝
            $this->announcementModel->audit_status = BaseAnnouncement::STATUS_AUDIT_REFUSE;
            if (!$this->announcementModel->save()) {
                throw new Exception($this->announcementModel->getFirstErrorsMessage());
            }

            // 是否有操作职位
            if ($this->editorType != BaseAnnouncement::TYPE_EDITOR_ANNOUNCEMENT) {
                //设置公告下的职位审核状态
                $this->setJobAuditStatus(BaseJob::AUDIT_STATUS_REFUSE_AUDIT);
            }

            //发送站内信
            //            $sendTitle = '【公告审核不通过】您发布的公告审核不通过，请修改后重新发布。';
            //            BaseMemberMessage::send($this->companyModel->member_id, BaseMemberMessage::TYPE_COMPANY_SYSTEM, $sendTitle,
            //                '您发布的公告“' . $this->announcementModel->title . '”审核不通过，请查看详情并按照要求修改重新发布公告。');
        } else {
            throw new Exception('审核状态非法');
        }

        // 这里修改栏目、副栏目、文档属性、特色标签等
        if ($this->incidental == 1) {
            if (strlen($this->templateId) > 0) {
                $announcementTempModel                           = BaseAnnouncement::findOne(['id' => $this->announcementId]);
                $announcementTempModel->template_id              = $this->templateId;
                $announcementTempModel->background_img_file_id   = $this->backgroundImgFileId ?: 0;
                $announcementTempModel->background_img_file_id_2 = $this->backgroundImgFileId2 ?: 0;
                $announcementTempModel->background_img_file_id_3 = $this->backgroundImgFileId3 ?: 0;
                $announcementTempModel->background_img_file_type = $this->backgroundImgFileType ?: BaseAnnouncement::BACKGROUND_IMG_FILE_TYPE_DEFAULT;

                if (!$announcementTempModel->save()) {
                    throw new Exception($announcementTempModel->getFirstErrorsMessage());
                }
            }

            $articleTempModel = BaseArticle::findOne(['id' => $this->articleId]);
            if (strlen($this->homeColumnId) > 0) {
                $articleTempModel->home_column_id = $this->homeColumnId;
            }
            if (strlen($this->homeSubColumnIds) > 0) {
                $articleTempModel->home_sub_column_ids = $this->homeSubColumnIds;
            }
            if (strlen($this->tagIds) > 0) {
                $articleTempModel->tag_ids = $this->tagIds;
            }
            if (!$articleTempModel->save()) {
                throw new Exception($articleTempModel->getFirstErrorsMessage());
            }
            // 审核操作属性
            BaseArticleAttribute::createAttribute($this->articleId, $this->attribute);
        }
    }

    /**
     * 编辑类型-仅修改公告
     * @throws Exception
     */
    private function editorAnnouncement()
    {
        //获取最新一条编辑表的数据
        $announcementEdit = BaseAnnouncementEdit::find()
            ->where([
                'announcement_id' => $this->announcementId,
                'status'          => BaseJobEdit::STATUS_ONLINE,
            ])
            ->select([
                'edit_content',
                'id',
            ])
            ->orderBy('id desc')
            ->asArray()
            ->one();

        $handleAfterMessage = json_decode($announcementEdit['edit_content'], true);
        //将编辑表内容更新到文章表
        if ($handleAfterMessage['content'] && $handleAfterMessage['file_ids']) {
            $this->articleModel->content       = $handleAfterMessage['content'];
            $this->announcementModel->file_ids = $handleAfterMessage['file_ids'];
        } elseif ($handleAfterMessage['content']) {
            $this->articleModel->content = $handleAfterMessage['content'];
        } else {
            $this->announcementModel->file_ids = $handleAfterMessage['file_ids'] ?: '';
        }

        //修改编辑公告表--使用状态
        $announcementEditModel         = BaseAnnouncementEdit::findOne(['announcement_id' => $this->announcementId]);
        $announcementEditModel->status = BaseAnnouncementEdit::STATUS_OFFLINE;
        if (!$announcementEditModel->save()) {
            throw new Exception($announcementEditModel->getFirstErrorsMessage());
        }
    }

    /**
     * 编辑类型-仅新增职位
     * @throws Exception
     */
    private function addJob()
    {
        if (!$this->jobIds) {
            throw new Exception('职位不能为空');
        }

        $jobModel   = new BaseJob();
        $updataData = [
            'status'       => BaseJob::STATUS_WAIT,
            'release_time' => CUR_DATETIME,
        ];
        //批量修改职位状态
        if (!$jobModel->updateAll($updataData, [
            'in',
            'id',
            $this->jobIds,
        ])) {
            throw new Exception($jobModel->getFirstErrorsMessage());
        }
    }

    /**
     * 编辑类型-仅修改职位
     * @throws Exception
     */
    private function editorJob()
    {
        // 获取开启编辑的职位id
        $jobEditData = BaseJobEdit::find()
            ->select('job_id')
            ->where([
                'announcement_id' => $this->announcementId,
                'status'          => BaseJobEdit::STATUS_ONLINE,
            ])
            ->orderBy('id desc')
            ->asArray()
            ->all();

        foreach ($jobEditData as $item) {
            $jobIdArr[] = $item['job_id'];
        }

        // 修改了两个以上的职位
        if (count($jobIdArr) > 1) {
            //获取编辑表数据
            $jobEditData = BaseJobEdit::find()
                ->select('job_id,edit_content')
                ->where([
                    'in',
                    'job_id',
                    $jobIdArr,
                ])
                ->asArray()
                ->all();

            $handleAfterMessage = [];
            foreach ($jobEditData as $key => $item) {
                $handleAfterMessage[$key] = json_decode($item['edit_content'], true);
                foreach ($handleAfterMessage as $k => $v) {
                    $jobUpdateData = BaseJob::findOne(['id' => $item['job_id']]);

                    if (array_key_exists('duty', $v)) {
                        $jobUpdateData->duty = $v['duty'];
                    }
                    if (array_key_exists('requirement', $v)) {
                        $jobUpdateData->requirement = $v['requirement'];
                    }
                    if (array_key_exists('remark', $v)) {
                        $jobUpdateData->remark = $v['remark'];
                    }
                    if (array_key_exists('file_ids', $v)) {
                        $jobUpdateData->file_ids = $v['file_ids'];
                    }
                    //更新到正式职位表
                    if (!$jobUpdateData->save()) {
                        throw new Exception($jobUpdateData->getFirstErrorsMessage());
                    }
                }

                //修改编辑职位表状态
                $jobEditModel         = BaseJobEdit::findOne(['job_id' => $item['job_id']]);
                $jobEditModel->status = BaseJobEdit::STATUS_OFFLINE;
                if (!$jobEditModel->save()) {
                    throw new Exception($jobEditModel->getFirstErrorsMessage());
                }
            }
        } else {
            if (!$jobIdArr) {
                return true;
            }

            // 单个职位修改
            $jobEditData = BaseJobEdit::find()
                ->where([
                    'in',
                    'job_id',
                    $jobIdArr,
                ])
                ->select([
                    'edit_content',
                    'id',
                ])
                ->orderBy('id desc')
                ->asArray()
                ->one();

            $handleAfterMessage = json_decode($jobEditData['edit_content'], true);

            $temp = [];
            foreach ($handleAfterMessage as $k => $item) {
                if (in_array($k, [
                    'duty',
                    'requirement',
                    'remark',
                    'file_ids',
                ])) {
                    $temp[$k] = $item;
                }
            }
            if (!empty($temp)) {
                BaseJob::updateAll($temp, [
                    'in',
                    'id',
                    $jobIdArr,
                ]);
            }

            //修改编辑职位表状态
            $jobEditModel         = BaseJobEdit::findOne(['job_id' => $jobIdArr[0]]);
            $jobEditModel->status = BaseJobEdit::STATUS_OFFLINE;
            if (!$jobEditModel->save()) {
                throw new Exception($jobEditModel->getFirstErrorsMessage());
            }
        }
    }

    /**
     * 设置公告下的职位审核状态
     * @throws Exception
     */
    private function setJobAuditStatus($auditStatus)
    {
        // 获取公告原职位并修改状态
        $jobData = BaseJob::find()
            ->select('id,period_date,audit_status,first_release_time')
            ->where([
                'announcement_id' => $this->announcementId,
                'is_show'         => BaseJob::IS_SHOW_YES,
            ])
            ->andWhere([
                '<>',
                'status',
                BaseJob::STATUS_DELETE,
            ])
            ->andWhere([
                'audit_status' => [
                    BaseJob::AUDIT_STATUS_WAIT_AUDIT,
                    //BaseJob::AUDIT_STATUS_REFUSE_AUDIT,
                    //BaseJob::AUDIT_STATUS_WAIT,
                ],
            ])
            ->asArray()
            ->all();

        if ($jobData) {
            foreach ($jobData as $item) {
                $jobModel = BaseJob::findOne(['id' => $item['id']]);
                if ($auditStatus == BaseJob::AUDIT_STATUS_PASS_AUDIT) {
                    // 没有审核通过历史
                    if ($item['first_release_time'] == TimeHelper::ZERO_TIME) {
                        $jobModel->first_release_time = CUR_DATETIME;
                        $jobModel->refresh_time       = CUR_DATETIME; // 刷新时间
                        $jobModel->refresh_date       = CUR_DATE; // 刷新时间
                        $jobModel->is_first_release   = $this->companyModel->is_cooperation == BaseCompany::COOPERATIVE_UNIT_YES ? BaseJob::IS_FIRST_RELEASE_YES : BaseJob::IS_FIRST_RELEASE_NO;
                    }

                    $jobModel->status       = BaseJob::STATUS_ONLINE;
                    $jobModel->audit_status = $auditStatus;
                    $jobModel->release_time = CUR_DATETIME; // 发布时间
                    $jobModel->is_article   = BaseJob::IS_ARTICLE_YES;
                } else {
                    if ($item['audit_status'] == BaseJob::AUDIT_STATUS_WAIT_AUDIT) {
                        $jobModel->audit_status = $auditStatus;
                    }
                }

                if (!$jobModel->save()) {
                    throw new Exception($jobModel->getFirstErrorsMessage());
                }
            }
        } //else {
        //throw new Exception('审核操作失败');
        //}
    }

    /**
     * 创建公告审核操作日志
     * @throws Exception
     * @throws \yii\base\NotSupportedException
     */
    protected function auditHandleLog()
    {
        //操作动作入表
        $handleBefore = [
            'audit_status' => BaseJob::AUDIT_STATUS_WAIT_AUDIT,
        ];

        if ($this->auditType == self::TYPE_AUDIT_REFUSE) {
            $this->auditType = -1;
        }
        $handleAfter  = [
            'audit_status' => $this->auditType,
            'opinion'      => $this->opinion,
        ];
        $handleLogArr = [
            'add_time'        => CUR_DATETIME,
            'announcement_id' => $this->announcementId,
            'handle_type'     => (string)BaseAnnouncementHandleLog::HANDLE_TYPE_AUDIT,
            'handler_type'    => $this->operatorType,
            'handler_id'      => $this->operatorId,
            'handler_name'    => $this->operatorUserName ?: '',
            'handle_before'   => json_encode($handleBefore),
            'handle_after'    => json_encode($handleAfter),
            'ip'              => IpHelper::getIpInt(),
        ];
        BaseAnnouncementHandleLog::createInfo($handleLogArr);
    }

}
