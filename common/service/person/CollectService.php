<?php

namespace common\service\person;

use common\base\BaseActiveRecord;
use  \common\base\models\BaseAnnouncement;
use common\base\models\BaseArea;
use common\base\models\BaseArticle;
use  \common\base\models\BaseCompany;
use common\base\models\BaseDictionary;
use common\base\models\BaseJobApply;
use common\base\models\BaseJobApplyRecord;
use common\base\models\BaseMemberActionLog;
use  \common\base\models\BaseNews;
use  \common\base\models\BaseJob;
use common\base\models\BaseOffSiteJobApply;
use common\base\models\BaseResume;
use common\helpers\FileHelper;
use common\helpers\StringHelper;
use common\helpers\TimeHelper;
use frontendPc\models\Article;
use frontendPc\models\News;
use \yii\base\Exception;
use \common\base\models\BaseAnnouncementCollect;
use \common\base\models\BaseJobCollect;
use \common\base\models\BaseNewsCollect;
use \common\base\models\BaseCompanyCollect;
use h5\models\ResumeEquity;
use h5\models\ResumeEquitySetting;
use Yii;
use yii\helpers\Url;

class CollectService
{
    private $memberId;
    private $type;
    private $collectId;
    private $collectModel;
    private $dataModel;

    const TYPE_ANNOUNCEMENT = 1;
    const TYPE_JOB          = 2;
    const TYPE_COMPANY      = 3;
    const TYPE_NEWS         = 4;

    const DEFAULT_PAGE_SIZE = 20;
    //字段名称列表
    const FILTER_LIST = [
        self::TYPE_ANNOUNCEMENT => 'announcement_id',
        self::TYPE_JOB          => 'job_id',
        self::TYPE_COMPANY      => 'company_id',
        self::TYPE_NEWS         => 'news_id',
    ];

    //类型中文名称
    const NAME_LIST = [
        self::TYPE_ANNOUNCEMENT => '公告',
        self::TYPE_JOB          => '职位',
        self::TYPE_COMPANY      => '单位',
        self::TYPE_NEWS         => '资讯',
    ];

    public function init($data)
    {
        // 这里有可能小程序过来直接传了memberId
        if ($data['memberId']) {
            $this->memberId = $data['memberId'];
        }

        $this->checkLogin();
        //收藏的类型
        $this->type = $data['type'];
        //收藏的id（可能是数组）
        if (empty($data['ids'])) {
            throw new Exception('收藏数据不能为空');
        }
        $this->collectId = explode(',', $data['ids']);
        //设置基本参数
        $this->setData();
        //判断收藏的数据是否正常
        $this->checkRecord();

        return $this;
    }

    public function run()
    {
        //新增/取消收藏
        $this->collect();
    }

    private function checkLogin()
    {
        //判断用户登陆状态
        if ($this->memberId) {
            return true;
        }
        $this->memberId = Yii::$app->user->id;
        if (empty($this->memberId)) {
            throw new Exception('请先登录后再进行操作');
        }
    }

    /**
     * 检查记录是否正常
     * @return void
     * @throws Exception
     */
    private function checkRecord()
    {
        foreach ($this->collectId as $id) {
            $model = $this->dataModel::find()
                ->where([
                    'id' => $id,
                ])
                ->select('status')
                ->asArray()
                ->one();
            if (!$model) {
                throw new Exception('数据不存在');
            }
        }
    }

    private function setData()
    {
        switch ($this->type) {
            case self::TYPE_ANNOUNCEMENT:
                $this->collectModel = new BaseAnnouncementCollect();
                $this->dataModel    = new BaseAnnouncement();
                break;
            case self::TYPE_COMPANY:
                $this->collectModel = new BaseCompanyCollect();
                $this->dataModel    = new BaseCompany();
                break;
            case self::TYPE_NEWS:
                $this->collectModel = new BaseNewsCollect();
                $this->dataModel    = new BaseNews();
                break;
            case self::TYPE_JOB:
                $this->collectModel = new BaseJobCollect();
                $this->dataModel    = new BaseJob();
                break;
        }
    }

    /**
     * 收藏/取消收藏
     * @return void
     * @throws Exception
     * @throws \yii\base\NotSupportedException
     */
    private function collect()
    {
        $filter          = self::FILTER_LIST[$this->type];
        $collectTypeName = self::NAME_LIST[$this->type];
        foreach ($this->collectId as $id) {
            //判断数据是已经收藏，还是新增收藏
            $model = $this->collectModel::findOne([
                "$filter"   => $id,
                'member_id' => $this->memberId,
            ]);
            if (!empty($model)) {
                $model->status = $this->getNewStatus($model['status']);
                $logContent    = '取消' . $collectTypeName . '收藏' . 'collectId' . $model->id;
            } else {
                $model              = $this->collectModel;
                $model->$filter     = $id;
                $model->member_id   = $this->memberId;
                $model->status      = BaseAnnouncementCollect::STATUS_ACTIVE;
                $model->update_time = CUR_DATETIME;
                $logContent         = '新增' . $collectTypeName . '收藏' . 'collectId' . $model->id;
            }
            if (!$model->save()) {
                throw new Exception($model->getFirstErrorsMessage());
            }
            //新增操作日志
            BaseMemberActionLog::log([
                'content' => $logContent,
            ]);
        }
    }

    /**
     * 获取新的收藏状态
     * @param $status
     * @return int|void
     */
    private function getNewStatus($status)
    {
        if ($status == BaseActiveRecord::STATUS_ACTIVE) {
            return BaseActiveRecord::STATUS_DELETE;
        } elseif ($status == BaseActiveRecord::STATUS_DELETE) {
            return BaseActiveRecord::STATUS_ACTIVE;
        }
    }

    /**
     * h5收藏职位列表（所需数据暂时未与pc统一）
     * @param $searchData
     * @return array
     */
    public function getH5JobList($searchData)
    {
        $this->checkLogin();
        $onlineStatus  = BaseJob::STATUS_ONLINE;
        $offlineStatus = BaseJob::STATUS_OFFLINE;
        $deleteStatus  = BaseJob::STATUS_DELETE;

        $query = BaseJobCollect::find()
            ->alias('jc')
            ->leftJoin(['j' => BaseJob::tableName()], 'j.id=jc.job_id')
            ->leftJoin(['c' => BaseCompany::tableName()], 'c.id=j.company_id')
            ->where(['jc.member_id' => $searchData['memberId']])
            ->andWhere(['jc.status' => BaseJobCollect::STATUS_ACTIVE])
            ->andWhere([
                'in',
                'j.status',
                [
                    $onlineStatus,
                    $offlineStatus,
                    $deleteStatus,
                ],
            ]);

        $query->select([
            'jc.id',
            'j.id as jobId',
            'j.experience_type as experienceType',
            'j.announcement_id as announcementId',
            'j.education_type as educationType',
            'j.status as jobStatus',
            'j.name as jobName',
            'c.id as companyId',
            'c.full_name as companyName',
            'c.is_cooperation as isCooperation',
            'j.release_time as publishDate',
            'j.city_id',
            'j.apply_type as applyType',
            'c.nature as companyNature',
            'c.type as companyType',
            'j.min_wage as minWage',
            'j.max_wage as maxWage',
            'j.wage_type as wageType',
            'j.delivery_way',
            'j.delivery_type',
        ]);
        $count = $query->count();

        // 校验是否拥有收藏查看权益
        $colllectLimit = false;
        if ($count > 50 && ResumeEquity::checkEquity($searchData['resumeId'],
                ResumeEquitySetting::ID_COLLECT_VIEW) === false) {
            $colllectLimit = true;
            $count         = 50;
        }

        $pageSize = $searchData['pageSize'] ?: self::DEFAULT_PAGE_SIZE;
        $pages    = BaseJobCollect::setPage($count, $searchData['page'], $pageSize);
        $offset   = $pages['offset'];
        $limit    = $pages['limit'];
        if ($colllectLimit && $offset < $count && $offset + $limit > $count) {
            $limit = $count - $offset;
        }
        if ($colllectLimit && $offset >= $count) {
            $limit = 0;
        }

        $sort = "CASE WHEN `j`.`status` = $onlineStatus then 1 WHEN `j`.`status` = $offlineStatus then 2 WHEN `j`.`status` = $deleteStatus then 3 END,id desc";
        $list = $query->offset($offset)
            ->limit($limit)
            ->orderBy('j.status desc,jc.update_time desc')
            ->asArray()
            ->orderBy($sort)
            ->all();
        if (!empty($searchData['memberId'])) {
            //获取简历信息
            $resumeInfo = BaseResume::findOne([
                'member_id' => $searchData['memberId'],
                'status'    => BaseResume::STATUS_ACTIVE,
            ]);
        }
        foreach ($list as &$v) {
            $v['areaName']    = BaseArea::getAreaName($v['city_id']);
            $v['applyStatus'] = 0;
            if (!empty($searchData['memberId'])) {
                $v['applyStatus'] = BaseJobApplyRecord::checkJobApplyStatus($resumeInfo->id, $v['jobId']);
            }

            $v['url'] = Url::toRoute([
                'job/detail',
                'id' => $v['jobId'],
            ]);

            $v['companyUrl'] = Url::toRoute([
                'company/detail',
                'id' => $v['companyId'],
            ]);

            //判断单位是否合作单位
            $cooperationInfo = BaseCompany::findOneVal(['id' => $v['companyId']], 'is_cooperation');
            if ($cooperationInfo == BaseCompany::COOPERATIVE_UNIT_YES) {
                $v['isCooperation'] = true;
            } else {
                $v['isCooperation'] = false;
            }

            //判断职位投递方式是否是邮箱投递
            $applyTypeArr = explode(',', $v['applyType']);

            if (in_array(BaseJob::ATTRIBUTE_APPLY_EMAIL, $applyTypeArr)) {
                $v['isEmailApply'] = true;
            } else {
                $v['isEmailApply'] = false;
            }

            //获取公告名称
            $v['announcementName'] = BaseAnnouncement::findOneVal(['id' => $v['announcementId']], 'title');
            //获取经验要求
            $v['experience'] = BaseDictionary::getExperienceName($v['experienceType']);
            //获取学历水平
            $v['education'] = BaseDictionary::getEducationName($v['educationType']);
            //获取单位类型
            $v['companyTypeName'] = BaseDictionary::getCompanyTypeName($v['companyType']);
            //获取单位性质
            $v['companyNatureName'] = BaseDictionary::getCompanyNatureName($v['companyNature']);
            //拼接工资
            if ($v['minWage'] == 0 && $v['maxWage'] == 0) {
                $v['wage'] = '面议';
            } else {
                $v['wage'] = BaseJob::formatWage($v['minWage'], $v['maxWage'], $v['wageType']);
            }
        }

        return [
            'list' => $list,
            'page' => [
                'count' => intval($count),
                'limit' => intval($pages['limit']),
                'page'  => intval($searchData['page']),
            ],
        ];
    }

    public function getMiniJobList($searchData)
    {
        $onlineStatus  = BaseJob::STATUS_ONLINE;
        $offlineStatus = BaseJob::STATUS_OFFLINE;
        $deleteStatus  = BaseJob::STATUS_DELETE;

        $query = BaseJobCollect::find()
            ->alias('jc')
            ->leftJoin(['j' => BaseJob::tableName()], 'j.id=jc.job_id')
            ->leftJoin(['c' => BaseCompany::tableName()], 'c.id=j.company_id')
            ->where(['jc.member_id' => $searchData['memberId']])
            ->andWhere(['jc.status' => BaseJobCollect::STATUS_ACTIVE])
            ->andWhere([
                'in',
                'j.status',
                [
                    $onlineStatus,
                    $offlineStatus,
                    $deleteStatus,
                ],
            ]);

        $query->select([
            'jc.id',
            'j.id as jobId',
            'j.experience_type as experienceType',
            'j.announcement_id as announcementId',
            'j.education_type as educationType',
            'j.status',
            'j.name as jobName',
            'c.id as companyId',
            'c.full_name as companyName',
            'c.is_cooperation as isCooperation',
            'j.refresh_time',
            'j.city_id',
            'j.apply_type as applyType',
            'c.nature as companyNature',
            'c.type as companyType',
            'j.min_wage as minWage',
            'j.max_wage as maxWage',
            'j.wage_type as wageType',
            'j.amount',
            'j.delivery_way',
            'j.delivery_type',
        ]);
        $count = $query->count();

        // 这里还有一个逻辑，判断vip类型，如果是非vip。最多count=50
        $isVip   =  BaseResume::checkVip($searchData['memberId']);

        if (!$isVip && $count > 50) {
            $count = 50;
        }

        $pageSize = $searchData['pageSize'] ?: self::DEFAULT_PAGE_SIZE;
        $pages    = BaseJobCollect::setPage($count, $searchData['page'], $pageSize);
        $offset   = $pages['offset'];
        $limit    = $pages['limit'];

        $sort = "CASE WHEN `j`.`status` = $onlineStatus then 1 WHEN `j`.`status` = $offlineStatus then 2 WHEN `j`.`status` = $deleteStatus then 3 END,id desc";
        $list = $query->offset($offset)
            ->limit($limit)
            ->orderBy('j.status desc,jc.update_time desc')
            ->asArray()
            ->orderBy($sort)
            ->all();

        // 如果分页去到了第三页
        if (!$isVip && $searchData['page'] == 3) {
            // 只截取前10个
            $list = array_slice($list, 0, 10);
        }

        if (!$isVip && $searchData['page'] > 3) {
            $list = [];
        }

        if (!empty($searchData['memberId'])) {
            //获取简历信息
            $resumeInfo = BaseResume::findOne([
                'member_id' => $searchData['memberId'],
                'status'    => BaseResume::STATUS_ACTIVE,
            ]);
        }
        foreach ($list as &$v) {
            $v['city']        = BaseArea::getAreaName($v['city_id']);
            $v['applyStatus'] = 0;
            if (!empty($searchData['memberId'])) {
                $v['applyStatus'] = BaseJobApplyRecord::checkJobApplyStatus($resumeInfo->id, $v['jobId']);
            }

            //判断单位是否合作单位
            $cooperationInfo = BaseCompany::findOneVal(['id' => $v['companyId']], 'is_cooperation');
            if ($cooperationInfo == BaseCompany::COOPERATIVE_UNIT_YES) {
                $v['isCooperation'] = true;
            } else {
                $v['isCooperation'] = false;
            }

            //判断职位投递方式是否是邮箱投递
            $applyTypeArr = explode(',', $v['applyType']);

            if (in_array(BaseJob::ATTRIBUTE_APPLY_EMAIL, $applyTypeArr)) {
                $v['isEmailApply'] = true;
            } else {
                $v['isEmailApply'] = false;
            }

            //获取公告名称
            $v['announcementName'] = BaseAnnouncement::findOneVal(['id' => $v['announcementId']], 'title');
            //获取经验要求
            $v['experience'] = BaseDictionary::getExperienceName($v['experienceType']);
            //获取学历水平
            $v['education'] = BaseDictionary::getEducationName($v['educationType']);
            //获取单位类型
            $v['companyTypeName'] = BaseDictionary::getCompanyTypeName($v['companyType']);
            //获取单位性质
            $v['companyNatureName'] = BaseDictionary::getCompanyNatureName($v['companyNature']);
            //拼接工资
            if ($v['minWage'] == 0 && $v['maxWage'] == 0) {
                $v['wage'] = '面议';
            } else {
                $v['wage'] = BaseJob::formatWage($v['minWage'], $v['maxWage'], $v['wageType']);
            }

            if ($v['amount'] == '-1') {
                $v['amount'] = '若干';
            }

            $v['refreshDate'] = TimeHelper::formatDateByYear($v['refresh_time']);

            // 这里如果已下线或已删除就给一个错误文案
            if ($v['status'] == BaseJob::STATUS_OFFLINE || $v['status'] == BaseJob::STATUS_DELETE) {
                $v['toastMessage'] = '该职位已删除';
                $v['status']       = BaseJob::STATUS_OFFLINE;
            }

            // 因为前端跳转的是需要用到职位的id,所以这边重置一下
            $v['id'] = $v['jobId'];
        }

        return [
            'list' => $list,
            'page' => [
                'count' => intval($count),
                'limit' => intval($pages['limit']),
                'page'  => intval($searchData['page']),
            ],
        ];
    }

    /**
     * 获取公告收藏列表
     * @param $searchData
     * @return array
     */
    public function getH5AnnouncementList($searchData)
    {
        $this->checkLogin();
        $query = BaseAnnouncementCollect::find()
            ->alias('ac')
            ->leftJoin(['an' => BaseAnnouncement::tableName()], 'an.id=ac.announcement_id')
            ->leftJoin(['ar' => BaseArticle::tableName()], 'an.article_id=ar.id')
            ->where(['ac.member_id' => $searchData['memberId']])
            ->andWhere(['ac.status' => BaseAnnouncementCollect::STATUS_ACTIVE])
            ->andWhere([
                'in',
                'ar.status',
                [
                    BaseArticle::STATUS_ONLINE,
                    BaseArticle::STATUS_OFFLINE,
                ],
            ]);

        $query->select([
            'ac.id',
            'an.id as announcementId',
            'an.title',
            'an.add_time as publishDate',
            'ac.add_time as addDate',
            'ar.status',
            'ar.is_delete',
            'ar.is_show',
            'an.offline_time',
            'an.company_id',

        ]);
        $count = $query->count();

        // 校验是否拥有收藏查看权益
        $colllectLimit = false;
        if ($count > 50 && ResumeEquity::checkEquity($searchData['resumeId'],
                ResumeEquitySetting::ID_COLLECT_VIEW) === false) {
            $colllectLimit = true;
            $count         = 50;
        }

        $pageSize = $searchData['pageSize'] ?: self::DEFAULT_PAGE_SIZE;
        $pages    = BaseAnnouncementCollect::setPage($count, $searchData['page'], $pageSize);
        $offset   = $pages['offset'];
        $limit    = $pages['limit'];
        if ($colllectLimit && $offset < $count && $offset + $limit > $count) {
            $limit = $count - $offset;
        }
        if ($colllectLimit && $offset >= $count) {
            $limit = 0;
        }

        $isDeleteYes   = BaseArticle::IS_DELETE_YES;
        $isShowNo      = BaseArticle::IS_SHOW_NO;
        $offLineStatus = BaseArticle::STATUS_OFFLINE;
        $sort          = "CASE WHEN (`ar`.`is_delete` != $isDeleteYes and `ar`.`is_show` != $isShowNo and `ar`.`status` != $offLineStatus) THEN 1  WHEN (`ar`.`is_delete` = $isDeleteYes or `ar`.`is_show` = $isShowNo or `ar`.`status` = $offLineStatus) THEN 2 END,id desc";

        $list = $query->offset($offset)
            ->limit($limit)
            ->orderBy($sort)
            ->asArray()
            ->all();

        foreach ($list as $k => &$record) {
            // $record['jobAmount'] = BaseJob::find()
            //     ->where(['announcement_id' => $record['announcementId']])
            //     ->andWhere([
            //         'status' => [
            //             BaseJob::STATUS_ACTIVE,
            //             BaseJob::STATUS_OFFLINE,
            //         ],
            //     ])
            //     ->count();
            $record['url'] = BaseAnnouncement::getDetailUrl($record['announcementId']);
            //获取公告下职位数量（在线+下线）
            $record['jobAmount'] = BaseJob::getAnnouncementJobAmount($record['announcementId']);

            //判断公告是否失效了（下线，删除）
            if ($record['is_delete'] == $isDeleteYes || $record['status'] == $offLineStatus || $record['is_show'] == $isShowNo) {
                $record['invalid'] = 1;
            } else {
                $record['invalid'] = 2;
            }
            if ($record['is_delete'] == $isDeleteYes) {
                //将删除状态加到status，方便前端判断
                $record['status'] = -1;
            }

            //获取公告下的最低学历要求
            // $record['minEducation'] = BaseAnnouncement::getMinEducationName($record['announcementId']);
            $record['minEducation'] = trim(BaseJob::getAnnouncementJobEducationType($record['announcementId']));
            //获取公告下职位数量（在线+下线）
            $record['jobAmount'] = BaseJob::getAnnouncementJobAmount($record['announcementId']);
            //获取公告下职位招聘数量
            $record['recruitAmount'] = BaseJob::getAnnouncementJobRecruitAmount($record['announcementId']);
            //获取公告职位工作城市若有多个，则展示XX（随机选）等
            $record['city'] = BaseAnnouncement::getAnnouncementAreaName($record['announcementId']);
            //公告单位名称
            $record['companyName'] = BaseCompany::getNameById($record['company_id']);
            $companyInfo           = BaseCompany::findOne(['id' => $record['company_id']]);
            //获取单位类型
            $record['companyType'] = BaseDictionary::getCompanyTypeName($companyInfo->type);
            //获取单位性质
            $record['companyNature'] = BaseDictionary::getCompanyNatureName($companyInfo->nature);
        }

        return [
            'list' => $list,
            'page' => [
                'count' => intval($count),
                'limit' => intval($pages['limit']),
                'page'  => intval($searchData['page']),
            ],
        ];
    }

    public function getMiniAnnouncementList($searchData)
    {
        $query = BaseAnnouncementCollect::find()
            ->alias('ac')
            ->leftJoin(['an' => BaseAnnouncement::tableName()], 'an.id=ac.announcement_id')
            ->leftJoin(['ar' => BaseArticle::tableName()], 'an.article_id=ar.id')
            ->where(['ac.member_id' => $searchData['memberId']])
            ->andWhere(['ac.status' => BaseAnnouncementCollect::STATUS_ACTIVE])
            ->andWhere([
                'in',
                'ar.status',
                [
                    BaseArticle::STATUS_ONLINE,
                    BaseArticle::STATUS_OFFLINE,
                ],
            ]);

        $query->select([
            'ac.id',
            'an.id as announcementId',
            'an.title',
            'an.add_time as publishDate',
            'ac.add_time as addDate',
            'ar.status',
            'ar.is_delete',
            'ar.refresh_time',
            'ar.is_show',
            'an.offline_time',
            'an.company_id',

        ]);
        $count = $query->count();

        $pageSize = self::DEFAULT_PAGE_SIZE;
        $pages    = BaseAnnouncementCollect::setPage($count, $searchData['page'], $pageSize);
        $offset   = $pages['offset'];
        $limit    = $pages['limit'];

        $isDeleteYes   = BaseArticle::IS_DELETE_YES;
        $isShowNo      = BaseArticle::IS_SHOW_NO;
        $offLineStatus = BaseArticle::STATUS_OFFLINE;
        $sort          = "CASE WHEN (`ar`.`is_delete` != $isDeleteYes and `ar`.`is_show` != $isShowNo and `ar`.`status` != $offLineStatus) THEN 1  WHEN (`ar`.`is_delete` = $isDeleteYes or `ar`.`is_show` = $isShowNo or `ar`.`status` = $offLineStatus) THEN 2 END,id desc";

        $list = $query->offset($offset)
            ->limit($limit)
            ->orderBy($sort)
            ->asArray()
            ->all();

        foreach ($list as $k => &$record) {
            // $record['jobAmount'] = BaseJob::find()
            //     ->where(['announcement_id' => $record['announcementId']])
            //     ->andWhere([
            //         'status' => [
            //             BaseJob::STATUS_ACTIVE,
            //             BaseJob::STATUS_OFFLINE,
            //         ],
            //     ])
            //     ->count();
            $record['url'] = BaseAnnouncement::getDetailUrl($record['announcementId']);

            //判断公告是否失效了（下线，删除）
            if ($record['is_delete'] == $isDeleteYes || $record['status'] == $offLineStatus || $record['is_show'] == $isShowNo) {
                $record['status']       = '2';
                $record['toastMessage'] = '该公告已删除';
            }

            //获取公告下的最低学历要求
            $record['education'] = trim(BaseJob::getAnnouncementJobEducationType($record['announcementId']));
            //获取公告下职位数量（在线+下线）
            $record['jobAmount'] = BaseJob::getAnnouncementJobAmount($record['announcementId']);
            //获取公告下职位招聘数量
            $record['recruitAmount'] = BaseJob::getAnnouncementJobRecruitAmount($record['announcementId']);
            //获取公告职位工作城市若有多个，则展示XX（随机选）等
            $record['area'] = BaseAnnouncement::getAnnouncementAreaName($record['announcementId']);
            //公告单位名称
            $record['fullName'] = BaseCompany::getNameById($record['company_id']);
            $companyInfo        = BaseCompany::findOne(['id' => $record['company_id']]);
            //获取单位类型
            $record['companyType'] = BaseDictionary::getCompanyTypeName($companyInfo->type);
            //获取单位性质
            $record['companyNature'] = BaseDictionary::getCompanyTypeName($companyInfo->nature);
            $record['time']          = TimeHelper::formatDateByYear($record['refresh_time']);

            $record['id'] = $record['announcementId'];
        }

        return [
            'list' => $list,
            'page' => [
                'count' => intval($count),
                'limit' => intval($pages['limit']),
                'page'  => intval($searchData['page']),
            ],
        ];
    }

    /**
     * 获取单位收藏列表
     * @param $memberId
     * @param $searchData
     * @return array|\yii\db\ActiveRecord[]
     */
    public function getH5CompanyList($searchData)
    {
        $this->checkLogin();
        $query = BaseCompanyCollect::find()
            ->alias('cc')
            ->leftJoin(['c' => BaseCompany::tableName()], 'c.id=cc.company_id')
            ->where(['cc.member_id' => $searchData['memberId']])
            ->andWhere(['cc.status' => BaseCompanyCollect::STATUS_ACTIVE]);

        $query->select([
            'cc.id',
            'c.id as companyId',
            'c.full_name as companyName',
            'c.logo_url as companyLogo',
            'c.province_id',
            'c.city_id',
            'c.type',
            'c.label_ids',
            'c.member_id as companyMemberId',
            'cc.add_time',
            'c.nature',
            'c.scale',
            'c.logo_url',
        ]);
        $count = $query->count();

        $pageSize = $searchData['pageSize'] ?: \Yii::$app->params['defaultPageSize'];

        $pages = BaseCompanyCollect::setPage($count, $searchData['page'], $pageSize);

        $list = $query->offset($pages['offset'])
            ->limit($pages['limit'])
            ->orderBy('cc.update_time desc')
            ->asArray()
            ->all();

        foreach ($list as $k => &$record) {
            // 这里对logo做一个保底设置
            if (empty($record['companyLogo'])) {
                $record['companyLogo'] = \Yii::$app->params['defaultCompanyLogo'];
            }
            //获取地区名
            $record['areaName'] = BaseArea::getAreaName($record['city_id']) ?: '';

            //获取单位标签
            $record['companyLabel'] = [];
            if (!empty($record['label_ids'])) {
                $labelArr = explode(',', $record['label_ids']);
                foreach ($labelArr as $key => $v) {
                    array_push($record['companyLabel'], BaseDictionary::getCompanyLabelName($v));
                }
            }
            //获取单位类型
            $record['companyType'] = BaseDictionary::getCompanyTypeName($record['type']);
            //获取单位性质
            $record['companyNature'] = BaseDictionary::getNatureName($record['nature']);

            //获取单位在招职位数量
            $record['jobAmount'] = BaseJob::getCompanyJobAmount($record['companyId']);
            //获取单位公告数量
            $record['announcementAmount'] = BaseAnnouncement::getCompanyOnLineAnnouncementAmount($record['companyId']);
            //获取单位规模
            $record['scale'] = BaseDictionary::getCompanyScaleName($record['scale']);
            //获取单位logo
            $record['logoUrl'] = BaseCompany::getLogoFullUrl($record['logo_url']);

            $record['url'] = Url::toRoute([
                'company/detail',
                'id' => $record['companyId'],
            ]);
        }

        return [
            'list' => $list,
            'page' => [
                'count' => intval($count),
                'limit' => intval($pages['limit']),
                'page'  => intval($searchData['page']),
            ],
        ];
    }

    public function getMiniCompanyList($searchData)
    {
        $query = BaseCompanyCollect::find()
            ->alias('cc')
            ->leftJoin(['c' => BaseCompany::tableName()], 'c.id=cc.company_id')
            ->where(['cc.member_id' => $searchData['memberId']])
            ->andWhere(['cc.status' => BaseCompanyCollect::STATUS_ACTIVE]);

        $query->select([
            'cc.id',
            'c.id as companyId',
            'c.full_name as name',
            'c.logo_url as companyLogo',
            'c.province_id',
            'c.city_id',
            'c.type',
            'c.label_ids',
            'c.member_id as companyMemberId',
            'cc.add_time',
            'c.nature',
            'c.scale',
            'c.logo_url',
        ]);
        $count = $query->count();

        $pageSize = $searchData['pageSize'] ?: \Yii::$app->params['defaultPageSize'];

        $pages = BaseCompanyCollect::setPage($count, $searchData['page'], $pageSize);

        $list = $query->offset($pages['offset'])
            ->limit($pages['limit'])
            ->orderBy('cc.update_time desc')
            ->asArray()
            ->all();

        foreach ($list as $k => &$record) {
            // 这里对logo做一个保底设置
            if (empty($record['companyLogo'])) {
                $record['companyLogo'] = \Yii::$app->params['defaultCompanyLogo'];
            }
            //获取地区名
            $record['areaName'] = BaseArea::getAreaName($record['city_id']);

            //获取单位类型
            $record['type'] = BaseDictionary::getCompanyTypeName($record['type']);
            //获取单位性质
            $record['nature'] = BaseDictionary::getCompanyNatureName($record['nature']);

            //获取单位在招职位数量
            $record['jobAmount'] = BaseJob::getCompanyJobAmount($record['companyId']);
            //获取单位公告数量
            $record['announcementAmount'] = BaseAnnouncement::getCompanyOnLineAnnouncementAmount($record['companyId']);
            //获取单位规模
            $record['scale'] = BaseDictionary::getCompanyScaleName($record['scale']);
            //获取单位logo
            $record['logoUrl'] = BaseCompany::getLogoFullUrl($record['logo_url']);

            $record['id'] = $record['companyId'];
        }

        return [
            'list' => $list,
            'page' => [
                'count' => intval($count),
                'limit' => intval($pages['limit']),
                'page'  => intval($searchData['page']),
            ],
        ];
    }

    /**
     * 获取资讯收藏列表
     * @param $searchData
     * @param $needPageInfo
     * @return array
     */
    public function getH5NewsList($searchData)
    {
        $this->checkLogin();
        $query = BaseNewsCollect::find()
            ->alias('nc')
            ->leftJoin(['n' => BaseNews::tableName()], 'n.id = nc.news_id')
            ->leftJoin(['a' => BaseArticle::tableName()], 'a.id = n.article_id')
            ->where(['nc.member_id' => $searchData['memberId']])
            ->andWhere(['nc.status' => BaseNewsCollect::STATUS_ACTIVE])
            ->andWhere([
                'in',
                'a.status',
                [
                    BaseArticle::STATUS_ONLINE,
                    BaseArticle::STATUS_OFFLINE,
                ],
            ]);

        $query->select([
            'nc.id',
            'n.id as newsId',
            'a.title',
            'a.click',
            'n.add_time as publishDate',
            'nc.add_time',
            'a.status as articleStatus',
            'a.cover_thumb as coverThumb',
        ]);

        $count = $query->count();

        $pageSize = $searchData['pageSize'] ?: \Yii::$app->params['defaultPageSize'];

        $pages = BaseNewsCollect::setPage($count, $searchData['page'], $pageSize);

        $list = $query->offset($pages['offset'])
            ->limit($pages['limit'])
            ->orderBy('a.status asc,nc.add_time desc')
            ->asArray()
            ->all();
        foreach ($list as &$item) {
            $item['url']         = News::getDetailUrl($item['newsId']);
            $item['coverThumb']  = FileHelper::getFullUrl($item['coverThumb']);
            $item['publishDate'] = TimeHelper::formatDateByYear($item['publishDate']);
        }

        return [
            'list' => $list,
            'page' => [
                'count' => intval($count),
                'limit' => intval($pages['limit']),
                'page'  => intval($searchData['page']),
            ],
        ];
    }
}