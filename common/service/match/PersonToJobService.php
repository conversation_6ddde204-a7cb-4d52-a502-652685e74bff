<?php
namespace common\service\match;

use common\base\models\BaseAnnouncement;
use common\base\models\BaseArea;
use common\base\models\BaseCompany;
use common\base\models\BaseDictionary;
use common\base\models\BaseJob;
use common\base\models\BaseJobApplyRecord;
use common\base\models\BaseJobCollect;
use common\base\models\BaseJobMajorRelation;
use common\base\models\BaseJobTopConfig;
use common\base\models\BaseMajor;
use common\base\models\BaseMember;
use common\base\models\BaseResume;
use common\base\models\BaseResumeEducation;
use common\base\models\BaseResumeIntention;
use common\base\models\BaseResumeIntentionTemp;
use common\base\models\BaseResumeMatchJob;
use common\base\models\BaseWelfareLabel;
use common\helpers\ArrayHelper;
use common\helpers\UrlHelper;
use common\libs\Cache;
use common\service\CommonService;
use Yii;
use yii\base\Exception;
use function JmesPath\search;

class PersonToJobService extends CommonMatchService
{
    private $resume_id;
    private $resume_info;
    private $resume_education_info;
    private $intention_id;
    private $intention_info;
    private $rule_config;
    private $rule_condition_where      = [];
    private $rule_condition_where_item = [];
    private $distinct_resume_ids       = [];
    //匹配规则的条件---star
    /** 求职者的求职意向-职位类型 */
    private $person_job_category_id;
    /** 求职者的求职意向-意向地区 */
    private $person_area_ids;
    /** 求职者-最高学历 */
    private $person_education_id;
    /** 求职者的最高学历-专业 */
    private $person_major_id;
    //匹配规则的条件---end
    private $area_bool = false;
    private $init_bool = true;
    private $list_type = self::LIST_TYPE_RECOMMEND;
    private $page      = self::PAGE;
    private $page_size = self::PAGE_SIZE;

    private $topList    = [];
    private $topIdArray = [];

    private $select = [
        'j.id as job_id',
        'j.name as job_name',
        'j.wage_type',
        'j.min_wage',
        'j.max_wage',
        'j.province_id',
        'j.city_id',
        'j.experience_type',
        'j.education_type',
        'j.amount',
        'j.announcement_id',
        'j.company_id',
        'j.welfare_tag',
        'j.is_establishment',
        'j.major_id',
        'j.delivery_type as job_delivery_type',
        'j.delivery_way as job_delivery_way',
        'j.is_establishment',
        'c.full_name as company_name',
        'c.logo_url as company_logo',
        'c.type as company_type',
        'c.nature as company_nature',
        'c.is_cooperation',
        'a.title as announcement_name',
        'a.delivery_type as announcement_delivery_type',
        'a.delivery_way as announcement_delivery_way',
        //'IF(c.package_type=2,66,c.package_type) as package_status',
        //'c.sort',
        'jt.id as top_id',
    ];

    /**
     * 初始化数据
     * @param $params
     *          [
     *          'intentionId' => 求职意向id
     *          'type' => 列表类型 1推荐 2最新
     *          'page' => 页码
     *          'pageSize' => 数量
     *          'is_queue' => 是否是队列过来的 1是队列 其他则不是队列
     *          ]
     * @throws Exception
     */
    public function init($params)
    {//列表参数初始化
        $this->intention_id = (isset($params['intentionId']) && $params['intentionId'] > 0) ? $params['intentionId'] : 0;
        if ($this->intention_id <= 0) {
            throw new Exception('参数错误');
        }
        $this->list_type = (!isset($params['type']) || $params['type'] == self::LIST_TYPE_RECOMMEND) ? self::LIST_TYPE_RECOMMEND : self::LIST_TYPE_NEW;
        $this->page      = (!isset($params['page']) || $params['page'] <= 0) ? $this->page : $params['page'];
        $this->page_size = (!isset($params['pageSize']) || $params['pageSize'] <= 0) ? $this->page_size : ($params['pageSize'] > self::PAGE_SIZE_MAX ? $this->page_size : $params['pageSize']);
        //列表规则初始化
        $this->rule_config = array_column(Yii::$app->params[self::PERSON_TO_JOB_RULE], null, 'id');
        //获取求职意向数据
        $this->intention_info = BaseResumeIntention::findOne($this->intention_id);

        //做一下小程序首页匹配兼容
        if ($this->intention_info->resume_id == 0 && $this->operationPlatform == CommonService::PLATFORM_MINI) {
            //区分一下是小程序平台才能使用以下逻辑
            //去零时意向表
            $temp_intention_info = BaseResumeIntentionTemp::findOne(['intention_id' => $this->intention_info->id]);
            if ($temp_intention_info) {
                //初始化规则数据
                $this->person_education_id = $temp_intention_info->top_education_code;
                $this->person_major_id     = $temp_intention_info->major_id;
                //这里是临时求职意向---只让获取第一页数据且第一页数据是20条
                $this->page_size = 20;
                $this->page      = 1;
            } else {
                throw new Exception('参数错误');
            }
        } else {
            //获取简历数据
            $this->resume_info           = BaseResume::findOne($this->intention_info->resume_id);
            $this->resume_id             = $this->resume_info->id;
            $this->resume_education_info = BaseResumeEducation::findOne($this->resume_info->last_education_id);
            //初始化规则数据
            $this->person_education_id = $this->resume_education_info->education_id;
            $this->person_major_id     = empty($this->resume_education_info->major_id_level_2) ? 0 : $this->resume_education_info->major_id_level_2;
        }
        //初始化规则数据
        $this->person_job_category_id = $this->intention_info->job_category_id;
        $this->person_area_ids        = $this->intention_info->area_id;
        //规则数据预处理
        $this->processingArea();
        //$this->processingMajor();

        $this->init_bool = false;

        return $this;
    }

    public function setTopList()
    {
        // 拿出职位类型
        $jobCategoryId = $this->person_job_category_id;
        // 根据职位类型拿出当前置顶中的职位

        $jobIdArray = BaseJobTopConfig::find()
            ->alias('a')
            ->select('job_id')
            ->innerJoin(['b' => BaseJob::tableName()], 'b.id=a.job_id')
            ->where([
                'a.status'          => BaseJobTopConfig::STATUS_TOP_ING,
                'b.job_category_id' => $jobCategoryId,
            ])
            ->orderBy('first_release_time desc')
            ->asArray()
            ->column();

        if (!$jobIdArray) {
            return;
        }

        $list = BaseJob::find()
            ->alias('j')
            ->select($this->select)
            ->leftJoin(['a' => BaseAnnouncement::tableName()], 'j.announcement_id=a.id')
            ->leftJoin(['c' => BaseCompany::tableName()], 'j.company_id=c.id')
            ->leftJoin(['jmr' => BaseJobMajorRelation::tableName()], 'j.id=jmr.job_id')
            ->leftJoin(['jt' => BaseJobTopConfig::tableName()], 'j.id=jt.job_id and jt.status=1')
            ->where(['j.id' => $jobIdArray])
            ->groupBy('j.id')
            ->asArray()
            ->all();

        $this->topIdArray = $jobIdArray;
        $this->topList    = $list;
    }

    /**
     * 执行程序--职位推荐
     * @throws Exception
     */
    public function run()
    {
        if ($this->init_bool) {
            throw new Exception('请先初始化');
        }
        //根据参数获取职位参数
        $data = Cache::get($this->getCacheKey());
        if (!$data) {


            if ($this->list_type == self::LIST_TYPE_RECOMMEND) {
                // 设置置顶相关数据
                $this->setTopList();
                $data = $this->personalHomeRecommendJobList();
                //写表
                $this->updateMatchTable($data['list']);
            } else {
                $data = $this->personalHomeNewJobList();
            }
            //数据处理
            if (isset($data['list']) && !empty($data['list'])) {
                $data['list'] = $this->listData($data['list']);
            }
            if (isset($data['recommend_list']) && !empty($data['recommend_list'])) {
                $data['recommend_list'] = $this->listData($data['recommend_list']);
            }
            //缓存当前页数据
            if ($data['total'] > 0) {
                Cache::setex($this->getCacheKey(), self::CACHE_PERSON_EXPIRE_TIME, json_encode($data));
            }
        } else {
            $data = json_decode($data, true);
        }
        //不能做缓存的数据处理
        $data['list']           = $this->notCacheListData($data['list']);
        $data['recommend_list'] = $this->notCacheListData($data['recommend_list']);

        return $data;
    }

    /**
     * 执行程序--职位推荐(专门给队列使用)
     * @return array|mixed|string
     * @throws Exception
     */
    public function runQueue()
    {
        if ($this->init_bool) {
            throw new Exception('请先初始化');
        }
        //根据参数获取职位参数
        $data = Cache::get($this->getCacheKey());
        if (!$data) {
            if ($this->list_type == self::LIST_TYPE_RECOMMEND) {
                $data = $this->personalHomeRecommendJobList();
                //写表
                $this->updateMatchTable($data['list']);
            } else {
                $data = $this->personalHomeNewJobList();
            }
            //数据处理
            if (isset($data['list']) && !empty($data['list'])) {
                $data['list'] = $this->listData($data['list']);
            }
            if (isset($data['recommend_list']) && !empty($data['recommend_list'])) {
                $data['recommend_list'] = $this->listData($data['recommend_list']);
            }
            //缓存当前页数据
            if ($data['total'] > 0) {
                Cache::setex($this->getCacheKey(), self::CACHE_PERSON_EXPIRE_TIME, json_encode($data));
            }
        }
    }

    /**
     * 获取推荐列表
     * @return array|mixed|string
     */
    private function personalHomeRecommendJobList()
    {
        return $this->recommendJobOnePageData();
    }

    /**
     * 获取最新列表
     * @return array|mixed|string
     */
    private function personalHomeNewJobList()
    {
        return $this->newJobOnePageData();
    }

    /**
     * 处理缓存的数据列
     * @param $data
     * @return mixed
     * @throws \Exception
     */
    public function listData($data)
    {
        //处理数据列
        foreach ($data as &$value) {
            if ($value['min_wage'] == 0 && $value['max_wage'] == 0) {
                $value['wage'] = '面议';
            } else {
                $value['wage'] = BaseJob::formatWage($value['min_wage'], $value['max_wage'], $value['wage_type']);
            }
            $value['announcement_name'] = $value['announcement_name'] ?? '';
            $value['education']         = BaseDictionary::getEducationName($value['education_type']);
            $value['experience']        = BaseDictionary::getExperienceName($value['experience_type']);

            $city                         = BaseArea::getAreaName($value['city_id']);
            $value['area_name']           = implode('-', [
                BaseArea::getAreaName($value['province_id']),
                $city,
            ]);
            $value['city']                = $city;
            $value['company_logo']        = $value['company_logo'] ?: Yii::$app->params['defaultCompanyLogo'];
            $value['company_type_name']   = BaseDictionary::getCompanyTypeName($value['company_type']);
            $value['company_nature_name'] = BaseDictionary::getCompanyNatureName($value['company_nature']);
            //专业处理
            $major_arr = explode(',', $value['major_id']);
            $major_set = [];
            if (!empty($major_arr)) {
                foreach ($major_arr as $k => $v) {
                    $item_name = BaseMajor::getMajorName($v);
                    if ($item_name) {
                        array_push($major_set, $item_name);
                    }
                }
            }

            $value['major_set'] = $major_set;
            $value['is_fast']   = '2';
            $value['is_top']    = '2';
            if ($value['job_delivery_type'] == 2) {//职位站内
                if ($value['job_delivery_way'] == 1) {//是平台
                    $value['is_fast'] = '1';
                }
            } elseif ($value['job_delivery_type'] == 0) {//职位跟随公告
                if ($value['announcement_delivery_type'] == 2 && $value['announcement_delivery_way'] == 1) {//跟随公告且是平台
                    $value['is_fast'] = '1';
                }
            }
            if ($value['top_id'] > 0) {
                $value['is_top'] = '1';
            }
            $value['url']              = UrlHelper::createJobDetailPath($value['job_id']);
            $value['company_url']      = UrlHelper::createCompanyDetailPath($value['company_id']);
            $value['announcement_url'] = UrlHelper::createAnnouncementDetailPath($value['announcement_id']);
            $value['is_cooperation']   = '2';//写死不显示官方
            //根据平台处理数据
            switch ($this->operationPlatform) {
                case self::PLATFORM_MINI:
                    $value['major_name']      = count($value['major_set']) > 1 ? $value['major_set'][0] . '等' : ($value['major_set'][0] ?? '');
                    $value['welfare_tag_arr'] = BaseWelfareLabel::getWelfareLabelNameList($value['welfare_tag']);
                    break;
                case self::PLATFORM_WEB_PERSON:
                case self::PLATFORM_WEB:
                case self::PLATFORM_H5:
                    $value['welfare_tag_arr'] = array_slice(BaseWelfareLabel::getWelfareLabelNameList($value['welfare_tag']),
                        0, 2);
                    break;
                default:
                    $value['welfare_tag_arr'] = [];
                    break;
            }
            unset($value['welfare_tag'], $value['top_id'], $value['job_delivery_type'], $value['job_delivery_way'], $value['announcement_delivery_type'], $value['announcement_delivery_way'], $value['min_wage'], $value['max_wage'], $value['wage_type'], $value['education_type'], $value['experience_type'], $value['province_id'], $value['city_id'], $value['company_type'], $value['company_nature']);
        }

        return $data;
    }

    /**
     * 处理不需要缓存的数据列
     * @param $data
     * @return mixed
     * @throws \Exception
     */
    public function notCacheListData($data)
    {
        //处理v不能缓存的数据列
        foreach ($data as &$value) {
            $value['is_collect']   = (BaseJobCollect::checkIsCollect($this->resume_info->member_id,
                $value['job_id'])) ? '1' : '2';
            $value['apply_status'] = strval(BaseJobApplyRecord::checkJobApplyStatus($this->resume_id,
                $value['job_id']));
        }

        return $data;
    }

    /**
     * 获取列表DB对象
     * @return \yii\db\ActiveQuery
     */
    private function getQuery()
    {
        //DB对象
        $query = BaseJob::find()
            ->alias('j')
            ->select($this->select)
            ->leftJoin(['a' => BaseAnnouncement::tableName()], 'j.announcement_id=a.id')
            ->leftJoin(['c' => BaseCompany::tableName()], 'j.company_id=c.id')
            ->leftJoin(['jmr' => BaseJobMajorRelation::tableName()], 'j.id=jmr.job_id')
            ->leftJoin(['jt' => BaseJobTopConfig::tableName()], 'j.id=jt.job_id and jt.status=1')
            ->andWhere(['j.status' => BaseJob::STATUS_ONLINE])
            // 排除掉置顶的那些职位id
            ->andFilterWhere([
                'not in',
                'j.id',
                $this->topIdArray,
            ])
            ->orderBy('j.refresh_time desc,jt.status desc,c.is_cooperation asc,c.sort desc,j.id desc')
            ->groupBy('j.id');
        //处理一下小程序过来的数据要增加特殊条件
        if ($this->operationPlatform == self::PLATFORM_MINI) {
            $query->andWhere(['j.is_miniapp' => 1]);
        }

        return $query;
    }

    /**
     * 根据推荐规则拼装条件
     * @param $rule_id
     * @return \yii\db\ActiveQuery
     */
    private function getQueryRecommendRuleWhere($rule_id)
    {
        $rule_info                       = $this->rule_config[$rule_id];
        $this->rule_condition_where_item = [];
        $query                           = $this->getQuery();
        $where_job_category_arr          = $this->queryJobCategoryRecommend($rule_info['match']['job_category_id']);
        $where_area_arr                  = $this->queryAreaRecommend($rule_info['match']['area_id']);
        $where_education_arr             = $this->queryEducationRecommend($rule_info['match']['education_id']);
        $where_major_arr                 = $this->queryMajorRecommend($rule_info['match']['major_id']);
        $query->andWhere($where_job_category_arr);
        $query->andWhere($where_area_arr);
        $query->andWhere($where_education_arr);
        $query->andWhere($where_major_arr);

        return $query;
    }

    /**
     * 根据最新规则拼装条件
     * @param $rule_id
     * @return \yii\db\ActiveQuery
     */
    private function getQueryNewRuleWhere($type)
    {
        $query = $this->getQuery();
        if ($type == self::LIST_TYPE_RECOMMEND) {
            $max_rule_id          = 16;
            $init_rule_id_default = 1;
        } else {
            $init_rule_id_default = 17;
            $max_rule_id          = 22;
        }
        $where = [
            'or',
        ];
        for ($init_rule_id = $init_rule_id_default; $init_rule_id <= $max_rule_id; $init_rule_id++) {
            $rule_info              = $this->rule_config[$init_rule_id];
            $where_job_category_arr = $this->queryJobCategoryRecommend($rule_info['match']['job_category_id']);
            $where_area_arr         = $this->queryAreaRecommend($rule_info['match']['area_id']);
            $where_education_arr    = $this->queryEducationRecommend($rule_info['match']['education_id']);
            $where_major_arr        = $this->queryMajorRecommend($rule_info['match']['major_id']);
            if (!$this->area_bool) {
                array_push($where, [
                    'and',
                    $where_job_category_arr,
                    $where_education_arr,
                    $where_major_arr,
                    $where_area_arr,
                ]);
            }
        }
        if (count($where) > 1) {
            $query->andWhere($where);
        }

        return $query;
    }

    /**
     * 获取推荐某一页数据
     * @return array
     */
    private function recommendJobOnePageData()
    {
        $total          = 0;
        $list           = [];
        $recommend_list = [];
        $page           = $this->page;
        $page_size      = $this->page_size;
        //分页数据处理
        $page_count           = intval(ceil(($page + 1) / self::PAGE_STEP) * self::PAGE_STEP);
        $topCount             = count($this->topList);
        $total_step           = $page_count * $page_size - $topCount;
        $cur_page_data_offset = ($page - 1) * $page_size - $topCount;
        $cur_page_data_start  = $cur_page_data_offset;
        if ($page == 1) {
            // 置顶全部在第一页出现
            $one_page_number = $page_size - $topCount;
        } else {
            $one_page_number = $page_size;//这一页需要数量
        }
        for ($rule_id = 1; $rule_id <= count($this->rule_config); $rule_id++) {
            $query_item = $this->getQueryRecommendRuleWhere($rule_id);
            //这里处理一下规则出现重复条件情形
            $continue_bool = $this->verifyCondition();
            if ($continue_bool || $this->area_bool) {
                unset($query_item);
                continue;
            }
            $count_query = clone $query_item;
            $list_query  = clone $query_item;
            if (count($this->distinct_resume_ids) > 0) {
                $this->getDistinctWhere($count_query);
            }
            $item_ids   = $count_query->select('j.id')
                ->column();
            $rule_total = count($item_ids);
            $total_new  = $total + $rule_total;
            if ($cur_page_data_start + 1 <= $total_new && $one_page_number > 0) {//注意这里加1
                //获取规则类型
                $rule_type_num = $this->rule_config[$rule_id]['type'];
                //这个大于这个说明可以取数据了
                //这里分两种情况
                /// 1、第一次进入区间不在最后端点上 存在前置 所以要注意计算offset
                /// 2、第二次或者第一次刚好在前置端点上时候 我们不需要计算offset直接去拿  只需要判断当前页数量是否足够就OK了
                $offset = ($cur_page_data_start - $total > 0) ? ($cur_page_data_start - $total) : 0;
                $limit  = ($rule_total - $offset) > $one_page_number ? $one_page_number : ($rule_total - $offset);
                //拿数据 放入返回的列表中
                if (count($this->distinct_resume_ids) > 0) {
                    $this->getDistinctWhere($list_query);
                }
                $list_item  = $list_query->offset($offset)
                    ->limit($limit)
                    ->asArray()
                    ->all();
                $match_rate = $this->rule_config[$rule_id]['value'];
                foreach ($list_item as &$item_value) {
                    $item_value['rule_id']    = $rule_id;
                    $item_value['match_type'] = $rule_type_num;
                    $item_value['match_rate'] = $match_rate;
                }
                if ($rule_type_num == 1) {
                    $list = array_merge($list, $list_item);
                } else {
                    $recommend_list = array_merge($recommend_list, $list_item);
                }
                $one_page_number -= $limit;
            }
            $total = $total_new;
            //这里处理分页总数
            if ($total_step < $total) {
                $total = $total_step;
                break;
            }
            if ($rule_total > 0) {
                $this->distinct_resume_ids = array_merge($this->distinct_resume_ids, $item_ids);
            }
        }

        if ($page == 1 && $topCount) {
            $list = ArrayHelper::merge($this->topList, $list);
        }

        //不正常情形
        $list_total           = count($list);
        $recommend_list_total = count($recommend_list);
        if ($list_total + $recommend_list_total <= 0) {
            $total = 0;
        }

        //返回
        $result = [
            'list'           => $list,
            'recommend_list' => $recommend_list,
            'total'          => $total,
        ];

        return $result;
    }

    /**
     * 获取最新一页数据
     * @return array
     */
    private function newJobOnePageData()
    {
        $total          = 0;
        $list           = [];
        $recommend_list = [];
        $page           = $this->page;
        $page_size      = $this->page_size;
        //分页数据处理
        // $page_count           = intval(ceil(($page + 1) / self::PAGE_STEP) * self::PAGE_STEP);
        // $total_step           = $page_count * $page_size;
        // $cur_page_data_offset = ($page - 1) * $page_size;
        // $cur_page_data_start  = $cur_page_data_offset;
        // $cur_page_data_end    = $cur_page_data_offset + $page_size;
        // $one_page_number      = $page_size;//这一页需要数量
        //分页数据处理
        $page_count           = intval(ceil(($page + 1) / self::PAGE_STEP) * self::PAGE_STEP);
        $topCount             = count($this->topList);
        $total_step           = $page_count * $page_size - $topCount;
        $cur_page_data_offset = ($page - 1) * $page_size - $topCount;
        $cur_page_data_start  = $cur_page_data_offset;
        if ($page == 1) {
            // 置顶全部在第一页出现
            $one_page_number = $page_size - $topCount;
        } else {
            $one_page_number = $page_size;//这一页需要数量
        }

        for ($type_id = 1; $type_id <= 2; $type_id++) {
            $query_item = $this->getQueryNewRuleWhere($type_id);
            //克隆
            $count_query = clone $query_item;
            $list_query  = clone $query_item;
            if (count($this->distinct_resume_ids) > 0) {
                $this->getDistinctWhere($count_query);
            }
            $item_ids   = $count_query->select('j.id')
                ->column();
            $rule_total = count($item_ids);
            $total_new  = $total + $rule_total;
            if ($cur_page_data_start + 1 <= $total_new && $one_page_number > 0) {//注意这里加1
                //这个大于这个说明可以取数据了
                //这里分两种情况
                /// 1、第一次进入区间不在最后端点上 存在前置 所以要注意计算offset
                /// 2、第二次或者第一次刚好在前置端点上时候 我们不需要计算offset直接去拿  只需要判断当前页数量是否足够就OK了
                $offset = ($cur_page_data_start - $total > 0) ? ($cur_page_data_start - $total) : 0;
                $limit  = ($rule_total - $offset) > $one_page_number ? $one_page_number : ($rule_total - $offset);
                //拿数据 放入返回的列表中
                if (count($this->distinct_resume_ids) > 0) {
                    $this->getDistinctWhere($list_query);
                }
                $list_item = $list_query->offset($offset)
                    ->limit($limit)
                    ->asArray()
                    ->all();
                if ($type_id == 1) {
                    $list = array_merge($list, $list_item);
                } else {
                    $recommend_list = array_merge($recommend_list, $list_item);
                }
                $one_page_number -= $limit;
            }
            $total = $total_new;
            //这里处理分页总数
            if ($total_step < $total) {
                $total = $total_step;
                break;
            }
            if ($rule_total > 0) {
                $this->distinct_resume_ids = array_merge($this->distinct_resume_ids, $item_ids);
            }
        }

        if ($page == 1 && $topCount) {
            $list = ArrayHelper::merge($this->topList, $list);
        }
        //不正常情形
        $list_total           = count($list);
        $recommend_list_total = count($recommend_list);
        if ($list_total + $recommend_list_total <= 0) {
            $total = 0;
        }
        //返回
        $result = [
            'list'           => $list,
            'recommend_list' => $recommend_list,
            'total'          => $total,
        ];

        return $result;
    }

    /**
     * 去重
     * @param $query
     * @return mixed
     */
    private function getDistinctWhere($query)
    {
        return $query->andWhere([
            'not in',
            'j.id',
            $this->distinct_resume_ids,
        ]);
    }

    /**
     * 获取当前列表缓存key
     */
    private function getCacheKey()
    {
        //意向职位_意向地区_学历水平_学科专业_类表类型_分页_每页数量
        $area_ids = explode(',', $this->intention_info->area_id);
        sort($area_ids);
        $area_id_str = implode('/', $area_ids);
        //将本身的专业三级转成二级  作为缓存专业
        $major_id_level_2 = empty($this->resume_education_info->major_id_level_2) ? 0 : $this->resume_education_info->major_id_level_2;
        $key_arr          = [
            $this->intention_info->job_category_id,
            $area_id_str,
            $this->resume_education_info->education_id,
            $major_id_level_2,
            $this->list_type,
            $this->page,
            $this->page_size,
        ];
        $key              = md5(implode('_', $key_arr));
        $key_prefix       = $this->list_type == self::LIST_TYPE_RECOMMEND ? Cache::MATCH_PERSONAL_JOb_RECOMMEND_LIST : Cache::MATCH_PERSONAL_JOb_NEW_LIST;

        return $key_prefix . ':' . $key;
    }

    /**
     * 对职位类型进行条件组装
     * @param $type 0不用匹配 1匹配
     * @return mixed
     */
    private function queryJobCategoryRecommend($type)
    {
        $arr = [];
        switch ($type) {
            case self::JOB_CATEGORY_RULE_TYPE_NO:
                break;
            case self::JOB_CATEGORY_RULE_TYPE_YES:
                array_push($this->rule_condition_where_item, $this->person_job_category_id);

                $arr = ['j.job_category_id' => $this->person_job_category_id];
                break;
        }

        return $arr;
    }

    /**
     * 对学历进行条件组装
     * @param $type 0不用匹配 1匹配 2职位学历要求低于人才最高学历
     * @return mixed
     */
    private function queryEducationRecommend($type)
    {
        $arr = [];
        switch ($type) {
            case self::EDUCATION_RULE_TYPE_NO:
                break;
            case self::EDUCATION_RULE_TYPE_YES:
                array_push($this->rule_condition_where_item, $this->person_education_id);
                $arr = ['j.education_type' => $this->person_education_id];
                break;
            case self::EDUCATION_RULE_TYPE_GT:
                $education_type = $this->getEducationType();
                if ($education_type) {
                    array_push($this->rule_condition_where_item, implode(',', $education_type));
                    $arr = ['j.education_type' => $education_type];
                }
                break;
        }

        return $arr;
    }

    /**
     * 对专业进行条件组装
     * @param $type 0不用匹配 1匹配专业
     * @return mixed
     */
    private function queryMajorRecommend($type)
    {
        $arr = [];
        switch ($type) {
            case self::MAJOR_RULE_TYPE_NO:
                break;
            case self::MAJOR_RULE_TYPE_YES:
                if ($this->person_major_id > 0) {
                    array_push($this->rule_condition_where_item, $this->person_major_id);
                    $arr = ['jmr.major_id' => $this->person_major_id];
                }
                break;
            case self::MAJOR_RULE_TYPE_NO_LIMIT:
                array_push($this->rule_condition_where_item, self::MAJOR_ID_NO_LIMIT);
                $arr = ['jmr.major_id' => self::MAJOR_ID_NO_LIMIT];
                break;
            case self::MAJOR_RULE_TYPE_UNKNOWN:
                array_push($this->rule_condition_where_item, self::MAJOR_ID_UNKNOWN);
                $arr = ['jmr.major_id' => self::MAJOR_ID_UNKNOWN];
                break;
            case self::MAJOR_RULE_TYPE_NONE:
                array_push($this->rule_condition_where_item, '-1');
                $arr = ['j.major_id' => ''];
                break;
            case self::MAJOR_RULE_TYPE_UNKNOWN_OR_NONE:
                array_push($this->rule_condition_where_item, implode(',', [
                    '-1',
                    self::MAJOR_ID_UNKNOWN,
                ]));
                $arr = [
                    'or',
                    [
                        'jmr.major_id' => [
                            self::MAJOR_ID_UNKNOWN,
                            self::MAJOR_ID_NO_LIMIT,
                        ],
                    ],
                    ['j.major_id' => ''],
                ];
                break;
        }

        return $arr;
    }

    /**
     * 对地区进行条件组装
     * @param $type 0不用匹配 1匹配 2匹配城市 3匹配省份 4匹配重点城市 5匹配非重点城市 6意向城市所属省份匹配 7意向地区或所属省份匹配  8意向地区所属省份不匹配
     * @return mixed
     */
    private function queryAreaRecommend($type)
    {
        $this->area_bool    = false;
        $city               = array_keys($this->person_area_ids['city']);
        $province_city      = array_keys($this->person_area_ids['province_city']);
        $city_province_city = array_keys($this->person_area_ids['city_province_city']);
        $key_city           = self::TOP_AREA_KEY_CITY;//重点城市
        $on_key_city        = array_diff($key_city, $city);//不含自身的重点城市
        $key_city_sum       = array_unique(array_merge($key_city, $city, $province_city, $city_province_city));
        $arr                = [];
        switch ($type) {
            case self::AREA_RULE_TYPE_NO:
                $this->area_bool = true;
                break;
            case self::AREA_RULE_TYPE_YES:
                $city_ids = array_unique(array_merge($city, $province_city));
                if ($city_ids) {
                    array_push($this->rule_condition_where_item, implode(',', $city_ids));
                    $arr = [
                        'j.city_id' => $city_ids,
                    ];
                } else {
                    $this->area_bool = true;
                }
                break;
            case self::AREA_RULE_TYPE_CITY:
                if ($city) {
                    array_push($this->rule_condition_where_item, implode(',', $city));
                    $arr = ['j.city_id' => $city];
                } else {
                    $this->area_bool = true;
                }
                break;
            case self::AREA_RULE_TYPE_PROVINCE:
                if ($province_city) {
                    array_push($this->rule_condition_where_item, implode(',', $province_city));
                    $arr = ['j.city_id' => $province_city];
                } else {
                    $this->area_bool = true;
                }
                break;
            case self::AREA_RULE_TYPE_KEY_CITY:
                if ($on_key_city) {
                    array_push($this->rule_condition_where_item, implode(',', $on_key_city));
                    $arr = ['j.city_id' => $on_key_city];
                } else {
                    $this->area_bool = true;
                }
                break;
            case self::AREA_RULE_TYPE_ON_KEY_CITY:
                array_push($this->rule_condition_where_item, implode(',', $key_city_sum));
                $arr = [
                    'not in',
                    'j.city_id',
                    $key_city_sum,
                ];
                break;
            case self::AREA_RULE_TYPE_PROVINCE_CITY:
                if ($city_province_city) {
                    array_push($this->rule_condition_where_item, implode(',', $city_province_city));
                    $arr = ['j.city_id' => $city_province_city];
                } else {
                    $this->area_bool = true;
                }
                break;
            case self::AREA_RULE_TYPE_YES_CITY_OR_PROVINCE:
                $province_city_ids = array_unique(array_merge($city_province_city, $province_city));
                if ($province_city_ids) {
                    array_push($this->rule_condition_where_item, implode(',', $province_city_ids));
                    $arr = [
                        'j.city_id' => $province_city_ids,
                    ];
                } else {
                    $this->area_bool = true;
                }
                break;
        }

        return $arr;
    }

    /**
     * 处理学历类型
     * @return array|int[]
     */
    private function getEducationType()
    {
        $education_type = [];
        switch ($this->person_education_id) {
            case 1:
                $education_type = [
                    BaseResumeEducation::EDUCATION_TYPE_OTHER_CODE,
                ];
                break;
            case 2:
                $education_type = [
                    BaseResumeEducation::EDUCATION_TYPE_JUNIOR_CODE,
                    BaseResumeEducation::EDUCATION_TYPE_OTHER_CODE,
                ];
                break;
            case 3:
                $education_type = [
                    BaseResumeEducation::EDUCATION_TYPE_JUNIOR_CODE,
                    BaseResumeEducation::EDUCATION_TYPE_UNDERGRADUATE_CODE,
                    BaseResumeEducation::EDUCATION_TYPE_OTHER_CODE,
                ];
                break;
            case 4:
                $education_type = [
                    BaseResumeEducation::EDUCATION_TYPE_JUNIOR_CODE,
                    BaseResumeEducation::EDUCATION_TYPE_UNDERGRADUATE_CODE,
                    BaseResumeEducation::EDUCATION_TYPE_MASTER_CODE,
                    BaseResumeEducation::EDUCATION_TYPE_OTHER_CODE,
                ];
                break;
            case 5:
                $education_type = [];
                break;
        }

        return $education_type;
    }

    /**
     * 地区ID处理
     ** 原因：人才地区选择了多个有城市、省份
     ** 处理结果：将人才地区id处理成集合，城市(key:city)与省份(key:province)拆成两个集合
     */
    private function processingArea()
    {
        //直接获取人才地区ID
        $area_id     = $this->person_area_ids;
        $area_id_arr = explode(',', $area_id);
        //获取所有地区信息
        $area_data = BaseArea::find()
            ->andWhere([
                'in',
                'id',
                $area_id_arr,
            ])
            ->asArray()
            ->all();
        //区分
        $city               = [];//城市
        $city_province      = [];//城市所属省份
        $city_province_city = [];//城市所属省份下的所有城市
        $province           = [];//省份
        $province_city      = [];//省份下的城市
        foreach ($area_data as $item) {
            if ($item['level'] == 1) {
                $province[$item['id']] = $item;
            }
            if ($item['level'] == 2) {
                $city[$item['id']] = $item;
            }
        }
        $city_ids = [];
        //对意向城市处理
        if (count($city) > 0) {
            $city_ids           = array_keys($city);
            $city_parent_ids    = array_unique(array_column($city, 'parent_id'));
            $city_province_data = BaseArea::find()
                ->andWhere([
                    'id'    => $city_parent_ids,
                    'level' => 1,
                ])
                ->asArray()
                ->all();
            foreach ($city_province_data as $city_province_item) {
                $city_province[$city_province_item['id']] = $city_province_item;
            }
            if (count($city_province) > 0) {
                $city_province_ids       = array_keys($city_province);
                $city_province_city_data = BaseArea::find()
                    ->andWhere([
                        'parent_id' => $city_province_ids,
                        'level'     => 2,
                    ])
                    ->asArray()
                    ->all();
                foreach ($city_province_city_data as $city_province_city_item) {
                    $city_province_city[$city_province_city_item['id']] = $city_province_city_item;
                }
            }
        }
        //省份不为空时候获取省份下的所有城市，同时剔除意向城市
        if (count($province) > 0) {
            $province_ids       = array_keys($province);
            $province_city_data = BaseArea::find()
                ->andWhere([
                    'parent_id' => $province_ids,
                    'level'     => 2,
                ])
                ->asArray()
                ->all();
            foreach ($province_city_data as $province_item) {
                if (!in_array($province_item['id'], $city_ids)) {
                    $province_city[$province_item['id']] = $province_item;
                }
            }
        }
        $this->person_area_ids = [
            'city'               => $city,
            'city_province'      => $city_province,
            'city_province_city' => $city_province_city,
            'province'           => $province,
            'province_city'      => $province_city,
        ];

        return true;
    }

    /**
     * 人才专业ID处理
     ** 原因：由于专业是三级
     ** 处理结果：统一将三级转到两级
     * @throws Exception
     */
    private function processingMajor()
    {
        //由于初始化时候已经判断数据的存在性  这里直接获取
        $person_major_id = $this->person_major_id;
        //获取当前三级专业信息
        if ($person_major_id > 0) {
            $cur_person_major_info = BaseMajor::findOne($person_major_id);
            if ($cur_person_major_info && $cur_person_major_info->parent_id > 0) {
                //人才三级专业存在 则获取二级专业
                $parent_person_major_info = BaseMajor::findOne($cur_person_major_info->parent_id);
                $this->person_major_id    = $parent_person_major_info->id;
            } else {
                $this->person_major_id = 0;
            }
        }

        return true;
    }

    /**
     * 验证当前规则条件是否正常查询
     * @return bool
     */
    private function verifyCondition()
    {
        //将规则条件拼接
        $where_str = implode('_', $this->rule_condition_where_item);
        if (in_array($where_str, $this->rule_condition_where)) {
            return true;
        } else {
            array_push($this->rule_condition_where, $where_str);

            return false;
        }
    }

    /**
     * 写入匹配表
     * @param $list
     */
    private function updateMatchTable($list)
    {
        //意向职位_意向地区_学历水平_学科专业_类表类型_分页
        $area_id_arr = explode(',', $this->intention_info->area_id);
        sort($area_id_arr);
        $area_id_str     = implode(',', $area_id_arr);
        $job_category_id = $this->intention_info->job_category_id;
        $area_ids        = $area_id_str;
        $major_id        = $this->resume_education_info->major_id;
        $education_id    = $this->resume_education_info->education_id;
        foreach ($list as $item) {
            $model = BaseResumeMatchJob::findOne([
                'job_category_id' => $job_category_id,
                'area_ids'        => $area_ids,
                'major_id'        => $major_id,
                'education_id'    => $education_id,
                'job_id'          => $item['job_id'],
            ]);
            if (!$model) {
                $model                  = new BaseResumeMatchJob();
                $model->job_category_id = $job_category_id;
                $model->area_ids        = $area_ids;
                $model->major_id        = $major_id;
                $model->education_id    = $education_id;
                $model->job_id          = $item['job_id'];
                $model->add_time        = CUR_DATETIME;
            }
            $model->match_rate  = $item['match_rate'];
            $model->update_time = CUR_DATETIME;
            $model->save();
        }
    }


    //==============================谁看过我=====================================================================
    //==============================下方逻辑全部是处理谁看过我=======================================================
    private $seen_company_id;//保存单位ID
    private $seen_resume_id;//保存简历ID
    private $seen_resume_job_category_ids = [];//保存简历的求职意向的职位类型ID
    private $seen_init_bool               = true;//初始化
    const SEEN_RECOMMEND_LIMIT = 2;//推荐数量

    /**
     * 谁看过我-初始化
     * @param $params
     *               [
     *               'resume_id'  => '简历ID',
     *               'company_id' => '单位ID'
     *               ]
     */
    public function initRecommendJob($params)
    {
        //开始初始化
        //列表参数初始化
        $this->seen_resume_id = (isset($params['resume_id']) && $params['resume_id'] > 0) ? $params['resume_id'] : 0;
        if ($this->seen_resume_id <= 0) {
            throw new Exception('参数错误,未传入正确的简历ID');
        }
        $this->seen_company_id = (isset($params['company_id']) && $params['company_id'] > 0) ? $params['company_id'] : 0;
        if ($this->seen_company_id <= 0) {
            throw new Exception('参数错误,未传入正确的单位ID');
        }
        //初始化简历的求职意向-职位类型
        ////获取简历所有求职意向
        $seen_resume_job_category_ids = BaseResumeIntention::find()
            ->select('job_category_id')
            ->where([
                'resume_id' => $this->seen_resume_id,
                'status'    => BaseResumeIntention::STATUS_ACTIVE,
            ])
            ->column();

        $this->seen_init_bool = false;

        return $this;
    }

    /**
     * 谁看过我-推荐职位执行程序
     * @return array
     * @throws Exception
     */
    public function runRecommendJob()
    {
        if ($this->seen_init_bool) {
            throw new Exception('请先初始化');
        }
        //获取推荐数据
        //暂时没有其他业务---直接sql处理业务需求
        //因为不需要这里做业务逻辑---直接返回职位ID给到外部处理
        $query = BaseJob::find()
            ->alias('j')
            ->select(['j.id'])
            ->leftJoin(['jtc' => BaseJobTopConfig::tableName()], 'j.id=jtc.job_id and jtc.status=1')
            ->andWhere(['j.company_id' => $this->seen_company_id]);
        if (count($this->seen_resume_job_category_ids) > 0) {
            $query->andWhere(['j.job_category_id' => $this->seen_resume_job_category_ids]);
        }
        $job_ids = $query->orderBy('jtc.status desc,j.refresh_time desc')
            ->limit(self::SEEN_RECOMMEND_LIMIT)
            ->column();

        return [
            'job_ids' => $job_ids,
        ];
    }
}