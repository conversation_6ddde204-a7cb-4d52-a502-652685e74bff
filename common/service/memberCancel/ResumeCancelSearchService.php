<?php

namespace common\service\memberCancel;

use common\base\models\BaseAdmin;
use common\base\models\BaseMember;
use common\base\models\BaseResume;
use common\base\models\BaseResumeCancelLog;
use common\base\models\BaseResumeCancelSnapshot;
use common\helpers\MaskHelper;
use common\helpers\TimeHelper;
use common\base\BaseActiveRecord;
use Yii;

/**
 * 求职者注销申请搜索服务
 * 用于运营后台的注销申请列表查询和管理
 */
class ResumeCancelSearchService
{

    /**
     * 获取求职者注销申请列表（运营后台使用）
     * @param array $params 查询参数
     * @return array 返回列表数据和分页信息
     * @throws \Exception
     */
    public static function getList($params)
    {
        // 构建基础查询
        $query = BaseResumeCancelLog::find()
            ->alias('rcl')
            ->leftJoin(['m' => BaseMember::tableName()], 'm.id = rcl.member_id')
            ->leftJoin(['r' => BaseResume::tableName()], 'r.id = rcl.resume_id')
            ->leftJoin(['rcs' => BaseResumeCancelSnapshot::tableName()], 'rcs.cancel_log_id = rcl.id')
            ->leftJoin(['a' => BaseAdmin::tableName()], 'a.id = rcl.admin_id');

        // 搜索条件
        // 1. 按注销日志ID搜索
        if (!empty($params['cancelLogId'])) {
            $query->andWhere(['rcl.id' => $params['cancelLogId']]);
        }

        // 2. 按用户手机号搜索（支持快照中的手机号）
        if (!empty($params['mobile'])) {
            $query->andWhere([
                'or',
                [
                    'like',
                    'm.mobile',
                    $params['mobile'],
                ],
                [
                    'like',
                    'rcs.mobile',
                    $params['mobile'],
                ],
            ]);
        }

        // 3. 按用户姓名搜索（支持快照中的姓名）
        if (!empty($params['name'])) {
            $query->andWhere([
                'or',
                [
                    'like',
                    'r.name',
                    $params['name'],
                ],
                [
                    'like',
                    'rcs.name',
                    $params['name'],
                ],
            ]);
        }

        // 4. 按邮箱搜索（支持快照中的邮箱）
        if (!empty($params['email'])) {
            $query->andWhere([
                'or',
                [
                    'like',
                    'm.email',
                    $params['email'],
                ],
                [
                    'like',
                    'rcs.email',
                    $params['email'],
                ],
            ]);
        }

        // 5. 按注销状态筛选
        if (!empty($params['status'])) {
            $query->andWhere(['rcl.status' => $params['status']]);
        }

        // 6. 按注销原因类型筛选
        if (!empty($params['cancelReasonType'])) {
            $query->andWhere(['rcl.cancel_reason_type' => $params['cancelReasonType']]);
        }

        // 7. 按短信发送状态筛选
        if (!empty($params['smsStatus'])) {
            $query->andWhere(['rcl.sms_status' => $params['smsStatus']]);
        }

        // 8. 按申请时间范围筛选
        if (!empty($params['applyTimeStart']) && !empty($params['applyTimeEnd'])) {
            $query->andWhere([
                'between',
                'rcl.apply_time',
                TimeHelper::dayToBeginTime($params['applyTimeStart']),
                TimeHelper::dayToEndTime($params['applyTimeEnd']),
            ]);
        }

        // 9. 按冷静期结束时间范围筛选
        if (!empty($params['cooldownEndTimeStart']) && !empty($params['cooldownEndTimeEnd'])) {
            $query->andWhere([
                'between',
                'rcl.cooldown_end_time',
                TimeHelper::dayToBeginTime($params['cooldownEndTimeStart']),
                TimeHelper::dayToEndTime($params['cooldownEndTimeEnd']),
            ]);
        }

        // 10. 按完成时间范围筛选
        if (!empty($params['completeTimeStart']) && !empty($params['completeTimeEnd'])) {
            $query->andWhere([
                'between',
                'rcl.complete_time',
                TimeHelper::dayToBeginTime($params['completeTimeStart']),
                TimeHelper::dayToEndTime($params['completeTimeEnd']),
            ]);
        }

        // 11. 按操作管理员筛选
        if (!empty($params['adminId'])) {
            $query->andWhere(['rcl.admin_id' => $params['adminId']]);
        }

        // 12. 按IP地址搜索
        if (!empty($params['ip'])) {
            $query->andWhere([
                'like',
                'rcl.ip',
                $params['ip'],
            ]);
        }

        // 获取总数
        $total = $query->count();

        // 分页处理
        $pageSize = $params['pageSize'] ?: Yii::$app->params['defaultPageSize'];
        $pages    = BaseActiveRecord::setPage($total, $params['page'], $pageSize);

        // 排序（默认按申请时间倒序）
        $orderBy = 'rcl.apply_time DESC, rcl.id DESC';
        if (!empty($params['sortField']) && !empty($params['sortOrder'])) {
            $sortField = $params['sortField'];
            $sortOrder = strtoupper($params['sortOrder']) === 'ASC' ? 'ASC' : 'DESC';

            // 允许的排序字段
            $allowedSortFields = [
                'applyTime'        => 'rcl.apply_time',
                'cooldownEndTime'  => 'rcl.cooldown_end_time',
                'completeTime'     => 'rcl.complete_time',
                'status'           => 'rcl.status',
                'cancelReasonType' => 'rcl.cancel_reason_type',
                'smsStatus'        => 'rcl.sms_status',
            ];

            if (isset($allowedSortFields[$sortField])) {
                $orderBy = $allowedSortFields[$sortField] . ' ' . $sortOrder . ', rcl.id DESC';
            }
        }

        // 查询数据
        $list = $query->select([
            'rcl.id',
            'rcl.member_id',
            'rcl.resume_id',
            'rcl.admin_id',
            'rcl.cancel_reason_type',
            'rcl.cancel_reason_detail',
            'rcl.apply_time',
            'rcl.cooldown_end_time',
            'rcl.complete_time',
            'rcl.withdraw_time',
            'rcl.status',
            'rcl.ip',
            'rcl.sms_status',
            'rcl.resume_setting',
            // 用户信息（优先使用快照数据）
            'COALESCE(rcs.mobile, m.mobile) as mobile',
            'COALESCE(rcs.mobile_code, m.mobile_code) as mobile_code',
            'COALESCE(rcs.email, m.email) as email',
            'COALESCE(rcs.name, r.name) as name',
            'COALESCE(rcs.username, m.username) as username',
            // 管理员信息
            'a.name as admin_name',
            // 会员状态
            'm.status as member_status',
            'm.cancel_status as member_cancel_status',
        ])
            ->offset($pages['offset'])
            ->limit($pages['limit'])
            ->orderBy($orderBy)
            ->asArray()
            ->all();

        // 数据格式化处理
        foreach ($list as &$item) {
            // 格式化状态文本
            $item['statusText']           = BaseResumeCancelLog::getStatusName($item['status']);
            $item['cancelReasonTypeText'] = BaseResumeCancelLog::getCancelReasonTypeName($item['cancel_reason_type']);
            $item['smsStatusText']        = BaseResumeCancelLog::SMS_STATUS_LIST[$item['sms_status']] ?? '未知';

            // 格式化时间
            $item['applyTimeFormat']       = $item['apply_time'] == TimeHelper::ZERO_TIME ? '-' : $item['apply_time'];
            $item['cooldownEndTimeFormat'] = $item['cooldown_end_time'] == TimeHelper::ZERO_TIME ? '-' : $item['cooldown_end_time'];
            $item['completeTimeFormat']    = $item['complete_time'] == TimeHelper::ZERO_TIME ? '-' : $item['complete_time'];
            $item['withdrawTimeFormat']    = $item['withdraw_time'] == TimeHelper::ZERO_TIME ? '-' : $item['withdraw_time'];

            // 脱敏处理手机号
            if ($item['mobile']) {
                $item['mobileMasked'] = MaskHelper::getPhoneLast($item['mobile']);
            }

            // 脱敏处理邮箱
            if ($item['email']) {
                $item['emailMasked'] = MaskHelper::getEmail($item['email']);
            }

            // 计算剩余冷静期天数（仅对申请中状态）
            if ($item['status'] == BaseResumeCancelLog::STATUS_APPLYING && $item['cooldown_end_time']) {
                $remainingDays                 = max(0, ceil((strtotime($item['cooldown_end_time']) - time()) / 86400));
                $item['remainingCooldownDays'] = $remainingDays;
            } else {
                $item['remainingCooldownDays'] = 0;
            }

            // 操作类型标识
            $item['operationType']     = $item['admin_id'] > 0 ? 'manual' : 'system';
            $item['operationTypeText'] = $item['admin_id'] > 0 ? '人工操作' : '系统自动';

            // 解析消息通知设置
            if ($item['resume_setting']) {
                $resumeSetting               = json_decode($item['resume_setting'], true);
                $item['resumeSettingParsed'] = $resumeSetting ?: [];
            } else {
                $item['resumeSettingParsed'] = [];
            }
        }

        return [
            'list'  => $list,
            'pages' => [
                'total'      => (int)$total,
                'pageSize'   => (int)$pageSize,
                'page'       => (int)$params['page'],
                'totalPages' => $pages['total'],
            ],
        ];
    }

    /**
     * 获取注销申请详情（运营后台使用）
     * @param int $cancelLogId 注销日志ID
     * @return array 详情数据
     * @throws \Exception
     */
    public static function getDetail($cancelLogId)
    {
        if (empty($cancelLogId)) {
            throw new \Exception('注销日志ID不能为空');
        }

        $detail = BaseResumeCancelLog::find()
            ->alias('rcl')
            ->leftJoin(['m' => BaseMember::tableName()], 'm.id = rcl.member_id')
            ->leftJoin(['r' => BaseResume::tableName()], 'r.id = rcl.resume_id')
            ->leftJoin(['rcs' => BaseResumeCancelSnapshot::tableName()], 'rcs.cancel_log_id = rcl.id')
            ->leftJoin(['a' => BaseAdmin::tableName()], 'a.id = rcl.admin_id')
            ->select([
                'rcl.*',
                // 用户信息（优先使用快照数据）
                'COALESCE(rcs.mobile, m.mobile) as mobile',
                'COALESCE(rcs.mobile_code, m.mobile_code) as mobile_code',
                'COALESCE(rcs.email, m.email) as email',
                'COALESCE(rcs.name, r.name) as name',
                'COALESCE(rcs.username, m.username) as username',
                // 管理员信息
                'a.name as admin_name',
                // 会员状态
                'm.status as member_status',
                'm.cancel_status as member_cancel_status',
                // 快照数据
                'rcs.resume_data_json',
            ])
            ->where(['rcl.id' => $cancelLogId])
            ->asArray()
            ->one();

        if (!$detail) {
            throw new \Exception('注销申请记录不存在');
        }

        // 格式化详情数据
        $detail['statusText']           = BaseResumeCancelLog::getStatusName($detail['status']);
        $detail['cancelReasonTypeText'] = BaseResumeCancelLog::getCancelReasonTypeName($detail['cancel_reason_type']);
        $detail['smsStatusText']        = BaseResumeCancelLog::SMS_STATUS_LIST[$detail['sms_status']] ?? '未知';

        $detail['applyTimeFormat']       = $detail['apply_time'] == TimeHelper::ZERO_TIME ? '-' : $detail['apply_time'];
        $detail['cooldownEndTimeFormat'] = $detail['cooldown_end_time'] == TimeHelper::ZERO_TIME ? '-' : $detail['cooldown_end_time'];
        $detail['completeTimeFormat']    = $detail['complete_time'] == TimeHelper::ZERO_TIME ? '-' : $detail['complete_time'];
        $detail['withdrawTimeFormat']    = $detail['withdraw_time'] == TimeHelper::ZERO_TIME ? '-' : $detail['withdraw_time'];

        // 脱敏处理
        if ($detail['mobile']) {
            $detail['mobile'] = MaskHelper::getPhoneLast($detail['mobile']);
        }
        if ($detail['email']) {
            $detail['email'] = MaskHelper::getEmail($detail['email']);
        }

        // 计算剩余冷静期天数
        if ($detail['status'] == BaseResumeCancelLog::STATUS_APPLYING && $detail['cooldown_end_time']) {
            $remainingDays                   = max(0, ceil((strtotime($detail['cooldown_end_time']) - time()) / 86400));
            $detail['remainingCooldownDays'] = $remainingDays;
        } else {
            $detail['remainingCooldownDays'] = 0;
        }

        // 操作类型
        $detail['operationType']     = $detail['admin_id'] > 0 ? 'manual' : 'system';
        $detail['operationTypeText'] = $detail['admin_id'] > 0 ? '人工操作' : '系统自动';

        // 解析消息通知设置和简历数据
        if ($detail['resume_setting']) {
            $detail['resumeSettingParsed'] = json_decode($detail['resume_setting'], true) ?: [];
        } else {
            $detail['resumeSettingParsed'] = [];
        }

        if ($detail['resume_data_json']) {
            $detail['resumeDataParsed'] = json_decode($detail['resume_data_json'], true) ?: [];
        } else {
            $detail['resumeDataParsed'] = [];
        }

        return $detail;
    }

    /**
     * 获取统计数据（运营后台使用）
     * @param array $params 查询参数
     * @return array 统计数据
     */
    public static function getStatistics($params = [])
    {
        $baseQuery = BaseResumeCancelLog::find();

        // 时间范围筛选
        if (!empty($params['startDate']) && !empty($params['endDate'])) {
            $baseQuery->andWhere([
                'between',
                'apply_time',
                TimeHelper::dayToBeginTime($params['startDate']),
                TimeHelper::dayToEndTime($params['endDate']),
            ]);
        }

        // 总申请数
        $totalApplies = (clone $baseQuery)->count();

        // 按状态统计
        $statusStats = [];
        foreach (BaseResumeCancelLog::STATUS_LIST as $status => $statusName) {
            $count         = (clone $baseQuery)->where(['status' => $status])
                ->count();
            $statusStats[] = [
                'status'     => $status,
                'statusName' => $statusName,
                'count'      => (int)$count,
                'percentage' => $totalApplies > 0 ? round($count / $totalApplies * 100, 2) : 0,
            ];
        }

        // 按注销原因统计
        $reasonStats = [];
        foreach (BaseResumeCancelLog::CANCEL_REASON_TYPE_LIST as $reasonType => $reasonName) {
            $count         = (clone $baseQuery)->where(['cancel_reason_type' => $reasonType])
                ->count();
            $reasonStats[] = [
                'reasonType' => $reasonType,
                'reasonName' => $reasonName,
                'count'      => (int)$count,
                'percentage' => $totalApplies > 0 ? round($count / $totalApplies * 100, 2) : 0,
            ];
        }

        // 按短信发送状态统计
        $smsStats = [];
        foreach (BaseResumeCancelLog::SMS_STATUS_LIST as $smsStatus => $smsStatusName) {
            $count      = (clone $baseQuery)->where(['sms_status' => $smsStatus])
                ->count();
            $smsStats[] = [
                'smsStatus'     => $smsStatus,
                'smsStatusName' => $smsStatusName,
                'count'         => (int)$count,
                'percentage'    => $totalApplies > 0 ? round($count / $totalApplies * 100, 2) : 0,
            ];
        }

        // 今日新增申请
        $todayApplies = (clone $baseQuery)->andWhere([
            'between',
            'apply_time',
            TimeHelper::dayToBeginTime(date('Y-m-d')),
            TimeHelper::dayToEndTime(date('Y-m-d')),
        ])
            ->count();

        // 本周新增申请
        $weekStart   = date('Y-m-d', strtotime('this week monday'));
        $weekApplies = (clone $baseQuery)->andWhere([
            'between',
            'apply_time',
            TimeHelper::dayToBeginTime($weekStart),
            TimeHelper::dayToEndTime(date('Y-m-d')),
        ])
            ->count();

        // 本月新增申请
        $monthStart   = date('Y-m-01');
        $monthApplies = (clone $baseQuery)->andWhere([
            'between',
            'apply_time',
            TimeHelper::dayToBeginTime($monthStart),
            TimeHelper::dayToEndTime(date('Y-m-d')),
        ])
            ->count();

        return [
            'overview'    => [
                'totalApplies' => (int)$totalApplies,
                'todayApplies' => (int)$todayApplies,
                'weekApplies'  => (int)$weekApplies,
                'monthApplies' => (int)$monthApplies,
            ],
            'statusStats' => $statusStats,
            'reasonStats' => $reasonStats,
            'smsStats'    => $smsStats,
        ];
    }

    /**
     * 获取筛选选项数据（运营后台使用）
     * @return array 筛选选项
     */
    public static function getFilterOptions()
    {
        return [
            'statusOptions' => array_map(function ($status, $statusName) {
                return [
                    'value' => $status,
                    'label' => $statusName,
                ];
            }, array_keys(BaseResumeCancelLog::STATUS_LIST), BaseResumeCancelLog::STATUS_LIST),

            'cancelReasonOptions' => array_map(function ($reasonType, $reasonName) {
                return [
                    'value' => $reasonType,
                    'label' => $reasonName,
                ];
            }, array_keys(BaseResumeCancelLog::CANCEL_REASON_TYPE_LIST), BaseResumeCancelLog::CANCEL_REASON_TYPE_LIST),

            'smsStatusOptions' => array_map(function ($smsStatus, $smsStatusName) {
                return [
                    'value' => $smsStatus,
                    'label' => $smsStatusName,
                ];
            }, array_keys(BaseResumeCancelLog::SMS_STATUS_LIST), BaseResumeCancelLog::SMS_STATUS_LIST),
        ];
    }
}
