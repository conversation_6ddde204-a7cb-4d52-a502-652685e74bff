<?php

namespace common\service\job;

use admin\models\Announcement;
use common\base\models\BaseAdmin;
use common\base\models\BaseAnnouncement;
use common\base\models\BaseAnnouncementHandleLog;
use common\base\models\BaseCompany;
use common\base\models\BaseCompanyMemberInfo;
use common\base\models\BaseDictionary;
use common\base\models\BaseJob;
use common\base\models\BaseJobContact;
use common\base\models\BaseJobContactSynergy;
use common\base\models\BaseJobEdit;
use common\base\models\BaseJobExtra;
use common\base\models\BaseJobHandleLog;
use common\base\models\BaseMember;
use common\helpers\IpHelper;
use common\helpers\ValidateHelper;
use yii\base\Exception;

class AddService extends BaseService
{

    /**
     * 有很多种添加的类型
     */
    const TYPE_ALONE   = 2;
    const TYPE_ARTICLE = 1;

    public $type;
    public $saveType;

    /**
     * 执行添加
     * @throws Exception
     */
    public function run()
    {
        $this->actionType = self::ACTION_TYPE_ADD;
        if ($this->jobId && $this->operatorType == self::OPERATOR_TYPE_COMPANY) {
            $this->actionType = self::ACTION_TYPE_EDIT;
        }
        $this->beforeRun();

        if (!$this->jobData['jobId'] || $this->jobModel->status != BaseJob::STATUS_ONLINE) {
            $this->afterRun();
        }
        switch ($this->saveType) {
            case self::SAVE_TYPE_STAGING:
                $this->staging();
                break;
            case self::SAVE_TYPE_AUDIT:
                $this->audit();
                break;
            default:
                throw new Exception('添加类型错误');
        }

        //返回职位ID
        return $this->jobId;
    }

    public function setStaging(): AddService
    {
        $this->saveType = self::SAVE_TYPE_STAGING;

        return $this;
    }

    public function setAudit(): AddService
    {
        $this->saveType = self::SAVE_TYPE_AUDIT;

        return $this;
    }

    /**
     * 这里把所有应该有的数据都设置好
     *
     * @param $data
     * @return $this
     * @throws Exception
     */
    public function setDate($data): AddService
    {
        // 默认情况下,职位的类型是纯职位,
        $this->type      = $data['type'] ?: self::TYPE_ALONE;
        $this->companyId = $data['companyId'];
        if (!$this->companyId) {
            throw new Exception('请选择所属企业');
        }

        $this->setCompany();
        if ($this->companyModel->is_cooperation == BaseAnnouncement::IS_COOPERATION_NO) {
            if (isset($data['deliveryWay'])) {
                //非合作单位去掉delivery_way字段 永远走else分支
                unset($data['deliveryWay']);
            }
            if ($data['extraNotifyAddress']) {
                throw new Exception('非合作单位不支持填写邮件通知地址');
            }
        }
        //特殊处理单位端过来的数据，且携带source ，source=1跟公告 source =2跟随自己
        if ($this->operatorType == self::OPERATOR_TYPE_COMPANY && isset($data['source']) && $data['source'] == 1) {
            //谨慎处理,才能进入特殊处理否侧让它独立
            if ($data['announcementId'] > 0) {
                //获取公告数据
                $announcementInfo         = BaseAnnouncement::findOne($data['announcementId']);
                $dataApplyTypeStr         = '';
                $announcementApplyTypeStr = '';
                //升-排序报名方式
                if ($data['applyType']) {
                    $dataApplyType = explode(',', $data['applyType']);
                    sort($dataApplyType);
                    $dataApplyTypeStr = implode(',', $dataApplyType);
                }
                if ($announcementInfo->apply_type) {
                    $announcementApplyType = explode(',', $announcementInfo->apply_type);
                    sort($announcementApplyType);
                    $announcementApplyTypeStr = implode(',', $announcementApplyType);
                }
                //是否用自己配置
                $myData = true;//用自己的
                if ($dataApplyTypeStr == $announcementApplyTypeStr && $data['applyAddress'] == $announcementInfo->apply_address && $data['extraNotifyAddress'] == $announcementInfo->extra_notify_address) {
                    $myData = false;//说明跟公告相同那就是跟公告
                }
                if (!$myData) {//跟随公告
                    //将参数清理为空跟随公告数据
                    $data['applyType']          = '';
                    $data['applyAddress']       = '';
                    $data['extraNotifyAddress'] = '';
                    $data['deliveryWay']        = 0;
                    $data['deliveryType']       = 0;
                }
            }
        }

        if (strtotime($data['periodDate']) > 1 && strtotime($data['periodDate']) < strtotime(date('Y-m-d'))) {
            throw new Exception('职位有效时间不能小于当前时间');
        }
        if ($data['jobId']) {
            $this->setJob($data['jobId']);
        }
        if ($this->operatorType == self::OPERATOR_TYPE_ADMIN) {
            // 运营后台添加的
            $this->adminModel   = BaseAdmin::findOne($this->operatorId);
            $this->operatorName = $this->adminModel->name;
        }

        if ($data['auditStatus'] == BaseJob::AUDIT_STATUS_WAIT_AUDIT) {
            $data['applyAuditTime'] = CUR_DATETIME;
        }
        // 开始组装数据
        $data['memberId'] = $this->companyMemberId;

        // 薪资范围(非年薪)
        if ($data['wageType'] != BaseJob::WAGE_TYPE_YEAR) {
            // 如果非面议(前端直接传了类型过来)
            if ($data['isNegotiable'] == BaseJob::IS_NEGOTIABLE_NO) {
                if (!empty($data['wageId'])) {
                    $wageInfo        = BaseDictionary::getMinAndMaxWage($data['wageId']);
                    $data['minWage'] = (int)$wageInfo['min'];
                    $data['maxWage'] = (int)$wageInfo['max'];
                }
            }
        }
        //报名方式与通知地址不可同时填写
        if ($data['applyType'] && $data['extraNotifyAddress']) {
            throw new Exception('报名方式与投递通知邮箱不可同时填写');
        }
        if ($this->companyModel->is_cooperation == BaseAnnouncement::IS_COOPERATION_NO && isset($data['deliveryWay']) && $data['deliveryWay'] == BaseJob::DELIVERY_WAY_PLATFORM) {
            throw new Exception('报名方式填写错误');
        }
        if ($data['deliveryWay'] > 0) {
            if ($data['deliveryWay'] == BaseJob::DELIVERY_WAY_EMAIL_LINK) {
                if (empty($data['applyType']) || empty($data['applyAddress'])) {
                    throw new Exception('报名方式没有勾选或者投递地址为空');
                }
                $applyTypeArr = explode(',', $data['applyType']);
                $isEmail      = in_array(BaseJob::ATTRIBUTE_APPLY_EMAIL, $applyTypeArr);
                //校验
                if ($isEmail) {
                    BaseJob::checkEmailApplyAddress($data['applyAddress']);
                } else {
                    if (!ValidateHelper::isUrl($data['applyAddress'])) {
                        throw new Exception('单位报名网址格式错误');
                    }
                }
                //BaseJob::validateApplyAddress($applyTypeArr, $data['applyAddress']);
                if ($isEmail) {
                    $data['deliveryWay'] = BaseJob::DELIVERY_WAY_EMAIL;
                } else {
                    $data['deliveryWay'] = BaseJob::DELIVERY_WAY_LINK;
                }
            } else {//deliveryWay=1
                $data['applyType']    = '';
                $data['applyAddress'] = '';
            }
        } else {
            if ($data['applyType']) {
                $applyTypeArr = explode(',', $data['applyType']);
                $isEmail      = in_array(BaseJob::ATTRIBUTE_APPLY_EMAIL, $applyTypeArr);
                if (empty($data['applyAddress'])) {
                    throw new Exception('投递地址不能为空');
                }
                //校验
                if ($isEmail) {
                    BaseJob::checkEmailApplyAddress($data['applyAddress']);
                } else {
                    if (!ValidateHelper::isUrl($data['applyAddress'])) {
                        throw new Exception('单位报名网址格式错误');
                    }
                }
                //BaseJob::validateApplyAddress($applyTypeArr, $data['applyAddress']);
                //判断投递方式
                if ($isEmail) {
                    $data['deliveryWay'] = BaseJob::DELIVERY_WAY_EMAIL;
                } else {
                    $data['deliveryWay'] = BaseJob::DELIVERY_WAY_LINK;
                }
            } else {
                $data['applyType']    = '';
                $data['applyAddress'] = '';
                //判断投递方式
                if ((!isset($data['source']) || (isset($data['source']) && $data['source'] == 2))) {
                    $data['deliveryWay'] = BaseJob::DELIVERY_WAY_PLATFORM;
                } else {
                    $data['deliveryWay'] = 0;
                }
            }
        }
        $data['extraNotifyAddress'] = $data['extraNotifyAddress'] ?: '';
        //检查通知邮箱的格式
        if ($data['extraNotifyAddress']) {
            $data['extraNotifyAddress'] = BaseJob::checkEmailApplyAddress($data['extraNotifyAddress']);
        }

        //处理纯职位投递类型；选择投递类型那就站外投递，没有就站内投递
        if ($this->companyModel->is_cooperation == BaseAnnouncement::IS_COOPERATION_YES) {
            if ($data['deliveryWay'] == 0) {
                $data['deliveryType'] = 0;//跟公告---这里是处理公告的合作单位职位
            } elseif ($data['deliveryWay'] == BaseJob::DELIVERY_WAY_LINK) {
                $data['deliveryType'] = BaseJob::DELIVERY_TYPE_OUTSIDE;
            } else {
                $data['deliveryType'] = BaseJob::DELIVERY_TYPE_INSIDE;
            }

            //验证传递过来的协同设置与职位联系人的合法性----jobContactSynergy与jobContact
            //职位协同设置
            if ($data['jobContactSynergyIds']) {
                //验证协同账号的合法性
                if (count($data['jobContactSynergyIds']) > 3) {
                    throw new Exception('协同账号最多设置3个');
                }
                $jobContactSynergyIds = [];
                foreach ($data['jobContactSynergyIds'] as $item) {
                    $record_result_synergy = BaseCompanyMemberInfo::validateMemberRecordId($this->companyId, $item, 2);
                    if ($record_result_synergy) {
                        $jobContactSynergyIds[] = $item;
                    }
                }
                $data['jobContactSynergyIds'] = $jobContactSynergyIds;
            } else {
                $data['jobContactSynergyIds'] = [];
            }
            //职位联系人
            if (empty($data['jobContactId'])) {
                throw new Exception('职位联系人必须设置');
            }
            $record_result = BaseCompanyMemberInfo::validateMemberRecordId($this->companyId, $data['jobContactId'], 3);
            if (!$record_result) {
                throw new Exception('职位联系人设置错误');
            }
            //获取单位主账号信息
            $companyMemberInfo = BaseCompanyMemberInfo::findOne([
                'company_id'          => $this->companyId,
                'company_member_type' => BaseCompanyMemberInfo::COMPANY_MEMBER_TYPE_MAIN,
            ]);
            if (!in_array($data['jobContactId'],
                array_unique(array_merge($data['jobContactSynergyIds'], [$companyMemberInfo->id])))) {
                throw new Exception('职位联系人必须是协同账号或者是单位主账号');
            }
        } else {
            if ($data['deliveryWay'] == 0) {
                $data['deliveryType'] = 0;
            } else {
                $data['deliveryType'] = BaseJob::DELIVERY_TYPE_OUTSIDE;
            }
        }
        $this->jobData = $data;

        return $this;
    }

    /**
     * 暂存信息
     * @throws Exception
     */
    private function staging()
    {
        if ($this->jobId) {
            $this->save();
        } else {
            $this->add();
        }
    }

    /**
     * @throws Exception
     */
    private function audit()
    {
        // 有两种情况,第一种是直接提交审核,第二种是从编辑页面提交审核
        if ($this->jobId) {
            $this->edit();
        } else {
            $this->add();
        }
    }

    private function preview()
    {
    }

    /**
     * @throws Exception
     */
    private function add()
    {
        $data = $this->jobData;
        // 开始对数据进行校验
        $model = new BaseJob();
        if ($this->operatorType == self::OPERATOR_TYPE_COMPANY) {
            // 企业的
            $model->create_type = BaseJob::CREATE_TYPE_SELF;
            $model->creator     = $this->companyMemberModel->username;
        }

        if ($this->operatorType == self::OPERATOR_TYPE_ADMIN) {
            // 运营后台添加的x
            $model->create_type = BaseJob::CREATE_TYPE_AGENT;
            $model->creator     = $this->adminModel->name;
        }
        $delivery_limit_type_arr = [];
        if (isset($data['deliveryLimitTypeEducation']) && !empty($data['deliveryLimitTypeEducation'])) {
            array_push($delivery_limit_type_arr, $data['deliveryLimitTypeEducation']);
        }
        if (isset($data['deliveryLimitTypeFile']) && !empty($data['deliveryLimitTypeFile'])) {
            array_push($delivery_limit_type_arr, $data['deliveryLimitTypeFile']);
        }
        $delivery_limit_type = '';
        if (count($delivery_limit_type_arr) > 0) {
            $delivery_limit_type = implode(',', $delivery_limit_type_arr);
        }
        if (isset($data['deliveryLimitType']) && !empty($data['deliveryLimitType']) && empty($delivery_limit_type)) {
            $delivery_limit_type = $data['deliveryLimitType'];
        }

        $model->create_id            = $this->operatorId;
        $model->member_id            = $this->companyMemberId;
        $model->company_id           = $this->companyId;
        $model->name                 = $data['name'] ?: "";
        $model->code                 = $data['code'] ?: "";
        $model->job_category_id      = $data['jobCategoryId'] ?: "";
        $model->education_type       = $data['educationType'] ?: "";
        $model->major_id             = $data['majorId'] ?: "";
        $model->nature_type          = $data['natureType'] ?: "";
        $model->wage_type            = $data['wageType'] ?: "";
        $model->is_negotiable        = $data['isNegotiable'] ?: "";
        $model->min_wage             = $data['minWage'] ?: "";
        $model->max_wage             = $data['maxWage'] ?: "";
        $model->experience_type      = $data['experienceType'] ?: "";
        $model->age_type             = $data['ageType'] ?: '';
        $model->title_type           = $data['titleType'] ?: "";
        $model->political_type       = $data['politicalType'] ?: "";
        $model->abroad_type          = $data['abroadType'] ?: "";
        $model->amount               = $data['amount'] ?: "";
        $model->department           = $data['department'] ?: "";
        $model->province_id          = $data['provinceId'] ?: "";
        $model->city_id              = $data['cityId'] ?: "";
        $model->district_id          = $data['districtId'] ?: "";
        $model->address              = $data['address'] ?: "";
        $model->welfare_tag          = $data['welfareTag'] ?: '';
        $model->period_date          = $data['periodDate'] ?: "";
        $model->duty                 = $data['duty'] ?: "";
        $model->remark               = $data['remark'] ?: "";
        $model->requirement          = $data['requirement'] ?: "";
        $model->apply_type           = $data['applyType'] ?: "";
        $model->apply_address        = $data['applyAddress'] ?: '';
        $model->status               = $data['status'] ?: BaseJob::STATUS_WAIT;
        $model->is_show              = BaseJob::IS_SHOW_YES;
        $model->is_article           = BaseJob::IS_ARTICLE_NO;
        $model->audit_status         = $data['auditStatus'] ?: BaseJob::AUDIT_STATUS_WAIT;
        $model->apply_audit_time     = $data['applyAuditTime'] ?: '0000-00-00 00:00:00';
        $model->file_ids             = $data['fileIds'] ?: '';
        $model->delivery_limit_type  = $delivery_limit_type;
        $model->delivery_type        = $data['deliveryType'];
        $model->extra_notify_address = $data['extraNotifyAddress'] ?: '';
        $model->delivery_way         = $data['deliveryWay'];
        //判断操作平台为运营平台
        if ($this->operatorType == self::OPERATOR_TYPE_ADMIN) {
            $model->establishment_type = $data['establishmentType'] ?: '';
            $model->is_establishment   = !empty($data['establishmentType']) ? BaseJob::IS_ESTABLISHMENT_YES : BaseJob::IS_ESTABLISHMENT_NO;
        } else {
            $model->is_establishment = BaseJob::IS_ESTABLISHMENT_NO;
        }
        if (!$model->save()) {
            throw new Exception($model->getFirstErrorsMessage());
        }
        //判断合作单位
        if ($this->companyModel->is_cooperation == BaseAnnouncement::IS_COOPERATION_YES) {
            //写入职位联系人与职位协同
            $params_data = [
                'job_id'                  => $model->id,
                'company_id'              => $this->companyId,
                'job_contact_id'          => $data['jobContactId'],
                'announcement_id'         => $data['announcementId'],
                'job_contact_synergy_ids' => $data['jobContactSynergyIds'],
            ];
            $this->contact($params_data);
        }
        //写一条职位附属表逻辑
        BaseJobExtra::insertData([
            'job_id'          => $model->id,
            'announcement_id' => $data['announcementId'] ?: 0,
            'company_id'      => $this->companyId,
        ]);
        $this->jobModel = $model;

        $this->jobId = $model->id;
    }

    /**
     * @throws Exception
     *
     *  这个职位已经有了,其实现在算是编辑了
     */
    private function edit()
    {
        $jobId    = $this->jobId;
        $jobModel = $this->jobModel;
        $data     = $this->jobData;

        // 校验一下什么状态下是允许修改的
        /**
         * self::STATUS_OFFLINE
         * self::STATUS_ONLINE
         * self::STATUS_WAIT
         * self::STATUS_WAIT_AUDIT
         * self::STATUS_DELETE
         * self::STATUS_REFUSE_AUDIT
         */
        switch ($jobModel->status) {
            case BaseJob::STATUS_OFFLINE:
                throw new Exception('该职位已经下线,不能修改');
            case BaseJob::STATUS_WAIT_AUDIT:
                throw new Exception('该职位正在审核中,不能修改');
            case BaseJob::STATUS_DELETE:
                throw new Exception('该职位已经删除,不能修改');
            case BaseJob::STATUS_ONLINE:
                // 在线状态,允许修改,但是会去到待审核
                //判断职位投递性质
                if ($jobModel->delivery_type > 0) {
                    if ($data['deliveryType'] == 0 && $jobModel->announcement_id > 0) {//修改后跟公告
                        $announcementInfo = BaseAnnouncement::findOne($jobModel->announcement_id);
                        if ($jobModel->delivery_type != $announcementInfo->delivery_type) {
                            throw new Exception('你编辑使职位投递类型站内外发生变化，导致修改失败');
                        }
                    } elseif ($data['deliveryType'] > 0) {
                        if ($jobModel->delivery_type != $data['deliveryType']) {//跟自己
                            throw new Exception('你编辑使职位投递类型站内外发生变化，导致修改失败');
                        }
                    }
                } else {
                    if ($jobModel->announcement_id > 0) {
                        $announcementInfo = BaseAnnouncement::findOne($jobModel->announcement_id);
                        if ($jobModel->delivery_type == 0 && $data['deliveryType'] > 0) {//跟公告
                            if ($announcementInfo->delivery_type != $data['deliveryType']) {
                                throw new Exception('你编辑使职位投递类型站内外发生变化，导致修改失败');
                            }
                        }
                    }
                }
                break;
            case BaseJob::STATUS_WAIT:
                // 待审核状态,允许修改,但是会去到待审核
                break;
            case BaseJob::STATUS_REFUSE_AUDIT:
                // 审核拒绝状态,允许修改,但是会去到待审核
                break;
            default:
                throw new Exception('该职位状态不正确,不能修改');
        }

        $jobModel->create_id            = $this->operatorId;
        $jobModel->member_id            = $this->companyMemberModel->id;
        $jobModel->name                 = $data['name'];
        $jobModel->code                 = $data['code'];
        $jobModel->job_category_id      = $data['jobCategoryId'];
        $jobModel->education_type       = $data['educationType'];
        $jobModel->major_id             = $data['majorId'] ?: '';
        $jobModel->nature_type          = $data['natureType'];
        $jobModel->wage_type            = $data['wageType'];
        $jobModel->is_negotiable        = $data['isNegotiable'];
        $jobModel->min_wage             = $data['minWage'];
        $jobModel->max_wage             = $data['maxWage'];
        $jobModel->experience_type      = $data['experienceType'];
        $jobModel->age_type             = $data['ageType'] ?: '';
        $jobModel->title_type           = $data['titleType'];
        $jobModel->political_type       = $data['politicalType'];
        $jobModel->abroad_type          = $data['abroadType'];
        $jobModel->amount               = $data['amount'];
        $jobModel->department           = $data['department'];
        $jobModel->province_id          = $data['provinceId'];
        $jobModel->city_id              = $data['cityId'];
        $jobModel->district_id          = $data['districtId'];
        $jobModel->address              = $data['address'];
        $jobModel->welfare_tag          = $data['welfareTag'] ?: '';
        $jobModel->period_date          = $data['periodDate'];
        $jobModel->apply_type           = $data['applyType'] ?: '';
        $jobModel->apply_address        = $data['applyAddress'] ?: '';
        $jobModel->is_show              = $data['isShow'] ?: BaseJob::IS_SHOW_YES;
        $jobModel->delivery_limit_type  = $data['deliveryLimitType'] ?: '';
        $jobModel->delivery_type        = $data['deliveryType'];
        $jobModel->extra_notify_address = $data['extraNotifyAddress'];
        $jobModel->delivery_way         = $data['deliveryWay'];
        //判断操作平台为运营平台
        if ($this->operatorType == self::OPERATOR_TYPE_ADMIN) {
            $jobModel->establishment_type = $data['establishmentType'] ?: '';
            $jobModel->is_establishment   = !empty($data['establishmentType']) ? BaseJob::IS_ESTABLISHMENT_YES : BaseJob::IS_ESTABLISHMENT_NO;
        }
        //        $jobModel->file_ids            = $data['fileIds'] ?: '';
        // oldData
        $oldData = $jobModel->getOldAttributes();

        // TODO 修改了【岗位职责、任职要求、其他说明、职位附件】
        $editList = [
            'duty'        => $data['duty'],
            'requirement' => $data['requirement'],
            'remark'      => $data['remark'],
        ];
        $oldList  = [
            'duty'        => $oldData['duty'],
            'requirement' => $oldData['requirement'],
            'remark'      => $oldData['remark'],
        ];
        if ($this->operatorType == self::OPERATOR_TYPE_COMPANY) {
            $fileIdsArr    = empty($data['fileIds']) ? [] : explode(',', $data['fileIds']);
            $oldFileIdsArr = empty($oldData['file_ids']) ? [] : explode(',', $oldData['file_ids']);
            if (count(array_diff($fileIdsArr, $oldFileIdsArr)) > 0) {
                $editList['file_ids'] = is_null($data['fileIds']) ? '' : $data['fileIds'];
                $oldList['file_ids']  = is_null($oldData['file_ids']) ? '' : $oldData['file_ids'];
            } else {
                $jobModel->file_ids = $data['fileIds'] ?: '';
            }
        }
        $modifyAfterList  = array_diff_assoc($editList, $oldList);
        $modifyBeforeList = [];
        foreach ($modifyAfterList as $k => $list) {
            $modifyBeforeList[$k] = $oldList[$k];
        }
        if ($oldData['status'] == BaseJob::STATUS_ONLINE) {
            if (sizeof($modifyAfterList) > 0) {
                if ($this->operatorType == self::OPERATOR_TYPE_COMPANY) {
                    $jobModel->creator = $this->companyMemberModel->username;
                }

                if ($this->operatorType == self::OPERATOR_TYPE_ADMIN) {
                    $jobModel->creator = $this->adminModel->name;
                }
                $announcementId = $oldData['announcement_id'];

                $editContent = json_encode($modifyAfterList);
                $list        = [
                    'job_id'          => $jobId,
                    'add_time'        => CUR_DATETIME,
                    'status'          => BaseJobEdit::STATUS_ONLINE,
                    'edit_content'    => $editContent,
                    'editor_id'       => $this->operatorId,
                    'editor_type'     => BaseJobEdit::EDITOR_TYPE_COMPANY,
                    'editor'          => $jobModel->creator,
                    'announcement_id' => $announcementId,
                ];
                // 检测之前是否有职位编辑内容
                $jobEditInfo = BaseJobEdit::selectInfo(['job_id' => $jobId], ['id']);
                if ($jobEditInfo['id']) {
                    $condition = ['id' => $jobEditInfo['id']];
                    BaseJobEdit::updateAll($list, $condition);
                } else {
                    BaseJobEdit::createInfo($list);
                }
                //这里存职位操作表
                $changeModifyBeforeList = [];
                $changeModifyAfterList  = [];
                foreach ($modifyBeforeList as $k => $item) {
                    switch ($k) {
                        case 'duty':
                            $changeModifyBeforeList['岗位职责'] = $item;
                            break;
                        case 'requirement':
                            $changeModifyBeforeList['任职要求'] = $item;
                            break;
                        case 'remark':
                            $changeModifyBeforeList['其他说明'] = $item;
                            break;
                        case 'file_ids':
                            $changeModifyBeforeList[BaseJobHandleLog::LOG_TEXT_LIST_FILE_IDS] = $item;
                            break;
                    }
                }

                foreach ($modifyAfterList as $k => $item) {
                    switch ($k) {
                        case 'duty':
                            $changeModifyAfterList['岗位职责'] = $item;
                            break;
                        case 'requirement':
                            $changeModifyAfterList['任职要求'] = $item;
                            break;
                        case 'remark':
                            $changeModifyAfterList['其他说明'] = $item;
                            break;
                        case 'file_ids':
                            $changeModifyAfterList[BaseJobHandleLog::LOG_TEXT_LIST_FILE_IDS] = $item;
                            break;
                    }
                }

                $handleBefore = json_encode($changeModifyBeforeList);
                $handleAfter  = json_encode($changeModifyAfterList);
                $jobHandleLog = [
                    'add_time'      => CUR_DATETIME,
                    'job_id'        => $jobId,
                    'handle_type'   => (string)BaseJobHandleLog::HANDLE_TYPE_EDIT,
                    'handler_type'  => BaseJobHandleLog::HANDLER_TYPE_USER,
                    'handler_id'    => $this->operatorId,
                    'handler_name'  => $jobModel->creator,
                    'handle_before' => $handleBefore,
                    'handle_after'  => $handleAfter,
                    'ip'            => IpHelper::getIpInt(),
                ];
                BaseJobHandleLog::createInfo($jobHandleLog);

                //只有修改--岗位职责、任职要求、其他说明，移至待审核
                $jobModel->audit_status     = BaseJob::AUDIT_STATUS_WAIT_AUDIT;
                $jobModel->apply_audit_time = CUR_DATETIME;
                //这里将公告状态调整
                if ($jobModel->announcement_id > 0) {
                    $announcementModel               = BaseAnnouncement::findOne(['id' => $jobModel->announcement_id]);
                    $announcementModel->audit_status = BaseAnnouncement::STATUS_AUDIT_AWAIT;
                    if (!$announcementModel->save()) {
                        throw new Exception($announcementModel->getFirstErrorsMessage());
                    }
                    //创建公告操作日志
                    $this->createAnnouncementHandleLog($jobModel->announcement_id, $handleBefore, $handleAfter);
                }
            }
        } else {
            $jobModel->duty             = $data['duty'];
            $jobModel->requirement      = $data['requirement'];
            $jobModel->remark           = $data['remark'];
            $jobModel->file_ids         = $data['fileIds'] ?: '';
            $jobModel->status           = BaseJob::STATUS_WAIT_AUDIT;
            $jobModel->audit_status     = BaseJob::AUDIT_STATUS_WAIT_AUDIT;
            $jobModel->apply_audit_time = CUR_DATETIME;
        }
        if (!$jobModel->save()) {
            throw new Exception($jobModel->getFirstErrorsMessage());
        }
        //判断合作单位
        if ($this->companyModel->is_cooperation == BaseAnnouncement::IS_COOPERATION_YES) {
            //写入职位联系人与职位协同
            $params_data = [
                'job_id'                  => $jobModel->id,
                'company_id'              => $this->companyId,
                'job_contact_id'          => $data['jobContactId'],
                'announcement_id'         => $data['announcementId'],
                'job_contact_synergy_ids' => $data['jobContactSynergyIds'],
            ];
            $this->contact($params_data);
        }
    }

    /**
     * @throws Exception
     *
     *  这个职位已经有了,再次编辑保存
     */
    private function save()
    {
        $jobModel = $this->jobModel;
        $data     = $this->jobData;

        // 校验一下什么状态下是允许修改的
        /**
         * self::STATUS_OFFLINE
         * self::STATUS_ONLINE
         * self::STATUS_WAIT
         * self::STATUS_WAIT_AUDIT
         * self::STATUS_DELETE
         * self::STATUS_REFUSE_AUDIT
         */
        switch ($jobModel->status) {
            case BaseJob::STATUS_OFFLINE:
                throw new Exception('该职位已经下线,不能修改');
            case BaseJob::STATUS_WAIT_AUDIT:
                throw new Exception('该职位正在审核中,不能修改');
            case BaseJob::STATUS_DELETE:
                throw new Exception('该职位已经删除,不能修改');
            case BaseJob::STATUS_ONLINE:
                // 在线状态,允许修改,但是会去到待审核
                //判断职位投递性质
                if ($jobModel->delivery_type > 0) {
                    if ($data['deliveryType'] == 0 && $jobModel->announcement_id > 0) {//修改后跟公告
                        $announcementInfo = BaseAnnouncement::findOne($jobModel->announcement_id);
                        if ($jobModel->delivery_type != $announcementInfo->delivery_type) {
                            throw new Exception('你编辑使职位投递类型站内外发生变化，导致修改失败');
                        }
                    } elseif ($data['deliveryType'] > 0) {
                        if ($jobModel->delivery_type != $data['deliveryType']) {//跟自己
                            throw new Exception('你编辑使职位投递类型站内外发生变化，导致修改失败');
                        }
                    }
                } else {
                    if ($jobModel->announcement_id > 0) {
                        $announcementInfo = BaseAnnouncement::findOne($jobModel->announcement_id);
                        if ($jobModel->delivery_type == 0 && $data['deliveryType'] > 0) {//跟公告
                            if ($announcementInfo->delivery_type != $data['deliveryType']) {
                                throw new Exception('你编辑使职位投递类型站内外发生变化，导致修改失败');
                            }
                        }
                    }
                }
                break;
            case BaseJob::STATUS_WAIT:
                // 待审核状态,允许修改,但是会去到待审核
                break;
            case BaseJob::STATUS_REFUSE_AUDIT:
                // 审核拒绝状态,允许修改,但是会去到待审核
                break;
            default:
                throw new Exception('该职位状态不正确,不能修改');
        }

        $jobModel->create_id            = $this->operatorId;
        $jobModel->member_id            = $this->companyMemberModel->id;
        $jobModel->name                 = $data['name'];
        $jobModel->code                 = $data['code'];
        $jobModel->job_category_id      = $data['jobCategoryId'];
        $jobModel->education_type       = $data['educationType'];
        $jobModel->major_id             = $data['majorId'] ?: '';
        $jobModel->nature_type          = $data['natureType'];
        $jobModel->wage_type            = $data['wageType'];
        $jobModel->is_negotiable        = $data['isNegotiable'];
        $jobModel->min_wage             = $data['minWage'];
        $jobModel->max_wage             = $data['maxWage'];
        $jobModel->experience_type      = $data['experienceType'];
        $jobModel->age_type             = $data['ageType'] ?: '';
        $jobModel->title_type           = $data['titleType'];
        $jobModel->political_type       = $data['politicalType'];
        $jobModel->abroad_type          = $data['abroadType'];
        $jobModel->amount               = $data['amount'];
        $jobModel->department           = $data['department'];
        $jobModel->province_id          = $data['provinceId'];
        $jobModel->city_id              = $data['cityId'];
        $jobModel->district_id          = $data['districtId'];
        $jobModel->address              = $data['address'];
        $jobModel->welfare_tag          = $data['welfareTag'] ?: '';
        $jobModel->period_date          = $data['periodDate'];
        $jobModel->apply_type           = $data['applyType'] ?: '';
        $jobModel->apply_address        = $data['applyAddress'] ?: '';
        $jobModel->is_show              = $data['isShow'] ?: BaseJob::IS_SHOW_YES;
        $jobModel->duty                 = $data['duty'];
        $jobModel->requirement          = $data['requirement'];
        $jobModel->remark               = $data['remark'];
        $jobModel->status               = BaseJob::STATUS_WAIT;
        $jobModel->audit_status         = BaseJob::AUDIT_STATUS_WAIT;
        $jobModel->apply_audit_time     = CUR_DATETIME;
        $jobModel->file_ids             = $data['fileIds'] ?: '';
        $jobModel->delivery_limit_type  = $data['deliveryLimitType'] ?: '';
        $jobModel->delivery_type        = $data['deliveryType'];
        $jobModel->extra_notify_address = $data['extraNotifyAddress'];
        $jobModel->delivery_way         = $data['deliveryWay'];
        if (!$jobModel->save()) {
            throw new Exception($jobModel->getFirstErrorsMessage());
        }
        //判断合作单位
        if ($this->companyModel->is_cooperation == BaseAnnouncement::IS_COOPERATION_YES) {
            //写入职位联系人与职位协同
            $params_data = [
                'job_id'                  => $jobModel->id,
                'company_id'              => $this->companyId,
                'job_contact_id'          => $data['jobContactId'],
                'announcement_id'         => $data['announcementId'],
                'job_contact_synergy_ids' => $data['jobContactSynergyIds'],
            ];
            $this->contact($params_data);
        }
    }

    /**
     * 创建公告操作日志
     * @param $announcementId
     * @param $handleBefore
     * @param $handleAfter
     * @throws Exception
     * @throws \yii\base\NotSupportedException
     */
    private function createAnnouncementHandleLog($announcementId, $handleBefore, $handleAfter)
    {
        $memberId  = \Yii::$app->user->id;
        $adminInfo = BaseMember::find()
            ->where(['id' => $memberId])
            ->select('username')
            ->asArray()
            ->one();
        if ($this->operatorType == self::OPERATOR_TYPE_COMPANY) {
            $handler_name = BaseCompany::findOneVal(['member_id' => $memberId], 'full_name');
        } else {
            $handler_name = $adminInfo['username'];
        }

        //操作动作入表
        $handleLogArr = [
            'add_time'        => CUR_DATETIME,
            'announcement_id' => $announcementId,
            'handle_type'     => (string)BaseAnnouncementHandleLog::HANDLE_TYPE_EDIT,
            'editor_type'     => BaseAnnouncement::TYPE_EDITOR_JOB,
            'handler_type'    => BaseAnnouncementHandleLog::HANDLER_TYPE_USER,
            'handler_id'      => $memberId,
            'handler_name'    => $handler_name,
            'handle_before'   => $handleBefore,
            'handle_after'    => $handleAfter,
            'ip'              => IpHelper::getIpInt(),
        ];
        BaseAnnouncementHandleLog::createInfo($handleLogArr);
    }

    /**
     * 对职位的联系人与协同人进行处理
     * @param $params
     */
    private function contact($params)
    {
        //写入职位联系人与职位协同
        $contact_insert                           = [
            'job_id'          => $params['job_id'],
            'company_id'      => $params['company_id'],
            'announcement_id' => $params['announcement_id'] ?: 0,
        ];
        $contact_synergy_insert                   = $contact_insert;
        $contact_insert['company_member_info_id'] = $params['job_contact_id'];

        BaseJobContact::add($contact_insert);
        $contact_synergy_insert['company_member_info_id'] = $params['job_contact_synergy_ids'];
        BaseJobContactSynergy::addBatch($contact_synergy_insert);
    }
}
