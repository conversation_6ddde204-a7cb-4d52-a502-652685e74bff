<?php

namespace common\service\job;

use common\base\models\BaseCompanyInterview;
use common\base\models\BaseJobApply;
use common\base\models\BaseJobApplyHandleLog;
use common\base\models\BaseJobApplyRecordExtra;
use common\base\models\BaseJobApplyTopEquityRecord;
use common\base\models\BaseMember;
use common\base\models\BaseMemberMessage;
use common\base\models\BaseMemberSchedule;
use common\base\models\BaseResume;
use common\libs\EmailQueue;
use common\service\messageCenter\MessageCenterApplication;
use Faker\Provider\Base;
use Mpdf\Tag\B;
use queue\Producer;
use Yii;
use yii\base\Exception;
use yii\base\NotSupportedException;

class JobApplyHandleService extends BaseService
{
    public $jobApplyId;
    public $handleType;

    public $companyInfo;
    public $handleTitle;
    public $oldStatus;
    public $newStatus;
    public $oldMarkStatus;
    public $newMarkStatus;
    public $oldIsCheck;
    public $title;
    public $content;

    /**
     * @var BaseJobApply
     */
    public $jobApplyModel;

    public $data;

    public $isFirstCheck = false;

    // 邀请面试的id
    public $companyInterviewid;
    public $isEmailCompanyInterview = false;

    // 全部操作的类型
    const HANDEL_TYPE_THROUGH_FIRST            = BaseJobApplyHandleLog::TYPE_THROUGH_FIRST;//通过初筛
    const HANDEL_TYPE_SEND_INVITATION          = BaseJobApplyHandleLog::TYPE_SEND_INVITATION;//邀请面试
    const HANDEL_TYPE_INAPPROPRIATE            = BaseJobApplyHandleLog::TYPE_INAPPROPRIATE;//不合适
    const HANDEL_TYPE_INAPPROPRIATE_REVOCATION = BaseJobApplyHandleLog::TYPE_INAPPROPRIATE_REVOCATION;//撤销不合适
    const HANDEL_TYPE_ENTRY                    = BaseJobApplyHandleLog::TYPE_ENTRY;//已入职
    const HANDEL_TYPE_ENTRY_NO                 = BaseJobApplyHandleLog::TYPE_ENTRY_NO;//未入职
    const HANDEL_TYPE_ENTRY_REVOCATION         = BaseJobApplyHandleLog::TYPE_ENTRY_REVOCATION;//撤销已入职
    const HANDEL_TYPE_ENTRY_NO_REVOCATION      = BaseJobApplyHandleLog::TYPE_ENTRY_NO_REVOCATION;//撤销未入职
    const HANDEL_TYPE_HANDLE_WAIT              = BaseJobApplyHandleLog::TYPE_HANDLE_WAIT;//待处理
    const HANDEL_TYPE_EMPLOYED_WAIT            = BaseJobApplyHandleLog::TYPE_EMPLOYED_WAIT;//待录用
    const HANDEL_TYPE_EMPLOYED_REVOCATION      = BaseJobApplyHandleLog::TYPE_EMPLOYED_REVOCATION;//撤销录用
    const HANDEL_TYPE_BLOCK_CALL               = BaseJobApplyHandleLog::TYPE_BLOCK_CALL;//未接通
    const HANDEL_TYPE_INTENTION_NO             = BaseJobApplyHandleLog::TYPE_INTENTION_NO;//无意向
    const HANDEL_TYPE_INTERVIEW_NO             = BaseJobApplyHandleLog::TYPE_INTERVIEW_NO;//未到面
    const HANDEL_TYPE_EMPLOYED                 = BaseJobApplyHandleLog::TYPE_EMPLOYED;//已录用
    const HANDEL_TYPE_CHECK_VIEW               = BaseJobApplyHandleLog::TYPE_VIEW;//已查看

    // 操作影响到主状态
    // const HANDEL_TYPE_LIST = [
    //     self::HANDEL_TYPE_THROUGH_FIRST => [
    //         'status' => BaseJobApply::STATUS_THROUGH_FIRST,
    //         'title'  => '通过初筛',
    //     ],
    //     self::HANDEL_TYPE_SEND_INVITATION => [
    //         'status' => BaseJobApply::STATUS_SEND_INVITATION,
    //         'title'  => '邀请面试',
    //     ],
    //     self::HANDEL_TYPE_INAPPROPRIATE => [
    //         'status' => BaseJobApply::STATUS_INAPPROPRIATE,
    //         'title'  => '不合适',
    //     ],
    //     self::HANDEL_TYPE_INAPPROPRIATE_REVOCATION => [
    //         'status' => BaseJobApply::STATUS_THROUGH_FIRST,
    //         'title'  => '撤销不合适',
    //     ],
    //     self::HANDEL_TYPE_ENTRY => [
    //         'status' => BaseJobApply::STATUS_EMPLOYED,
    //         'title'  => '已入职',
    //     ],
    //     self::HANDEL_TYPE_ENTRY_NO => [
    //         'status' => BaseJobApply::STATUS_ENTRY_NO,
    //         'title'  => '未入职',
    //     ],
    // ];

    /**
     * 执行操作
     */
    public function run()
    {
        try {
            $this->handle();
        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * 查看
     */
    public function runCheck()
    {
        $this->jobApplyCheck();
    }

    private function jobApplyCheck()
    {
        if ($this->jobApplyModel->is_check != BaseJobApply::IS_CHECK_YES) {
            //这个仅此一次
            $this->jobApplyModel->is_check = BaseJobApply::IS_CHECK_YES;
            if ($this->jobApplyModel->save()) {
                $this->isFirstCheck = true;
                $logData            = [
                    'handle_type' => BaseJobApplyHandleLog::TYPE_VIEW,
                    'title'       => BaseJobApplyHandleLog::STATUS_LIST[BaseJobApplyHandleLog::TYPE_VIEW],
                    'content'     => BaseJobApplyHandleLog::STATUS_LIST[BaseJobApplyHandleLog::TYPE_VIEW],
                ];
                $this->createJobApplyHandleLog($logData);
                $this->sendMessage();
            }
        }

        return true;
    }

    /**
     * 设置好要处理的数据
     * @param $params
     * @return $this
     * @throws Exception
     */
    public function setData($params): JobApplyHandleService
    {
        $this->data          = $params;
        $this->jobApplyId    = $params['id'];
        $jobApplyModel       = BaseJobApply::findOne(['id' => $params['id']]);
        $this->jobApplyModel = $jobApplyModel;
        $this->companyId     = $jobApplyModel->company_id;

        if (!$jobApplyModel) {
            throw new Exception('非法请求');
        }

        $handleTypeList   = BaseJobApplyHandleLog::STATUS_LIST;
        $this->handleType = $params['handle_type'];
        $this->title      = $handleTypeList[$params['handle_type']];
        $this->content    = $handleTypeList[$params['handle_type']];
        $this->oldIsCheck = $jobApplyModel->is_check;
        $this->oldStatus  = $jobApplyModel->status;

        //这里做个校验操作权限
        if ($params['status']) {
            $this->newStatus = $params['status'];
        }
        if (strlen($params['company_mark_status']) > 0) {
            $this->newMarkStatus = $params['company_mark_status'];
        }

        $this->check();

        $this->setCompany();

        return $this;
    }

    /**
     * 操作职位投递
     * @throws Exception
     * @throws \Exception
     */
    private function handle()
    {
        if ($this->oldIsCheck != BaseJobApply::IS_CHECK_YES) {
            //这个仅此一次
            $this->jobApplyModel->is_check = BaseJobApply::IS_CHECK_YES;
            $this->isFirstCheck            = true;
        }

        // 操作分比较多,有一些是影响到主状态的,有一些是没有的,要更加前后来判断这次是做了什么?
        // 这里是主状态变化了,需要做一些操作
        switch ($this->newStatus) {
            case BaseJobApply::STATUS_THROUGH_FIRST:
                // 通过初筛
                $this->throughFirst();
                break;
            case BaseJobApply::STATUS_SEND_INVITATION:
                // 发送面试邀请
                $this->invite();
                break;
            case BaseJobApply::STATUS_INAPPROPRIATE:
                // 不合适
                $this->inappropriate();
                break;
            case BaseJobApply::STATUS_EMPLOYED:
                // 录用
                $this->employed();
                break;
        }

        if ($this->newStatus) {
            $this->jobApplyModel->status = $this->newStatus;
        }

        if (strlen($this->newMarkStatus) > 0) {
            $this->jobApplyModel->company_mark_status = $this->newMarkStatus;
        }

        if (!$this->jobApplyModel->save()) {
            throw new Exception($this->jobApplyModel->getFirstErrorsMessage());
        }
        //所有按钮操作后失效一下权益
        BaseJobApplyTopEquityRecord::expireEquity($this->jobApplyModel->id,
            BaseJobApplyTopEquityRecord::EXPIRE_TYPE_COMPANY);
        BaseJobApply::expireEquity($this->jobApplyModel->id);
        // 更新下面试邀请统计数据
        BaseResume::updateInterviewAmount($this->jobApplyModel->resume_id);
        $this->afterRun();
    }

    public function throughFirst()
    {
        // 做一些简单的检查
        if ($this->oldStatus == BaseJobApply::STATUS_THROUGH_FIRST) {
            throw new Exception('已经通过初筛了');
        }
        //
    }

    /**
     * 邀请面试
     * @throws Exception
     */
    public function invite()
    {
        $this->handleType            = self::HANDEL_TYPE_SEND_INVITATION;
        $this->jobApplyModel->status = BaseJobApply::STATUS_SEND_INVITATION;

        // 拿到用户提交过来的信息
        $submitData = $this->data['baseData'];
        // 写入一条面试邀请
        $companyInterviewModel                 = new BaseCompanyInterview();
        $companyInterviewModel->job_apply_id   = $this->jobApplyId;
        $companyInterviewModel->job_name       = $this->jobApplyModel->job_name;
        $companyInterviewModel->contact        = $submitData['contact'];
        $companyInterviewModel->telephone      = $submitData['telephone'];
        $companyInterviewModel->address        = $submitData['address'];
        $companyInterviewModel->interview_time = $submitData['interview_time'];
        $companyInterviewModel->content        = $submitData['content'];
        if (!$companyInterviewModel->save()) {
            throw new Exception($companyInterviewModel->getFirstErrorsMessage());
        }

        $this->companyInterviewid      = $companyInterviewModel->id;
        $this->isEmailCompanyInterview = $submitData['is_email'] == 1 ? true : false;

        $remindTime = date('Y-m-d H:i:s',
            (strtotime($submitData['interview_time']) - 60 * BaseMemberSchedule::REMIND_TYPE_LIST[BaseMemberSchedule::REMIND_TYPE_FIVE]));

        $memberScheduleData = [
            'add_time'       => CUR_DATETIME,
            'update_time'    => CUR_DATETIME,
            'status'         => BaseMemberSchedule::STATUS_ONLINE,
            'title'          => '面试职位：' . $this->jobApplyModel->job_name,
            'content'        => $this->jobApplyModel->job_name,
            'begin_time'     => $submitData['interview_time'],
            'place'          => $submitData['address'],
            'remind_time'    => $remindTime,
            'remind_type'    => BaseMemberSchedule::REMIND_TYPE_FIVE,
            'is_need_remind' => BaseMemberSchedule::IS_NEED_REMIND_YES,
            'main_id'        => $this->jobApplyModel->id,
        ];

        // 企业面试日程
        $companyData = array_merge($memberScheduleData, [
            //'member_id' => $this->jobApplyModel->company_member_id,
            'member_id' => $this->operatorId,
            'type'      => BaseMemberSchedule::TYPE_COMPANY_INTERVIEW,
        ]);
        //用户面试日程
        $personData = array_merge($memberScheduleData, [
            'member_id' => $this->jobApplyModel->resume_member_id,
            'type'      => BaseMemberSchedule::TYPE_PERSON_INTERVIEW,
        ]);

        // 分别写入日程
        BaseMemberSchedule::createNormal($companyData, BaseMemberSchedule::TYPE_COMPANY_INTERVIEW);
        BaseMemberSchedule::createNormal($personData, BaseMemberSchedule::TYPE_PERSON_INTERVIEW);

        // 这里应该是日志去做的事情,不应该这里来做
        // $data = [
        //     'id'             => $this->jobApplyModel->job_id,
        //     'additionalInfo' => [
        //         '面试职位：' . $this->jobApplyModel->job_name,
        //         '面试时间：' . $submitData['interview_time'],
        //         '面试地址：' . $submitData['address'],
        //         '联系人：' . $submitData['contact'] . ' ' . $submitData['telephone'],
        //     ],
        // ];
        //
        // if (!empty($interviewInfo['content'])) {
        //     $data['additionalInfo'][] = '备注：' . $interviewInfo['content'];
        // }
        $this->jobApplyModel->is_invitation = BaseJobApply::IS_INVITATION_YES;
        // //发送站内信--面试邀请
        // $title   = '【面试邀请】招聘单位向您发出面试邀请';
        // $content = "您投递的“" . $this->operatorUserName . "-" . $this->jobApplyModel->job_name . "”职位，招聘单位已向您发出面试邀请，请查看具体信息。";
        // BaseMemberMessage::send($this->jobApplyModel->resume_member_id, BaseMemberMessage::TYPE_RESUME_APPLY, $title,
        //     $content, BaseMemberMessage::LINK_TYPE_JOB_DETAIL, $data);
        //更新邀面次数
        BaseJobApplyRecordExtra::jobApplyUpdateInterview($this->jobApplyModel->job_id);
    }

    /**
     * 面试通过
     * @throws Exception
     */
    public function inappropriate()
    {
    }

    public function employed()
    {
    }

    /**
     * 在执行后
     * @throws NotSupportedException
     * @throws Exception
     */
    public function afterRun()
    {
        // 这里有一种特殊情况,就是第一次查看简历要写一个查看日志
        if ($this->isFirstCheck) {
            $logData = [
                'handle_type' => BaseJobApplyHandleLog::TYPE_VIEW,
                'title'       => BaseJobApplyHandleLog::STATUS_LIST[BaseJobApplyHandleLog::TYPE_VIEW],
                'content'     => BaseJobApplyHandleLog::STATUS_LIST[BaseJobApplyHandleLog::TYPE_VIEW],
            ];
            $this->createJobApplyHandleLog($logData);
        }

        $logData = [
            'handle_type' => $this->handleType,
            'title'       => $this->title,
            'content'     => $this->content,
        ];

        $this->createJobApplyHandleLog($logData);
        $this->sendMessage();
    }

    /**
     * 发消息
     * 短信
     * 邮箱(暂时只对接了这里)
     * 内部消息
     * @throws \Exception
     */
    public function sendMessage()
    {
        // 首次查看
        if ($this->isFirstCheck) {
            (new MessageCenterApplication())->jobApplyCheck($this->jobApplyModel->id);
        }

        switch ($this->newStatus) {
            case BaseJobApply::STATUS_HANDLE_WAIT:
                if ($this->handleType == self::HANDEL_TYPE_INAPPROPRIATE_REVOCATION) {
                    // 撤销不合适
                    (new MessageCenterApplication())->jobApplyInappropriateRevocation($this->jobApplyModel->id);
                }
                if ($this->handleType == self::HANDEL_TYPE_EMPLOYED_REVOCATION) {
                    // 撤销录用
                    (new MessageCenterApplication())->jobApplyEmployedRevocation($this->jobApplyModel->id);
                }
            case BaseJobApply::STATUS_THROUGH_FIRST:
                (new MessageCenterApplication())->jobApplyApprove($this->jobApplyModel->id);
                break;
            case BaseJobApply::STATUS_INAPPROPRIATE:
                (new MessageCenterApplication())->jobApplyInappropriate($this->jobApplyModel->id);
                break;
            case BaseJobApply::STATUS_SEND_INVITATION:
                (new MessageCenterApplication())->jobApplyInvitation($this->companyInterviewid,
                    $this->isEmailCompanyInterview);
            case BaseJobApply::STATUS_EMPLOYED:
                (new MessageCenterApplication())->jobApplyEmployed($this->jobApplyModel->id);
                break;
        }
    }

    /**
     * 职位投递流程日志写表
     * @throws NotSupportedException
     * @throws Exception
     */
    public function createJobApplyHandleLog($logData)
    {
        $self               = new BaseJobApplyHandleLog();
        $self->job_apply_id = $this->jobApplyId;
        $self->handler_type = $this->operatorType;
        $self->handler_name = $this->operatorUserName;
        $self->handle_type  = $logData['handle_type'];
        $self->title        = $logData['title'];
        $self->content      = $logData['content'];
        $self->handle_id    = Yii::$app->user->id ?: ($this->operatorType == self::OPERATOR_TYPE_COMPANY ? $this->companyMemberModel->id : 0);
        if (!$self->save()) {
            throw new Exception($self->getFirstErrorsMessage());
        }
    }

    public function check(): bool
    {
        BaseMember::checkMemberIsCanceled(0, $this->jobApplyModel->resume_id);

        if ($this->operatorType == BaseService::OPERATOR_HANDLE_TYPE_COMPANY) {
            //单位账号影响
            //            if ($this->jobApplyModel->company_member_id != $this->operatorId) {
            //                // 操作了不属于自己的
            //                throw new Exception('非法请求');
            //            }
            if ($this->jobApplyModel->company_id != $this->companyId) {
                // 操作了不属于自己的
                throw new Exception('非法请求');
            }
        }

        // https://weixin.processon.com/mindmap/61e4e790e4b08e0e93c154b9,这里理论上要对操作做一系列的前置检查
        $oldStatus     = $this->oldStatus;
        $newStatus     = $this->newStatus;
        $newMarkStatus = $this->newMarkStatus;

        // 1. 检查状态是否合法
        if ($newStatus && !BaseJobApply::STATUS_LIST[$newStatus]) {
            throw new Exception('非法状态');
        }

        // 2. 检查标记状态是否合法
        if ($newMarkStatus && !BaseJobApply::MARK_STATUS_LIST[$newMarkStatus]) {
            throw new Exception('非法标记状态');
        }

        switch ($oldStatus) {
            case BaseJobApply::STATUS_HANDLE_WAIT:
                // 未处理的简历可以做几个操作(通过初筛、不合适、面试邀请、已录用、查看)
                if (!in_array($this->handleType, [
                    self::HANDEL_TYPE_THROUGH_FIRST,
                    self::HANDEL_TYPE_INAPPROPRIATE,
                    self::HANDEL_TYPE_SEND_INVITATION,
                    self::HANDEL_TYPE_EMPLOYED,
                    self::HANDEL_TYPE_CHECK_VIEW,
                ])) {
                    throw new Exception('非法操作');
                }
                break;
            case BaseJobApply::STATUS_THROUGH_FIRST:
                // 有三种操作是可以的(不合适、面试邀请、已录用)
                if (in_array($this->handleType, [
                    self::HANDEL_TYPE_INAPPROPRIATE,
                    self::HANDEL_TYPE_SEND_INVITATION,
                    self::HANDEL_TYPE_EMPLOYED,
                ])) {
                    break;
                }

                // 如果不是上述三种操作,那么就得看看companyMarkStatus
                if ($this->jobApplyModel->company_mark_status == BaseJobApply::COMPANY_MARK_STATUS_NOT_CONNECT) {
                    if ($newMarkStatus != BaseJobApply::COMPANY_MARK_STATUS_NOT_INTENTION) {
                        throw new Exception('非法操作');
                    }
                } elseif ($this->jobApplyModel->company_mark_status == BaseJobApply::COMPANY_MARK_STATUS_NOT_INTENTION) {
                    // 无意向就只能操作上诉的
                    throw new Exception('非法操作');
                } else {
                    // 如果上面两种标记都不属于,这个时候还可以操作未接通和无意向
                    if (!in_array($this->handleType, [
                        self::HANDEL_TYPE_BLOCK_CALL,
                        self::HANDEL_TYPE_INTENTION_NO,
                    ])) {
                        throw new Exception('非法操作');
                    }
                }

                break;
            case BaseJobApply::STATUS_SEND_INVITATION:
                // 先看companyMarkStatus
                if ($this->jobApplyModel->company_mark_status == BaseJobApply::COMPANY_MARK_STATUS_NOT_FACE) {
                    // 如果是未面试,那么只能操作不合适和已录用和再次邀面
                    if (!in_array($this->handleType, [
                        self::HANDEL_TYPE_INAPPROPRIATE,
                        self::HANDEL_TYPE_EMPLOYED,
                        self::HANDEL_TYPE_SEND_INVITATION,
                    ])) {
                        throw new Exception('非法操作');
                    }
                } elseif ($this->jobApplyModel->company_mark_status == BaseJobApply::COMPANY_MARK_STATUS_WAIT_EMPLOYMENT) {
                    // 如果是待录用,那么只能操作已录用 已入职 未入职
                    if (!in_array($this->handleType, [
                        self::HANDEL_TYPE_EMPLOYED,
                        self::HANDEL_TYPE_ENTRY,
                        self::HANDEL_TYPE_ENTRY_NO,
                    ])) {
                        throw new Exception('非法操作');
                    }
                } elseif ($this->jobApplyModel->company_mark_status == BaseJobApply::COMPANY_MARK_STATUS_ENTRY) {
                    // 如果是已入职,那么只能操作撤销已入职
                    if ($this->handleType != self::HANDEL_TYPE_ENTRY_NO_REVOCATION) {
                        throw new Exception('非法操作');
                    }
                } elseif ($this->jobApplyModel->company_mark_status == BaseJobApply::COMPANY_MARK_STATUS_NOT_ENTRY) {
                    // 如果是未入职,那么只能操作撤销未入职
                    if ($this->handleType != self::HANDEL_TYPE_ENTRY_NO_REVOCATION) {
                        throw new Exception('非法操作');
                    }
                } else {
                    // 如果是其他状态,那么可以操作邀请 不合适 已录用 未到面 待录用
                    if (!in_array($this->handleType, [
                        self::HANDEL_TYPE_SEND_INVITATION,
                        self::HANDEL_TYPE_INAPPROPRIATE,
                        self::HANDEL_TYPE_EMPLOYED,
                        self::HANDEL_TYPE_INTERVIEW_NO,
                        self::HANDEL_TYPE_EMPLOYED_WAIT,
                    ])) {
                        throw new Exception('非法操作');
                    }
                }

                break;
            case BaseJobApply::STATUS_INAPPROPRIATE:
                // 不合适的简历只能操作撤销不合适
                if ($this->handleType != self::HANDEL_TYPE_INAPPROPRIATE_REVOCATION) {
                    throw new Exception('非法操作');
                }
                break;
            case BaseJobApply::STATUS_EMPLOYED:
                if ($this->jobApplyModel->company_mark_status == BaseJobApply::COMPANY_MARK_STATUS_ENTRY) {
                    // 如果是已入职,那么只能操作撤销已入职
                    if ($this->handleType != self::HANDEL_TYPE_ENTRY_REVOCATION) {
                        throw new Exception('非法操作');
                    }
                } elseif ($this->jobApplyModel->company_mark_status == BaseJobApply::COMPANY_MARK_STATUS_NOT_ENTRY) {
                    // 如果是未入职,那么只能操作撤销未入职
                    if ($this->handleType != self::HANDEL_TYPE_ENTRY_NO_REVOCATION) {
                        throw new Exception('非法操作');
                    }
                } else {
                    // 如果是其他状态,那么可以操作撤销录用 已入职 未入职
                    if (!in_array($this->handleType, [
                        self::HANDEL_TYPE_EMPLOYED_REVOCATION,
                        self::HANDEL_TYPE_ENTRY,
                        self::HANDEL_TYPE_ENTRY_NO,
                    ])) {
                        throw new Exception('非法操作');
                    }
                }
                break;
            default:
                throw new Exception('非法操作');
        }

        return true;
    }

}
