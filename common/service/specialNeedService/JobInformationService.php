<?php

namespace common\service\specialNeedService;

use common\base\models\BaseSpecialNeedConfig;

/**
 * 职位信息特殊需求处理服务
 * 重构后支持数据库配置，向后兼容原有硬编码逻辑
 */
class JobInformationService extends BaseService
{

    // 职位详情
    public function handelJobDetail($data)
    {
        // 优先使用新的配置系统
        $data = $this->applyConfigurableRules($data);

        // 向后兼容：继续执行原有逻辑（但新配置系统优先级更高）
        if (!$this->isOpen) {
            return $data;
        }
        // http://zentao.jugaocai.com/index.php?m=story&f=view&id=816
        switch (PLATFORM) {
            case 'H5':
                $announcementId = $data['announcementId'];
                $jobId          = $data['jobId'];
                break;
            case 'PC':
                $announcementId = $data['announcementId'];
                $jobId          = $data['id'];
                break;
            case 'MINI':
                $announcementId = $data['announcement_id'];
                $jobId          = $data['job_id'];
                break;
            default:
                break;
        }

        if ($announcementId == 212812) {
            // 正式进入逻辑，拿专业信息
            switch (PLATFORM) {
                case 'H5':
                    break;
                case 'PC':
                    // 空格前端才会显示
                    $data['education'] = ' ';
                    $data['major']     = [
                        [
                            'majorText' => '与拟聘二级学院专业设置或发展方向相关的专业',
                            'url'       => '#',
                        ],
                    ];
                    break;
                case 'MINI':
                default:
                    break;
            }
        }

        // https://zentao.jugaocai.com/index.php?m=story&f=storyView&storyID=1023
        if ($announcementId == 282268) {
            // 正式进入逻辑，拿专业信息
            switch (PLATFORM) {
                case 'H5':
                    break;
                case 'PC':
                    // 空格前端才会显示
                    if ($data['education'] === '硕士研究生') {
                        $data['education'] = '硕士研究生，高级职称可放宽至本科';
                    }
                    break;
                case 'MINI':
                default:
                    break;
            }
        }

        if ($announcementId == 212795) {
            // 正式进入逻辑，拿专业信息
            switch (PLATFORM) {
                case 'H5':
                    break;
                case 'PC':
                    // 空格前端才会显示
                    $data['education'] = '博士研究生或正高职称';
                    break;
                case 'MINI':
                default:
                    break;
            }
        }

        if ($announcementId == 225958) {
            // 正式进入逻辑，拿专业信息
            switch (PLATFORM) {
                case 'H5':
                    break;
                case 'PC':
                    $majorConfig       = [
                        1129053 => '材料加工工程，材料科学与工程，机械电子工程，机械制造及其自动化，电气工程，机器人工程，智能制造技术，控制理论与控制工程等相关专业',
                        1129054 => '机械制造及其自动化，机械电子工程，机械工程，模式识别与智能系统，电气工程，控制理论与控制工程等相关专业',
                        1129055 => '电子科学与技术，信息与通信工程，电子信息，新一代电子信息技术，集成电路工程，应用电子技术，物理电子学，检测技术与自动化装置等相关专业',
                        1129056 => '载运工具运用工程，车辆工程，智能车辆工程，新能源汽车工程，汽车维修工程教育等相关专业',
                        1129057 => '计算机科学与技术，软件工程，网络空间安全，电子信息，大数据技术与工程，智能科学与技术，教育技术学，设计学等相关专业',
                        1129058 => '制冷与低温工程，供热、供燃气、通风及空调工程，控制理论与工程，检测技术与自动化装置，电气工程及其自动化，清洁能源技术，化学工程与技术，室内设计，土木工程等相关专业',
                        1129059 => '电子商务，跨境电子商务，物流工程，大数据技术与工程，新闻与传播，数字媒体技术，包装设计，摄影摄像等相关专业',
                        1129060 => '设计艺术学，设计学，美术，艺术设计硕士（专业硕士），工业设计工程硕士（专业硕士），建筑学，木材科学与技术，广播电视艺术学等相关专业',
                        1129061 => '汉语言文学（专业硕士或博士），财务管理，人力资源管理，旅游管理，工商管理(专业硕士)，电影学，会计等相关专业',
                        1129062 => '材料学，冶金物理与化学，机械制造及其自动化，电机与电器，电力系统及其自动化等相关专业',
                        1129063 => '应用数学，基础数学，体育教育训练学，体育教学硕士（专业硕士），运动训练硕士（专业硕士），中国语言文学，英语语言文学，外国语言学等相关专业',
                    ];
                    $data['education'] = '硕士研究生，具有高级职称的可放宽至本科学历、学士及以上学位';
                    $data['major']     = [
                        [
                            'majorText' => $majorConfig[$jobId],
                            'url'       => '#',
                        ],
                    ];
                    break;
                case 'MINI':
                default:
                    break;
            }
        }

        if ($announcementId == 306054) {
            $majorConfig      = [
                1558458 => "医药类及相关专业",
                1558459 => "医药类及相关专业",
                1558460 => "医药类及相关专业",
                1558461 => "医药类及相关专业",
                1558462 => "医药类及相关专业",
                1558463 => "医药类及相关专业",
                1558464 => "医药类及相关专业",
                1558465 => "医药类及相关专业",
                1558466 => "医药类及相关专业",
                1558467 => "医药类及相关专业",
                1558468 => "医药类及相关专业",
                1558469 => "医药类、管理类",
                1558470 => "人文社会科学（包括哲学、经济学、法学、教育学、文学、历史学、管理学、艺术学等学科门类和心理学、社会学等专业）",
                1558471 => "医学大类（基础医学类、临床医学类、口腔医学类、公共卫生与预防医学类专业优先）",
                1558472 => "英语类（含医学英语）",
                1558473 => "体育类",
                1558474 => "医学大类（护理类专业优先）",
                1558475 => "医学大类（中医学类、医学技术类、中西医结合类、临床医学类专业优先）",
                1558476 => "医学大类（医学技术类、临床医学类、口腔医学类专业优先）",
                1558477 => "医学大类（药学类、中药学类专业优先）",
                1558478 => "医学大类、管理大类（健康服务与管理、养老服务管理等专业优先）",
                1558479 => "计算机类专业、电子信息类专业",
                1558480 => "教育学、心理学、思想政治教育、管理学、医学及相关专业",
            ];
            $data['major']    = [
                [
                    'majorText' => $majorConfig[$jobId],
                    'url'       => '#',
                ],
            ];
            $data['majorTxt'] = $majorConfig[$jobId];
        }

        if ($announcementId == 313171) {
            $majorConfig      = [
                1613689 => "文史类、管理类相关专业优先",
                1613690 => "文史类、管理类相关专业优先",
            ];
            $data['major']    = [
                [
                    'majorText' => $majorConfig[$jobId],
                    'url'       => '#',
                ],
            ];
            $data['majorTxt'] = $majorConfig[$jobId];
        }

        //https://zentao.jugaocai.com/index.php?m=story&f=view&id=1182
        if ($announcementId == 289323) {
            $majorConfig       = [
                1623838 => "人力资源管理、心理学、高等教育学、管理学、法学等相关专业",
                1623837 => "人力资源管理、心理学、高等教育学、管理学、法学等相关专业",
            ];
            $jobCategoryConfig = [
                1623838 => "行政管理",
                1623837 => "行政管理",
            ];

            if (isset($jobCategoryConfig[$jobId])) {
                $data['jobTypeUrl']  = '#';
                $data['jobCategory'] = $jobCategoryConfig[$jobId];
            }

            if (isset($majorConfig[$jobId])) {
                $data['major']    = [
                    [
                        'majorText' => $majorConfig[$jobId],
                        'url'       => '#',
                    ],
                ];
                $data['majorTxt'] = $majorConfig[$jobId];
            }
        }

        if ($announcementId == $this->config['944']['announcementId']) {
            switch (PLATFORM) {
                case 'H5':
                    $data['amount']   = '1~2';
                    $data['majorTxt'] .= ",{$this->config['944']['jobAddMajor']}";
                    $data['major'][]  = [
                        'majorText' => $this->config['944']['jobAddMajor'],
                        'url'       => '#',
                    ];
                    break;
                case 'PC':
                    $data['amount']   = '1~2';
                    $data['majorTxt'] .= ",{$this->config['944']['jobAddMajor']}";
                    // 先给最后一个加上,
                    $data['major'][count($data['major']) - 1]['majorText'] .= ',';
                    $data['major'][]                                       = [
                        'majorText' => $this->config['944']['jobAddMajor'],
                        'url'       => '#',
                    ];
                    break;
                case 'MINI':
                    // 若干改为1～2
                    $data['job_basics_info'] = str_replace('若干', '1～2', $data['job_basics_info']);
                    $data['major_txt']       .= ",{$this->config['944']['jobAddMajor']}";
                    break;
                default:
                    break;
            }
        }

        // https://zentao.jugaocai.com/index.php?m=story&f=storyView&storyID=1004
        if ($announcementId == 271935) {
            if ($data['applyTypeText'] === '网上系统') {
                $data['applyTypeText'] = '网上报名';
            }

            if ($data['apply_type_text'] === '网上系统') {
                $data['apply_type_text'] = '网上报名';
            }
        }

        // https://zentao.jugaocai.com/index.php?m=story&f=view&storyID=1042&version=0&param=0&storyType=story
        //   需求专业字段信息，统一展示为“详见招聘简章”
        //   报名方式字段信息，统一展示为“详见正文”
        //   学历字段信息，统一展示为“详见招聘简章”
        if ($announcementId == 289744) {
            switch (PLATFORM) {
                case 'H5':
                    $data['major']     = [
                        [
                            'majorText' => '详见招聘简章',
                            'url'       => '#',
                        ],
                    ];
                    $data['applyType'] = '详见正文';
                    // $data['education']     = '详见招聘简章';
                    $data['majorTxt']      = '详见招聘简章';
                    $data['applyTypeText'] = '详见正文';
                    // bb($data);
                    break;
                case 'PC':
                    $data['major']     = [
                        [
                            'majorText' => '详见招聘简章',
                            'url'       => '#',
                        ],
                    ];
                    $data['applyType'] = '详见正文';
                    // $data['education']     = '详见招聘简章';
                    $data['majorTxt']      = '详见招聘简章';
                    $data['applyTypeText'] = '详见正文';
                    break;
                case 'MINI':
                    $data['major_txt']      = '详见招聘简章';
                    $data['apply_type_txt'] = '详见正文';
                    // "jobBasicsInfo": "招3人 | 本科 | 烟台", 中间改成详见招聘简章
                    // $list                    = explode('|', $data['job_basics_info']);
                    // $data['job_basics_info'] = "{$list[0]} | 详见招聘简章 | {$list[2]}";

                    break;
                default:
                    break;
            }
        }

        // https://zentao.jugaocai.com/index.php?m=story&f=view&storyID=1073

        /**
         * 需求描述
         * 公告ID：301480
         *
         * 公告：事业编制，电子科技大学2025年专职辅导员招聘公告https://www.gaoxiaojob.com/announcement/detail/301480.html
         *
         * PC端+H5端+小程序端的公告页+职位页+单位页，均需按以下需求修改：
         *
         * 1.“招15人”改为“招不超15人”
         *
         * 2.需求学科改为：“马克思主义理论、心理学或学校其他学科专业背景”
         */
        if ($announcementId == 301480) {
            switch (PLATFORM) {
                case 'H5':
                    $data['amount']   = '不超过15';
                    $data['majorTxt'] = '马克思主义理论、心理学或学校其他学科专业背景';
                    $data['major']    = [
                        [
                            'majorText' => '马克思主义理论、心理学或学校其他学科专业背景',
                            'url'       => '#',
                        ],
                    ];
                    break;
                case 'PC':
                    $data['amount'] = '不超过15人';
                    $data['major']  = [
                        [
                            'majorText' => '马克思主义理论、心理学或学校其他学科专业背景',
                            'url'       => '#',
                        ],
                    ];
                    break;
                case 'MINI':
                    // "jobBasicsInfo": "招15人 | 硕士研究生 | 成都", 把第一个改成招不超过15人，首先用 | 分割，然后把第一个改成招不超过15人
                    $list    = explode('|', $data['job_basics_info']);
                    $list[0] = '招不超过15人';

                    $data['job_basics_info'] = implode(' | ', $list);

                    break;
                default:
                    break;
            }
        }

        // https://zentao.jugaocai.com/index.php?m=story&f=view&storyID=1185
        // 公告ID：316039
        //
        // 公告：南京城市职业学院（南京开放大学）2025年公开招聘高层次人才公告（第一批）https://www.gaoxiaojob.com/announcement/detail/316039.html

        /**
         * 2.职位列表、职位详情页、单位主页：各个职位的学科展示见下表
         *
         * 职位名称
         *
         * 学科
         *
         * 职位ID
         *
         * 教学质量管理与研究
         *
         * 教育类，计算机类
         *
         * 31635167
         *
         * 产品艺术设计专业教师
         *
         * 设计学（产品设计方向），艺术设计（产品设计方向）
         *
         * 31635159
         *
         * 健康管理专业教师
         *
         * 公共卫生类，医学类
         *
         * 31635151
         *
         * 实验实训员
         *
         * 计算机类
         *
         * 31635166
         *
         * 数字媒体艺术设计专业教师
         *
         * 设计学（数字媒体艺术方向），艺术设计（数字媒体艺术方向）
         *
         * 31635158
         *
         * 专业带头人
         *
         * 公共管理类，医学类，电子信息类，交通工程类，机械工程类，计算机类，艺术类，工商管理类，经济类，商务贸易类
         *
         * 31635150
         *
         * 体育教师
         *
         * 体育教育训练学，体育教学，运动训练，体育人文社会学
         *
         * 31635165
         *
         * 视觉传达设计专业教师
         *
         * 设计艺术学（平面设计方向）
         *
         * 31635157
         *
         * 思政教师2
         *
         * 马克思主义哲学，马克思主义基本原理，马克思主义理论，马克思主义中国化研究，思想政治教育
         *
         * 31635164
         *
         * 传播与策划专业教师
         *
         * 电影学，广播电视艺术学
         *
         * 31635156
         *
         * 云计算技术应用专业教师
         *
         * 网络空间安全，网络与信息安全，信息安全，大数据技术与工程，人工智能，通信工程（含宽带网络，移动通信等），信息与通信工程，通信与信息系统
         *
         * 31635155
         *
         * 思政教师1
         *
         * 马克思主义哲学，马克思主义基本原理，马克思主义理论，马克思主义中国化研究，思想政治教育
         *
         * 31635163
         *
         * 跨境电子商务专业教师
         *
         * 经济类，管理科学与工程
         *
         * 31635162
         *
         * 软件技术专业教师
         *
         * 计算机（软件）类
         *
         * 31635154
         *
         * 商务数据分析专业教师
         *
         * 统计类
         *
         * 31635161
         *
         * 智能网联汽车技术专业教师
         *
         * 电子信息类，交通工程类，机械工程类
         *
         * 31635153
         *
         * 智慧健康养老服务与管理专业教师
         *
         * 公共卫生类，医学类，计算机类，电子信息类
         *
         * 31635152
         *
         * 环境艺术设计专任教师
         *
         * 设计学(环境设计方向），艺术设计(环境设计方向）
         *
         * 31635160
         */

        if ($announcementId == 316039) {
            $majorConfig      = [
                1635167 => "教育类，计算机类",
                1635159 => "设计学（产品设计方向），艺术设计（产品设计方向）",
                1635151 => "公共卫生类，医学类",
                1635166 => "计算机类",
                1635158 => "设计学（数字媒体艺术方向），艺术设计（数字媒体艺术方向）",
                1635150 => "公共管理类，医学类，电子信息类，交通工程类，机械工程类，计算机类，艺术类，工商管理类，经济类，商务贸易类",
                1635165 => "体育教育训练学，体育教学，运动训练，体育人文社会学",
                1635157 => "设计艺术学（平面设计方向）",
                1635164 => "马克思主义哲学，马克思主义基本原理，马克思主义理论，马克思主义中国化研究，思想政治教育",
                1635156 => "电影学，广播电视艺术学",
                1635155 => "网络空间安全，网络与信息安全，信息安全，大数据技术与工程，人工智能，通信工程（含宽带网络，移动通信等），信息与通信工程，通信与信息系统",
                1635163 => "马克思主义哲学，马克思主义基本原理，马克思主义理论，马克思主义中国化研究，思想政治教育",
                1635162 => "经济类，管理科学与工程",
                1635154 => "计算机（软件）类",
                1635161 => "统计类",
                1635153 => "电子信息类，交通工程类，机械工程类",
                1635152 => "公共卫生类，医学类，计算机类，电子信息类",
                1635160 => "设计学(环境设计方向），艺术设计(环境设计方向）",
            ];
            $data['major']    = [
                [
                    'majorText' => $majorConfig[$jobId],
                    'url'       => '#',
                ],
            ];
            $data['majorTxt'] = $majorConfig[$jobId];
            if (isset($jobCategoryConfig[$jobId])) {
                $data['jobTypeUrl']  = '#';
                $data['jobCategory'] = $jobCategoryConfig[$jobId];
            }
        }

        // https://zentao.jugaocai.com/index.php?m=story&f=view&storyID=1190&version=0&param=0&storyType=story

        /**
         * 职位ID：1645988
         *
         * 职位名称及链接：国际交流处负责人https://www.gaoxiaojob.com/job/detail/1645988.html
         *
         * 修改内容：PC端、H5端、小程序端口职位详情页、职位所属公告的职位列表、单位主页-职位列表 的职位类型、需求专业，修改为指定文字。
         *
         * 职位类型修改为：行政管理
         *
         * 需求专业修改为：国际关系、教育管理、外语类等相关专业
         *
         * 学历要求：硕士及以上学历
         */

        if ($announcementId == 289323) {
            $jobCategoryConfig = [
                1645988 => "行政管理",
            ];
            $majorConfig       = [
                1645988 => "国际关系、教育管理、外语类等相关专业",
            ];
            $educationConfig   = [
                1645988 => "硕士及以上学历",
            ];
            if (isset($jobCategoryConfig[$jobId])) {
                $data['jobTypeUrl']  = '#';
                $data['jobCategory'] = $jobCategoryConfig[$jobId];
            }

            if (isset($majorConfig[$jobId])) {
                $data['major']    = [
                    [
                        'majorText' => $majorConfig[$jobId],
                        'url'       => '#',
                    ],
                ];
                $data['majorTxt'] = $majorConfig[$jobId];
            }

            // 三个端不太一样
            switch (PLATFORM) {
                case 'H5':
                    if (isset($educationConfig[$jobId])) {
                        $data['education'] = $educationConfig[$jobId];
                    }
                    break;
                case 'PC':
                    if (isset($educationConfig[$jobId])) {
                        $data['education'] = $educationConfig[$jobId];
                    }
                    break;
                case 'MINI':
                    if (isset($educationConfig[$jobId])) {
                        $list    = explode('|', $data['job_basics_info']);
                        $list[1] = '硕士及以上学历';

                        $data['job_basics_info'] = implode(' | ', $list);
                    }
                    break;
                default:
                    break;
            }
        }

        // https://zentao.jugaocai.com/index.php?m=story&f=view&id=1203

        /**
         * 需求描述
         * 共计需要修改4个职位
         *
         * 修改内容：PC端、H5端、小程序端口职位详情页、职位所属公告的职位列表、单位主页-职位列表 的职位类型、学历要求、需求专业，修改为指定文字。
         *
         * 请6月18日下午3点前处理完毕，谢谢
         *
         * 职位ID
         *
         * 职位类型
         *
         * 学历要求
         *
         * 需求专业
         *
         * 1682494
         *
         * 教学科研岗
         *
         * 硕士研究生及以上
         *
         * 英语专业毕业，应用语言学、文学、翻译、商务英语等相关专业；第一学历为全日制本科，本硕专业一致或相近
         *
         * 1682495
         *
         * 教学科研岗
         *
         * 硕士研究生及以上
         *
         * 数学专业毕业，统计学等数学相关专业；第一学历为全日制本科，本硕专业一致或相近
         *
         * 1682496
         *
         * 教学科研岗
         *
         * 硕士研究生及以上
         *
         * 体育教育、运动训练等体育学类专业；第一学历为全日制本科，本硕专业一致或相近
         *
         * 1682497
         *
         * 教学科研岗
         *
         * 硕士研究生及以上
         *
         * 计算机科学与技术、软件工程、数据科学与大数据技术、人工智能、数字媒体技术、物联网工程、虚拟现实与增强现实等相关专业
         *
         * 公告id 289323
         *
         * 按以上要求新增处理一个职位
         * 职位ID：1682498
         * 职位类型：教学科研岗
         * 学历要求：硕士研究生及以上
         * 需求专业：档案学、信息资源管理等相关专业
         */
        if ($announcementId == 289323) {
            $jobCategoryConfig = [
                1682494 => "教学科研岗",
                1682495 => "教学科研岗",
                1682496 => "教学科研岗",
                1682497 => "教学科研岗",
                1682498 => "教学科研岗",
            ];
            $educationConfig   = [
                1682494 => "硕士研究生及以上",
                1682495 => "硕士研究生及以上",
                1682496 => "硕士研究生及以上",
                1682497 => "硕士研究生及以上",
                1682498 => "硕士研究生及以上",
            ];
            $majorConfig       = [
                1682494 => "英语专业毕业，应用语言学、文学、翻译、商务英语等相关专业；第一学历为全日制本科，本硕专业一致或相近",
                1682495 => "数学专业毕业，统计学等数学相关专业；第一学历为全日制本科，本硕专业一致或相近",
                1682496 => "体育教育、运动训练等体育学类专业；第一学历为全日制本科，本硕专业一致或相近",
                1682497 => "计算机科学与技术、软件工程、数据科学与大数据技术、人工智能、数字媒体技术、物联网工程、虚拟现实与增强现实等相关专业",
                1682498 => "档案学、信息资源管理等相关专业",
            ];
            if (isset($jobCategoryConfig[$jobId])) {
                $data['jobTypeUrl']  = '#';
                $data['jobCategory'] = $jobCategoryConfig[$jobId];
            }

            if (isset($majorConfig[$jobId])) {
                $data['major']    = [
                    [
                        'majorText' => $majorConfig[$jobId],
                        'url'       => '#',
                    ],
                ];
                $data['majorTxt'] = $majorConfig[$jobId];
            }

            // 三个端不太一样
            switch (PLATFORM) {
                case 'H5':
                    if (isset($educationConfig[$jobId])) {
                        $data['education'] = $educationConfig[$jobId];
                    }
                    break;
                case 'PC':
                    if (isset($educationConfig[$jobId])) {
                        $data['education'] = $educationConfig[$jobId];
                    }
                    break;
                case 'MINI':
                    if (isset($educationConfig[$jobId])) {
                        $list    = explode('|', $data['job_basics_info']);
                        $list[1] = '硕士研究生及以上';

                        $data['job_basics_info'] = implode(' | ', $list);
                    }
                    break;
                default:
                    break;
            }
        }

        // https://zentao.jugaocai.com/index.php?m=story&f=view&storyID=1191
        // 公告ID：325258
        // 公告标题及链接：上海商学院2025年公开招聘商务信息学院院长公告https://www.gaoxiaojob.com/announcement/detail/325258.html
        // 修改内容：PC端、H5端、小程序端口公告详情页、职位详情页、职位所属公告的职位列表、单位主页-职位列表 的需求专业，修改为指定文字。
        // 需求专业：管理科学与工程、计算机科学与技术、电子信息、智能科学与技术
        if ($announcementId == 325258) {
            // 三个端不太一样
            switch (PLATFORM) {
                case 'H5':
                    $data['majorTxt'] = '管理科学与工程、计算机科学与技术、电子信息、智能科学与技术';
                    $data['major']    = [
                        [
                            'majorText' => '管理科学与工程、计算机科学与技术、电子信息、智能科学与技术',
                            'url'       => '#',
                        ],
                    ];
                    break;
                case 'PC':
                    $data['major'] = [
                        [
                            'majorText' => '管理科学与工程、计算机科学与技术、电子信息、智能科学与技术',
                            'url'       => '#',
                        ],
                    ];
                    break;
                case 'MINI':
                    $data['major_txt'] = '管理科学与工程、计算机科学与技术、电子信息、智能科学与技术';
                    break;
                default:
                    break;
            }
        }

        // https://zentao.jugaocai.com/index.php?m=story&f=view&storyID=1212
        // 公告ID：289323，单位ID：80360，长沙科技学院特殊修改需求
        // 修改内容：PC端、H5端、小程序端口职位详情页、职位所属公告的职位列表、单位主页-职位列表 的职位类型、学历要求、职称要求、需求专业
        if ($announcementId == 289323) {
            // 长沙科技学院14个职位的特殊配置
            $changShaJobConfig = [
                1700793 => [
                    'jobCategory' => '教学科研岗',
                    // 'education'   => '', // 学历要求列为空，不修改
                    'title'       => '教授',
                    // 职称要求修改为教授
                    'major'       => '智能装备与系统、轨道交通信号与控制、能源与动力工程等相关专业',
                ],
                1700800 => [
                    'jobCategory' => '教学科研岗',
                    // 'education'   => '', // 学历要求列为空，不修改
                    'title'       => '教授',
                    // 职称要求修改为教授
                    'major'       => '网络与新媒体、新闻学、广播电视学、广告学、编辑出版学等相关专业',
                ],
                1700792 => [
                    'jobCategory' => '教学科研岗',
                    // 'education'   => '', // 学历要求列为空，不修改
                    'title'       => '教授',
                    // 职称要求修改为教授
                    'major'       => '智能交互设计、机械电子工程、机器人工程等相关专业',
                ],
                1700799 => [
                    'jobCategory' => '教学科研岗',
                    // 'education'   => '', // 学历要求列为空，不修改
                    'title'       => '教授',
                    // 职称要求修改为教授
                    'major'       => '跨境电子商务、电子商务及法律、电子商务等相关专业',
                ],
                1700791 => [
                    'jobCategory' => '教学科研岗',
                    // 'education'   => '', // 学历要求列为空，不修改
                    'title'       => '教授',
                    // 职称要求修改为教授
                    'major'       => '智能制造工程、机械设计制造及自动化、车辆工程等相关专业',
                ],
                1700798 => [
                    'jobCategory' => '教学科研岗',
                    // 'education'   => '', // 学历要求列为空，不修改
                    'title'       => '教授',
                    // 职称要求修改为教授
                    'major'       => '供应链管理、物流管理、物流工程、采购管理等相关专业',
                ],
                1700790 => [
                    'jobCategory' => '教学科研岗',
                    'education'   => '博士或教授以上',
                    // 'title'       => '', // 职称要求不修改
                    'major'       => '轨道交通电气与控制、智能交通技术、轮机工程等相关专业',
                ],
                1700797 => [
                    'jobCategory' => '教学科研岗',
                    // 'education'   => '', // 学历要求列为空，不修改
                    'title'       => '教授',
                    // 职称要求修改为教授
                    'major'       => '经济学、国际经济与贸易、财政学、金融学等相关专业',
                ],
                1700789 => [
                    'jobCategory' => '教学科研岗',
                    'education'   => '博士或教授以上',
                    // 'title'       => '', // 职称要求不修改
                    'major'       => '智慧交通、交通工程、交通运输等相关专业',
                ],
                1700796 => [
                    'jobCategory' => '教学科研岗',
                    'education'   => '博士或教授以上',
                    // 'title'       => '', // 职称要求不修改
                    'major'       => '物联网工程、智能科学与技术、人工智能等相关专业',
                ],
                1700788 => [
                    'jobCategory' => '教学科研岗',
                    'education'   => '教授、副教授、博士及以上',
                    // 'title'       => '', // 职称要求不修改
                    'major'       => '电气工程及其自动化、智能电网信息工程、电气工程与智能控制等相关专业',
                ],
                1700787 => [
                    'jobCategory' => '教学科研岗',
                    'education'   => '教授、副教授、博士及以上',
                    // 'title'       => '', // 职称要求不修改
                    'major'       => '储能科学与工程、能源与动力工程、新能源科学与工程等相关专业',
                ],
                1700795 => [
                    'jobCategory' => '教学科研岗',
                    'education'   => '博士或教授以上',
                    // 'title'       => '', // 职称要求不修改
                    'major'       => '数字媒体技术、计算机科学与技术、软件工程、智能科学与技术、电子信息工程等相关专业',
                ],
                1700794 => [
                    'jobCategory' => '教学科研岗',
                    'education'   => '博士或教授以上',
                    // 'title'       => '', // 职称要求不修改
                    'major'       => '人工智能、电子信息工程、通信工程、自动化、机器人工程、计算机科学与技术等相关专业',
                ],
            ];

            if (isset($changShaJobConfig[$jobId])) {
                $config = $changShaJobConfig[$jobId];

                // 三个端不太一样
                switch (PLATFORM) {
                    case 'H5':
                        if (isset($config['education'])) {
                            $data['education'] = $config['education'];
                        }
                        if (isset($config['title'])) {
                            $data['title'] = $config['title'];
                        }
                        if (isset($config['major'])) {
                            $data['majorTxt'] = $config['major'];
                            $data['major']    = [
                                [
                                    'majorText' => $config['major'],
                                    'url'       => '#',
                                ],
                            ];
                        }
                        if (isset($config['jobCategory'])) {
                            $data['jobCategory'] = $config['jobCategory'];
                            $data['jobTypeUrl']  = '#';
                        }
                        break;
                    case 'PC':
                        if (isset($config['education'])) {
                            $data['education'] = $config['education'];
                        }
                        if (isset($config['title'])) {
                            $data['title'] = $config['title'];
                        }
                        if (isset($config['major'])) {
                            $data['major'] = [
                                [
                                    'majorText' => $config['major'],
                                    'url'       => '#',
                                ],
                            ];
                        }
                        if (isset($config['jobCategory'])) {
                            $data['jobCategory'] = $config['jobCategory'];
                            $data['jobTypeUrl']  = '#';
                        }
                        break;
                    case 'MINI':
                        if (isset($config['education'])) {
                            // 小程序需要修改job_basics_info字段
                            if (isset($data['job_basics_info'])) {
                                $list = explode('|', $data['job_basics_info']);
                                if (count($list) >= 2) {
                                    $list[1]                 = ' ' . $config['education'] . ' ';
                                    $data['job_basics_info'] = implode('|', $list);
                                }
                            }
                        }
                        if (isset($config['title'])) {
                            $data['title'] = $config['title'];
                        }
                        if (isset($config['major'])) {
                            $data['major_txt'] = $config['major'];
                        }
                        if (isset($config['jobCategory'])) {
                            $data['job_category'] = $config['jobCategory'];
                        }
                        break;
                    default:
                        break;
                }
            }
        }

        return $data;
    }

    /**
     * 应用可配置的规则
     * @param array $data
     * @return array
     */
    protected function applyConfigurableRules($data)
    {
        // 检查新系统是否启用
        if (!BaseSpecialNeedConfig::isEnabled()) {
            return $data;
        }

        // 获取当前平台
        $platform = defined('PLATFORM') ? PLATFORM : 'PC';

        // 提取关键ID
        $jobId          = $this->extractJobId($data);
        $announcementId = $this->extractAnnouncementId($data);

        if (!$jobId && !$announcementId) {
            return $data;
        }

        // 获取单位ID（如果有的话）
        $companyId = $data['companyId'] ?? $data['company_id'] ?? 0;

        // 职位详情页面可以应用职称字段，其他页面不应用
        $applyTitleField = true;

        // 1. 先获取单位级别的配置（最低优先级）
        if ($companyId) {
            $companyConfigs = BaseSpecialNeedConfig::getConfigs(BaseSpecialNeedConfig::TYPE_COMPANY, $companyId,
                $platform);
            if (!empty($companyConfigs)) {
                $data = BaseSpecialNeedConfig::applyConfigs($data, $companyConfigs,
                    BaseSpecialNeedConfig::TYPE_COMPANY, $applyTitleField);
            }
        }

        // 2. 再获取公告级别的配置（中等优先级）
        if ($announcementId) {
            $announcementConfigs = BaseSpecialNeedConfig::getConfigs(BaseSpecialNeedConfig::TYPE_ANNOUNCEMENT,
                $announcementId, $platform);
            if (!empty($announcementConfigs)) {
                $data = BaseSpecialNeedConfig::applyConfigs($data, $announcementConfigs,
                    BaseSpecialNeedConfig::TYPE_ANNOUNCEMENT, $applyTitleField);
            }
        }

        // 3. 最后获取职位级别的配置（最高优先级，会覆盖单位和公告配置）
        if ($jobId) {
            $jobConfigs = BaseSpecialNeedConfig::getConfigs(BaseSpecialNeedConfig::TYPE_JOB, $jobId, $platform);
            if (!empty($jobConfigs)) {
                $data = BaseSpecialNeedConfig::applyConfigs($data, $jobConfigs, BaseSpecialNeedConfig::TYPE_JOB, $applyTitleField);
            }
        }

        return $data;
    }

    /**
     * 提取职位ID
     * @param array $data
     * @return int
     */
    protected function extractJobId($data)
    {
        $platform = defined('PLATFORM') ? PLATFORM : 'PC';

        switch ($platform) {
            case 'H5':
                return $data['jobId'] ?? 0;
            case 'PC':
                return $data['id'] ?? 0;
            case 'MINI':
                return $data['job_id'] ?? 0;
            default:
                return $data['id'] ?? $data['jobId'] ?? $data['job_id'] ?? 0;
        }
    }

    /**
     * 提取公告ID
     * @param array $data
     * @return int
     */
    protected function extractAnnouncementId($data)
    {
        $platform = defined('PLATFORM') ? PLATFORM : 'PC';

        switch ($platform) {
            case 'H5':
                return $data['announcementId'] ?? 0;
            case 'PC':
                return $data['announcementId'] ?? 0;
            case 'MINI':
                return $data['announcement_id'] ?? 0;
            default:
                return $data['announcementId'] ?? $data['announcement_id'] ?? 0;
        }
    }

}
