<?php

namespace common\service\adminSearch;

use admin\models\Area;
use admin\models\Major;
use admin\models\Resume;
use admin\models\ResumeStatData;
use common\base\BaseActiveRecord;
use common\base\models\BaseChatRoom;
use common\base\models\BaseDictionary;
use common\base\models\BaseJobApplyRecord;
use common\base\models\BaseJobSubscribe;
use common\base\models\BaseMember;
use common\base\models\BaseResume;
use common\base\models\BaseResumeAcademicBook;
use common\base\models\BaseResumeAcademicPage;
use common\base\models\BaseResumeAcademicPatent;
use common\base\models\BaseResumeAcademicReward;
use common\base\models\BaseResumeCancelLog;
use common\base\models\BaseResumeEducation;
use common\base\models\BaseResumeIntention;
use common\base\models\BaseResumeIntentionAreaRelation;
use common\base\models\BaseResumeOtherReward;
use common\base\models\BaseResumeResearchDirection;
use common\base\models\BaseResumeResearchProject;
use common\base\models\BaseResumeSetting;
use common\base\models\BaseResumeTag;
use common\base\models\BaseResumeTagRelation;
use common\base\models\BaseResumeWork;
use common\base\models\BaseResumeWxBind;
use common\helpers\ArrayHelper;
use common\helpers\StringHelper;
use common\helpers\TimeHelper;
use common\models\ResumeEducation;
use frontendPc\models\Dictionary;
use yii\db\Expression;

class ResumeListService extends BaseResumeService
{
    private static $instance;

    private $adminId;
    private $params         = [];
    private $originalParams = [];

    private $page     = 1;
    private $pageSize = 20;
    private $limit    = 20;
    private $offset   = 0;
    private $order    = 'a.add_time desc';

    private $result = [
        'list'  => [],
        'total' => 0,

    ];

    // 基本的搜索参数
    const SEARCH_PARAMS = [
        // 关键字 搜索 人才姓名/ID/用户名
        'userKeyword',
        // 手机号,看情况拼接区号 mobileCode
        'mobileCode',
        'mobile',
        // 最高学历
        'educationId',
        // 身份
        'identityType',
        // 毕业开始时间
        'graduateBeginDate',
        // 毕业结束时间
        'graduateEndDate',
        // 工作年限
        'workYears',
        // 求职状态
        'workStatus',
        // 户籍国籍
        'householdRegisterId',
        // 现居住地
        'areaId',
        // 注册时间开始
        'startCreateTime',
        // 注册时间结束
        'endCreateTime',
        // 最近登录时间
        'startLastLoginTime',
        // 最近登录时间
        'endLastLoginTime',
        // 求职意向
        'jobCategory',
        // 意向城市
        'cityId',
        // 工作性质
        'natureType',
        // 薪资要求
        'wageId',
        // 到岗时间
        'arriveDateType',
        // 是否开通代投
        'isProxyDeliver',
        // 海外经历
        'isAbroad',
        // 985/211
        'isProjectSchool',
        // 博士后经历
        'isPostdoc',
        // 组合 研究方向/学术成果/荣誉奖励
        'textCombination',
        // 研究方向
        'researchDirection',
        // 学术成果
        'academicAchievement',
        // 荣誉奖励
        'reward',
        // 年龄
        'ageId',
        // 职称
        'titleId',
        // 性别
        'gender',
        // 注册来源
        'sourceType',
        // 毕业院校
        'school',
        // 是否开启订阅
        'isSubscribe',
        // 简历刷新时间
        'refreshTimeStart',
        // 简历刷新时间
        'refreshTimeEnd',
        // 学科专业
        'majorId',
        // 最小完整度
        'startComplete',
        // 最大完整度
        'endComplete',
        // 是否绑定微信
        'isWxBind',
        // 最近活跃时间开始
        'startLastActiveTime',
        // 最近活跃时间结束
        'endLastActiveTime',
        // 最近更新时间
        'startLastUpdateTime',
        // 近更新时间
        'endLastUpdateTime',
        // 简历开放状态
        'isResumeStatus',
        // 会员类型
        'vipType',
        // 标签
        'resumeTagId',
        // 分页
        'pageSize',
        // 分页
        'page',
        // 排序相关
        'sortCreateTime',
        'sortLastLoginTime',
        'sortLastActiveTime',
        'sortOnSiteApplyAmount',
        'sortOffSiteApplyAmount',
        'sortDownloadAmount',
        'sortInterviewAmount',
        'sortLastUpdateTime',
        'sortRefreshTime',
        'sortComplete',
        'sortVipExpireTime',
        'sortResumeLibraryCollectAmount',
        'sortResumeLibraryDownloadAmount',
        'sortLinkCount',
        'sortEmailCount',
        'sortOwnChatNum',
        'sortOtherChatNum',
        'resumeType',
        'projectCate',
        'memberStatus'
    ];

    //排序
    const SORT_ASC  = 1;
    const SORT_DESC = 2;
    //创建时间排序
    const SORT_CREATE_TIME_ASC  = 1;
    const SORT_CREATE_TIME_DESC = 2;
    //最近登陆时间排序
    const SORT_LAST_LOGIN_TIME_ASC  = 1;
    const SORT_LAST_LOGIN_TIME_DESC = 2;    //最近登陆时间排序

    const SORT_LAST_ACTIVE_TIME_ASC  = 1;
    const SORT_LAST_ACTIVE_TIME_DESC = 2;

    const SORT_LAST_UPDATE_TIME_ASC  = 1;
    const SORT_LAST_UPDATE_TIME_DESC = 2;

    const SORT_REFRESH_TIME_ASC  = 1;
    const SORT_REFRESH_TIME_DESC = 2;

    //根据站内投递数量排序
    const SORT_ON_SITE_APPLY_AMOUNT_ASC  = 1;
    const SORT_ON_SITE_APPLY_AMOUNT_DESC = 2;
    //根据站外投递数量排序
    const SORT_OFF_SITE_APPLY_AMOUNT_ASC  = 1;
    const SORT_OFF_SITE_APPLY_AMOUNT_DESC = 2;
    //根据约面数量排序
    const SORT_INTERVIEW_AMOUNT_ASC  = 1;
    const SORT_INTERVIEW_AMOUNT_DESC = 2;  //根据约面数量排序

    const SORT_DOWNLOAD_AMOUNT_ASC  = 1;
    const SORT_DOWNLOAD_AMOUNT_DESC = 2;

    const SORT_RESUME_LIBRARY_COLLECT_AMOUNT_ASC  = 1;
    const SORT_RESUME_LIBRARY_COLLECT_AMOUNT_DESC = 2;

    const SORT_RESUME_LIBRARY_DOWNLOAD_AMOUNT_ASC  = 1;
    const SORT_RESUME_LIBRARY_DOWNLOAD_AMOUNT_DESC = 2;

    const SORT_OWN_CHAT_NUM_ASC  = 1;
    const SORT_OWN_CHAT_NUM_DESC = 2;

    const SORT_OTHER_CHAT_NUM_ASC  = 1;
    const SORT_OTHER_CHAT_NUM_DESC = 2;

    //根据简历下载次数排序
    const SORT_RESUME_DOWNLOAD_AMOUNT_ASC  = 1;
    const SORT_RESUME_DOWNLOAD_AMOUNT_DESC = 2;

    //根据简历完整度排序
    const SORT_RESUME_COMPLETE_ASC  = 1;
    const SORT_RESUME_COMPLETE_DESC = 2;

    //根据简历完整度排序
    const SORT_VIP_EXPIRE_TIME_ASC  = 1;
    const SORT_VIP_EXPIRE_TIME_DESC = 2;

    //根据站外投递（邮件）排序
    const SORT_EMAIL_COUNT_ASC  = 1;
    const SORT_EMAIL_COUNT_DESC = 2;

    //根据站外投递（链接）排序
    const SORT_LINK_COUNT_ASC  = 1;
    const SORT_LINK_COUNT_DESC = 2;

    const DEFAULT_PAGE_SIZE = 20;
    const MAX_PAGE_SIZE     = 100;

    public static function getInstance()
    {
        if (!self::$instance instanceof self) {
            self::$instance = new self();
        }

        return self::$instance;
    }

    public function getList($params, $adminId)
    {
        try {
            $this->adminId        = $adminId;
            $this->originalParams = $params;
            // 做一些参数的简单过滤和处理
            $this->handelParams();

            // 人才查询开启db2
            BaseActiveRecord::openDb2();
            $this->search();
            BaseActiveRecord::closeDb2();
        } catch (\Exception $e) {
            // 往外抛异常sortOwnChatNum
            throw $e;
        }

        return $this->result;
    }

    /**
     * @return voidconst baseShowList = [
     * { k: 'uid', v: 'UID', select: true, default: true },
     * { k: 'name', v: '姓名', select: true, default: true },
     * { k: 'username', v: '用户名', select: true, default: true },
     * { k: 'baseInfo', v: '基本信息', select: true, default: true },
     * { k: 'resumeCompletePercent', v: '简历完整度', select: true, default: true },
     * { k: 'workStatusTxt', v: '求职状态', select: true, default: true },
     * { k: 'onSiteApplyAmount', v: '站内投递', select: true, default: true },
     * { k: 'offSiteApplyAmount', v: '站外投递', select: true, default: true },
     * { k: 'interviewAmount', v: '约面(次)', select: true, default: true },
     * { k: 'resumeDownloadAmount', v: '简历下载', select: true, default: true },
     * { k: 'resumeLibraryCollectAmount', v: '人才库收藏数量', select: true, default: true },
     * { k: 'resumeLibraryDownloadAmount', v: '人才库下载数量', select: true, default: true },
     * // { k: 'createTime', v: '创建时间', select: true, default: true },
     * { k: 'createTime', v: '注册时间', select: true, default: true },
     * { k: 'lastLoginTime', v: '最近登录', select: true, default: true },
     * { k: 'lastActiveTime', v: '最近活跃', select: true, default: true },
     * { k: 'sourceTypeTxt', v: '注册来源', select: true, default: false },
     * { k: 'highSchoolName', v: '最高学历毕业学院', select: true, default: false },
     * { k: 'bindTxt', v: '是否绑定微信', select: true, default: false },
     * { k: 'lastUpdateTime', v: '最近更新时间', select: true, default: false },
     * { k: 'emailCount', v: '站外投递(邮)', select: true, default: false },
     * { k: 'linkCount', v: '站外投递(网)', select: true, default: false },
     * { k: 'isSubscribe', v: '是否开启订阅', select: true, default: false },
     * { k: 'refreshTime', v: '简历刷新时间', select: true, default: false },
     * { k: 'resumeTypeTag', v: '简历类型', select: true, default: false },
     * { k: 'resumeStatusName', v: '简历开放状态', select: true, default: false },
     * { k: 'vipTypeName', v: '会员类型', select: true, default: false },
     * { k: 'vipExpireTime', v: '会员到期时间', select: true, default: false }
     * ]
     */
    private function search()
    {
        $query = BaseResume::find()
            ->alias('a')
            ->innerJoin(['b' => BaseMember::tableName()], 'a.member_id = b.id')
            ->innerJoin(['c' => BaseResumeSetting::tableName()], 'c.resume_id = a.id')
            ->innerJoin(['d' => ResumeStatData::tableName()], 'd.resume_id = a.id');

        $select = [
            'cancelStatus'                => 'b.cancel_status',
            'memberStatus'                => 'b.status',
            'uid'                         => 'a.uuid',
            'status'                      => 'b.status',
            'id'                          => 'a.id',
            'name'                        => 'a.name',
            'username'                    => 'b.username',
            'gender'                      => 'gender',
            'isHideResume'                => 'is_hide_resume',
            'resumeId'                    => 'a.id',
            'memberId'                    => 'a.member_id',
            'lastEducationId'             => 'last_education_id',
            'createTime'                  => 'a.add_time',
            'lastLoginTime'               => 'last_login_time',
            'lastActiveTime'              => 'last_active_time',
            'lastUpdateTime'              => 'last_update_time',
            'refreshTime'                 => 'refresh_time',
            'nativePlaceAreaId'           => 'native_place_area_id',
            'age'                         => 'age',
            'resumeCompletePercent'       => 'complete',
            'sourceType'                  => 'source_type',
            'vipLevel'                    => 'vip_level',
            'vipType'                     => 'vip_type',
            'vipExpireTime'               => 'vip_expire_time',
            'workStatus'                  => 'work_status',
            'arriveDateType'              => 'arrive_date_type',
            'onSiteApplyAmount'           => 'on_site_apply_amount',
            'offSiteApplyAmount'          => 'off_site_apply_amount',
            'interviewRecordAmount'       => 'interview_record_amount',
            'resumeDownloadAmount'        => 'resume_download_amount',
            'resumeLibraryDownloadAmount' => 'resume_library_download_amount',
            'resumeLibraryCollectAmount'  => 'resume_library_collect_amount',
            'isChat'                      => 'is_chat',
            'resumeType'                  => 'a.resume_type',
        ];

        // 用户状态
        if ($this->params['memberStatus']) {
            // 1：正常；2: 已注销；3:注销中；4：已禁用
            $memberStatus = StringHelper::changeStrToFilterArr($this->params['member_status']);
            $memberStatusWhere = [];
            if (in_array('1', $memberStatus) || in_array('4', $memberStatus) || in_array('2', $memberStatus)) {
                if (in_array('1', $memberStatus)) {
                    $memberStatusWhere[] = ['b.status' => BaseMember::STATUS_ACTIVE];
                }
                if (in_array('2', $memberStatus)) {
                    $memberStatusWhere[] = ['b.status' => BaseMember::STATUS_RESUME_CANCELED];
                }
                if (in_array('4', $memberStatus)) {
                    $memberStatusWhere[] = ['b.status' => BaseMember::STATUS_ILLEGAL];
                }

                if (in_array('3', $memberStatus)) {
                    $memberStatusWhere[] = ['b.cancel_status' => BaseMember::CANCEL_STATUS_CANCELING];
                }
            }
            if ($memberStatusWhere) {
                array_unshift($memberStatusWhere, 'or');
                $query->andWhere($memberStatusWhere);
            }
        }

        $isJoinEducation = false;
        if ($this->params['majorId'] || $this->params['educationId'] || $this->params['school']) {
            $query->innerJoin(['e' => ResumeEducation::tableName()], 'e.id = a.last_education_id');

            $select['majorId']     = 'e.major_id';
            $select['educationId'] = 'e.education_id';
            $select['school']      = 'e.school';

            if ($this->params['educationId']) {
                $educationId = BaseDictionary::getEducationValueByKey($this->params['educationId']);
                $query->andWhere([
                    'e.education_id' => $educationId,
                ]);
            }

            // majorId
            if ($this->params['majorId']) {
                $query->andWhere([
                    'e.major_id' => $this->params['majorId'],
                ]);
            }

            // school like
            if ($this->params['school']) {
                $query->andWhere([
                    'like',
                    'e.school',
                    $this->params['school'],
                ]);
            }

            $isJoinEducation = true;
        }

        if ($this->params['isPostdoc']) {
            $query->innerJoin(['r' => BaseResumeWork::tableName()], 'r.resume_id = a.id');
        }

        if ($this->params['handelAgeId']) {
            $this->handelAgeId($this->params['ageId']);
        }

        // name like
        // uuid 等于
        // username like
        if ($this->params['userKeyword']) {
            // 这里考虑一种情况,就是找的就是uuid,那么就没有必要往下找了
            $id = BaseResume::findOneVal([
                'uuid' => $this->params['userKeyword'],
            ], 'id');

            if ($id) {
                $query->andWhere([
                    'a.id' => $id,
                ]);
            } else {
                $query->andWhere([
                    'or',
                    [
                        'like',
                        'a.name',
                        $this->params['userKeyword'],
                    ],
                    [
                        'like',
                        'b.username',
                        $this->params['userKeyword'],
                    ],
                    [
                        'like',
                        'a.uuid',
                        $this->params['userKeyword'],
                    ],
                ]);
            }
        }

        // 手机区号
        $query->andFilterWhere([
            'b.mobile_code' => $this->params['mobileCode'],
        ]);
        // 手机号
        $query->andFilterWhere([
            'like',
            'b.mobile',
            $this->params['mobile'],
        ]);
        // 身份
        $query->andFilterWhere(['identity_type' => $this->params['identityType']]);
        // 工作年限
        if ($this->params['workYears']) {
            $workYearInfo = BaseDictionary::WORK_YEARS_LIST[$this->params['workYears']];
            $query->andFilterWhere([
                'between',
                'work_experience',
                $workYearInfo['min'],
                $workYearInfo['max'],
            ]);
        }
        //求职状态
        $query->andFilterWhere(['a.work_status' => $this->params['workStatus']]);
        // 户籍/国籍
        $query->andFilterWhere([
            // 'native_place_area_id' => $this->params['householdRegisterId'],
            'household_register_id' => $this->params['householdRegisterId'],
        ]);
        // 现居住地
        if ($this->params['areaId']) {
            // 需要用find_in_set residence
            $areaQuery = BaseMember::formatFindInSetQuery($this->params['areaId'], 'residence');
            $query->andWhere($areaQuery);
        }
        // 注册时间
        $query->andFilterWhere([
            '>=',
            'a.add_time',
            $this->params['startCreateTime'],
        ]);
        $query->andFilterWhere([
            '<=',
            'a.add_time',
            $this->params['endCreateTime'],
        ]);
        // 最近登录时间
        $query->andFilterWhere([
            '>=',
            'b.last_login_time',
            $this->params['startLastLoginTime'],
        ]);
        $query->andFilterWhere([
            '<=',
            'b.last_login_time',
            $this->params['endLastLoginTime'],
        ]);
        // 最近更新时间
        $query->andFilterWhere([
            '>=',
            'a.last_update_time',
            $this->params['startLastUpdateTime'],
        ]);
        $query->andFilterWhere([
            '<=',
            'a.last_update_time',
            $this->params['endLastUpdateTime'],
        ]);
        // 刷新时间
        $query->andFilterWhere([
            '>=',
            'a.refresh_time',
            $this->params['refreshTimeStart'],
        ]);
        $query->andFilterWhere([
            '<=',
            'a.refresh_time',
            $this->params['refreshTimeEnd'],
        ]);

        // 活跃时间
        $query->andFilterWhere([
            '>=',
            'b.last_active_time',
            $this->params['startLastActiveTime'],
        ]);

        $query->andFilterWhere([
            '<=',
            'b.last_active_time',
            $this->params['endLastActiveTime'],
        ]);

        // 求职意向四件套(需要特殊判断)
        // 意向职位
        // 意向城市
        // 意向工作性质
        // 意向薪资
        if ($this->params['jobCategory'] || $this->params['cityId'] || $this->params['natureType'] || $this->params['wageId']) {
            $query->innerJoin(['f' => BaseResumeIntention::tableName()], 'f.resume_id = a.id');

            // cityId
            if ($this->params['cityId']) {
                $query->innerJoin(['g' => BaseResumeIntentionAreaRelation::tableName()], 'g.resume_id = a.id');
                $query->andWhere([
                    'g.area_id' => $this->params['cityId'],
                ]);
            }

            // 意向职位
            if ($this->params['jobCategory']) {
                $query->andWhere([
                    'f.job_category_id' => $this->params['jobCategory'],
                ]);
            }

            // 意向工作性质
            if ($this->params['natureType']) {
                $query->andWhere([
                    'f.nature_type' => $this->params['natureType'],
                ]);
            }

            // 意向薪资
            if ($this->params['wageId']) {
                $wageInfo = Dictionary::getMinAndMaxWage($this->params['wageId']);

                //面议
                if ($wageInfo['min'] == 0 && $wageInfo['max'] == 0) {
                    $query->andWhere([
                        'min_wage' => 0,
                    ]);
                    $query->andWhere([
                        'max_wage' => 0,
                    ]);
                } else {
                    if ($wageInfo['min'] > 0) {
                        $query->andWhere([
                            '>=',
                            'min_wage',
                            (int)$wageInfo['min'],
                        ]);
                    }

                    if ($wageInfo['max'] > 0) {
                        $query->andWhere([
                            '<=',
                            'max_wage',
                            (int)$wageInfo['max'],
                        ]);
                    }
                    //除了以上条件，还必须保证两个不能同事为空
                    $query->andWhere([
                        'or',
                        [
                            '>',
                            'max_wage',
                            0,
                        ],
                        [
                            '>',
                            'min_wage',
                            0,
                        ],
                    ]);
                }
            }

            $query->groupBy('a.id');
        }

        // 到岗时间
        $query->andFilterWhere(['arrive_date_type' => $this->params['arriveDateType']]);
        //是否开通代投
        $query->andFilterWhere(['is_proxy_deliver' => $this->params['isProxyDeliver']]);
        //简历开放状态
        $query->andFilterWhere(['is_hide_resume' => $this->params['isResumeStatus']]);
        //985/211
        $query->andFilterWhere(['a.is_project_school' => $this->params['isProjectSchool']]);
        //博士后经历 - 优化：直接使用resume表的冗余字段
        $query->andFilterWhere(['a.is_postdoc' => $this->params['isPostdoc']]);
        // 海外经历
        if ($this->params['isAbroad']) {
            // 教育或者工作经历是有海外的
            if ($this->params['isAbroad'] == 1) {
                $query->andFilterWhere(['a.is_abroad' => 1]);
            } else {
                // 不等于1
                $query->andFilterWhere([
                    '<>',
                    'a.is_abroad',
                    1,
                ]);
            }
        }
        //会员类型
        if (strlen($this->params['vipType']) > 0) {
            if ($this->params['vipType'] == 2) {
                $query->andWhere([
                    'a.vip_type'  => BaseResume::VIP_TYPE_ACTIVE,
                    'a.vip_level' => BaseResume::VIP_LEVEL_DIAMOND,
                ]);
            } elseif ($this->params['vipType'] == 1) {
                $query->andWhere([
                    'a.vip_type'  => $this->params['vipType'],
                    'a.vip_level' => BaseResume::VIP_LEVEL_GOLD,
                ]);
            } else {
                $query->andWhere(['a.vip_type' => $this->params['vipType']]);
            }
        }
        // 性别
        $query->andFilterWhere(['gender' => $this->params['gender']]);
        // 注册来源
        $query->andFilterWhere(['source_type' => $this->params['sourceType']]);
        // 是否开启订阅,这个需要去到job_subscribe表里面去找
        if ($this->params['isSubscribe']) {
            $query->leftJoin(['i' => BaseJobSubscribe::tableName()], 'i.resume_id = a.id');
            if ($this->params['isSubscribe'] == 1) {
                $query->andWhere([
                    '>',
                    'i.id',
                    0,
                ]);
            } else {
                $query->andWhere([
                    'i.id' => null,
                ]);
            }
        }

        // textCombination 研究方向/学术成果/荣誉奖励  researchDirection
        // academicAchievement
        // reward
        if ($this->params['textCombination']) {
            $query->leftJoin(['rrd' => BaseResumeResearchDirection::tableName()], 'rrd.member_id = b.id');
            // 学术成果 = 学术论文 学术专利 学术专著
            $query->leftJoin(['rap' => BaseResumeAcademicPage::tableName()], 'rap.member_id = b.id');
            $query->leftJoin(['rra' => BaseResumeAcademicPatent::tableName()], 'rra.member_id = b.id');
            $query->leftJoin(['rrb' => BaseResumeAcademicBook::tableName()], 'rrb.member_id = b.id');
            // 荣誉奖励 = 学术奖励 其他荣誉
            $query->leftJoin(['rar' => BaseResumeAcademicReward::tableName()], 'rar.member_id = b.id');
            $query->leftJoin(['rro' => BaseResumeOtherReward::tableName()], 'rro.member_id = b.id');

            $query->andWhere([
                'or',
                [
                    'like',
                    'rrd.content',
                    $this->params['textCombination'],
                ],
                [
                    'like',
                    'rap.description',
                    $this->params['textCombination'],
                ],
                [
                    'like',
                    'rra.description',
                    $this->params['textCombination'],
                ],
                [
                    'like',
                    'rrb.name',
                    $this->params['textCombination'],
                ],
                [
                    'like',
                    'rar.name',
                    $this->params['textCombination'],
                ],
                [
                    'like',
                    'rro.name',
                    $this->params['textCombination'],
                ],
            ]);

            $query->groupBy('a.id');
        }

        // 研究方向 researchDirection
        if (!empty($this->params['researchDirection'])) {
            $query->innerJoin(['rrd' => BaseResumeResearchDirection::tableName()], 'rrd.member_id = b.id');
            $query->andWhere([
                'like',
                'rrd.content',
                $this->params['researchDirection'],
            ]);
            $query->groupBy('a.id');
        }

        // 学术成果 academicAchievement
        if (!empty($this->params['academicAchievement'])) {
            // 学术成果 = 学术论文 学术专利 学术专著
            $query->leftJoin(['rap' => BaseResumeAcademicPage::tableName()], 'rap.member_id = b.id');
            $query->leftJoin(['rra' => BaseResumeAcademicPatent::tableName()], 'rra.member_id = b.id');
            $query->leftJoin(['rrb' => BaseResumeAcademicBook::tableName()], 'rrb.member_id = b.id');

            $query->andWhere([
                'or',
                [
                    'like',
                    'rap.description',
                    $this->params['academicAchievement'],
                ],
                [
                    'like',
                    'rra.description',
                    $this->params['academicAchievement'],
                ],
                [
                    'like',
                    'rrb.name',
                    $this->params['academicAchievement'],
                ],
            ]);
            $query->groupBy('a.id');
        }

        // 荣誉奖励 reward
        if (!empty($this->params['reward'])) {
            // 荣誉奖励 = 学术奖励 其他荣誉
            $query->leftJoin(['rar' => BaseResumeAcademicReward::tableName()], 'rar.member_id = b.id');
            $query->leftJoin(['rro' => BaseResumeOtherReward::tableName()], 'rro.member_id = b.id');

            $query->andFilterWhere([
                'or',
                [
                    'like',
                    'rar.name',
                    $this->params['reward'],
                ],
                [
                    'like',
                    'rro.name',
                    $this->params['reward'],
                ],
            ]);
            $query->groupBy('a.id');
        }

        // 完善度
        $query->andFilterWhere([
            '>=',
            'complete',
            $this->params['startComplete'],
        ]);

        $query->andFilterWhere([
            '<=',
            'complete',
            $this->params['endComplete'],
        ]);

        // 微信绑定
        $isJoinWxBind = false;
        if ($this->params['isWxBind']) {
            $isJoinWxBind = true;
            $query->leftJoin(['wb' => BaseResumeWxBind::tableName()], 'wb.resume_id = a.id');
            $select['bindId'] = 'wb.id';
            if ($this->params['isWxBind'] == 1) {
                $query->andWhere([
                    '<>',
                    'wb.unionid',
                    '',
                ]);
            } else {
                $query->andWhere([
                    'or',
                    [
                        'wb.unionid' => '',
                    ],
                    [
                        'is',
                        'wb.id',
                        null,
                    ],
                ]);
            }
        }

        // 职称
        $query->andFilterWhere([
            'title_id' => $this->params['titleId'],
        ]);

        // 年龄
        if ($this->params['minAge']) {
            $query->andWhere([
                '>=',
                'age',
                $this->params['minAge'],
            ]);
        }
        if ($this->params['maxAge']) {
            $query->andWhere([
                '<=',
                'age',
                $this->params['maxAge'],
            ]);
        }

        // 标签
        if ($this->params['resumeTagId']) {
            $query->innerJoin(['rtr' => BaseResumeTagRelation::tableName()], 'rtr.resume_id = a.id');
            $query->andWhere([
                'rtr.resume_tag_id' => $this->params['resumeTagId'],
            ]);
        }

        if (!empty($this->params['projectCate'])) {
            $query->leftJoin(['rp' => BaseResumeResearchProject::tableName()], 'rp.resume_id = a.id');
            $query->andWhere([
                'rp.category' => $this->params['projectCate'],
            ]);
        }

        $query->andFilterWhere([
            'in',
            'a.resume_type',
            $this->params['resumeType'],
        ]);

        $count = $query->count();

        // 开始做排序逻辑

        $list = $query->select($select)
            ->limit($this->limit)
            ->offset($this->offset)
            ->orderBy($this->order)
            ->asArray()
            ->all();

        foreach ($list as &$record) {
            if (!$isJoinEducation) {
                // 这个时候educationId 和 majorId  school 也都是没有的
                $topEducation = BaseResumeEducation::find()
                    ->where([
                        'id' => $record['lastEducationId'],
                    ])
                    ->asArray()
                    ->one();

                $record['majorId']     = $topEducation['major_id'];
                $record['educationId'] = $topEducation['education_id'];
                $record['school']      = $topEducation['school'];
            }
            // 性别
            $genderName    = Resume::getGenderName($record['gender']) ?: '-';                       //性别
            $educationName = Dictionary::getEducationName($record['educationId']) ?: '-';          //学历
            $majorName     = Major::findOneVal([
                'id' => $record['major'],
            ], 'name',) ?: '-';                       //专业

            $nativePlaceAreaId      = Area::getAreaName($record['nativePlaceAreaId']) ?: '-';           //户籍
            $age                    = $record['age'] ? $record['age'] . '岁' : '-';                          //年龄
            $identityExperienceText = BaseResume::getIdentityExperienceText($record['resumeId']) ?: '-';
            $record['baseInfo']     = $age . '/' . $genderName . '/' . $educationName . '/' . $identityExperienceText . '/' . $majorName . '/' . $nativePlaceAreaId;
            //获取简历完成度
            $record['sourceTypeTxt'] = BaseMember::SOURCE_TYPE_LIST[$record['sourceType']];
            if ($record['vipType'] == BaseResume::VIP_TYPE_ACTIVE) {
                $record['vipTypeName'] = BaseResume::VIP_LEVEL_LIST[$record['vipLevel']];
            } else {
                $record['vipTypeName'] = BaseResume::VIP_TYPE_LIST[$record['vipType']] ?: '';
            }

            // 如果是空或者是0000-00-00 00:00:00就显示为''
            $record['vipExpireTime'] = $record['vipExpireTime'] == '0000-00-00 00:00:00' ? '-' : $record['vipExpireTime'];
            //获取到岗时间
            $workStatus = Dictionary::getJobStatusName($record['workStatus']);

            if ($record['arriveDateType'] != Resume::CUSTOM_ARRIVE_DATE_TYPE) {
                $arriveDate = Dictionary::getArriveDateName($record['arriveDateType']);
            } else {
                $arriveDate = $record['arriveDateType'];
            }

            $record['workStatusTxt'] = $workStatus . '-' . $arriveDate;
            if (!$isJoinWxBind) {
                $record['bindTxt'] = BaseResumeWxBind::checkWxBind($record['resumeId']) ? '已绑定' : '未绑定';
            } else {
                $record['bindTxt'] = $record['bindId'] ? '已绑定' : '未绑定';
            }
            //是否开启职位订阅
            if ($record['subscribeId'] > 0) {
                $record['isSubscribe'] = '是';
            } else {
                $record['isSubscribe'] = '否';
            }
            //简历类型
            $resumeTypeData            = BaseResume::getResumeLevel($record['resumeId']);
            $record['resume_type_tag'] = $resumeTypeData['name'];
            //简历开放状态
            $record['resumeStatusName'] = $record['isHideResume'] == BaseResumeSetting::IS_HIDE_RESUME_YES ? '否' : '是';

            $record['linkCount']  = BaseJobApplyRecord::getLinkCount($record['resumeId']);
            $record['emailCount'] = BaseJobApplyRecord::getEmailCount($record['resumeId']);

            $record['ownChatNum']   = BaseChatRoom::find()
                ->where([
                    'resume_id'    => $record['resumeId'],
                    'creator_type' => BaseChatRoom::CREATOR_TYPE_PERSON,
                ])
                ->count();
            $record['otherChatNum'] = BaseChatRoom::find()
                ->where([
                    'resume_id'    => $record['resumeId'],
                    'creator_type' => BaseChatRoom::CREATOR_TYPE_COMPANY,
                ])
                ->count();

            $resumeTagArray = BaseResumeTag::find()
                ->select('tag,id')
                ->where([
                    'id' => BaseResumeTagRelation::find()
                        ->select('resume_tag_id')
                        ->where([
                            'resume_id' => $record['resumeId'],
                        ]),
                ])
                ->asArray()
                ->all();

            $record['resumeTag']   = implode(',', ArrayHelper::getColumn($resumeTagArray, 'tag'));
            $record['resumeTagId'] = ArrayHelper::getColumn($resumeTagArray, 'id');
            $record['resumeType']  = BaseResume::RESUME_TYPE_LIST[$record['resumeType']];

            // 追加状态描述，注销状态弄到一起
            $record['statusTxt'] = BaseMember::COMPANY_STATUS_LIST[$record['memberStatus']];
            if ($record['cancelStatus'] == BaseMember::CANCEL_STATUS_CANCELING) {
                $record['statusTxt'] = '注销中';
            }

            // 如果注销中或者注销成功需要显示注销原因
            $record['cancelReason'] = '';
            if ($record['cancelStatus'] == BaseMember::CANCEL_STATUS_CANCELING || $record['cancelStatus'] == BaseMember::CANCEL_STATUS_CANCELED) {
                $record['cancelReason'] = BaseResumeCancelLog::getLastResumeLog($record['resumeId']);
            }
        }

        $this->result['list'] = $list;

        $this->result['page'] = [
            'limit' => $this->limit,
            'count' => $count,
            'page'  => $this->page,
        ];
    }

    private function handelParams()
    {
        foreach ($this->originalParams as $k => $v) {
            if (strlen($v) == 0) {
                continue;
            }
            if (in_array($k, self::SEARCH_PARAMS)) {
                // 根据不同的key,去到不同的handel
                $value = $v;
                // 去掉空格
                $value = trim($value);
                if (strlen($value) == 0) {
                    continue;
                }
                $handel = 'handel' . ucfirst($k);
                // 判断一下是否有这个方法
                if (!method_exists($this, $handel)) {
                    continue;
                }
                $this->$handel($v);
            } else {
                // 抛异常
                throw new \Exception('参数错误' . $k);
            }
        }
        // 设置一下分页信息
        if ($this->originalParams['pageSize']) {
            $this->pageSize = $this->originalParams['pageSize'];
        } else {
            $this->pageSize = self::DEFAULT_PAGE_SIZE;
        }

        if ($this->pageSize > self::MAX_PAGE_SIZE) {
            $this->pageSize = self::MAX_PAGE_SIZE;
        }

        if ($this->originalParams['page']) {
            $this->page = $this->originalParams['page'];
        } else {
            $this->page = 1;
        }

        $this->limit  = $this->pageSize;
        $this->offset = ($this->page - 1) * $this->pageSize;
    }

    // 关键字 搜索 人才姓名/ID/用户名
    private function handelUserKeyword($value)
    {
        $this->params['userKeyword'] = $value;
    }

    private function handelMobileCode($value)
    {
        // 去掉前面的+
        $mobileCode = ltrim($value, '+');

        $this->params['mobileCode'] = $mobileCode;
    }

    private function handelMobile($value)
    {
        // 看看有没有mobilecode过来
        $this->params['mobile'] = $value;
    }

    // 最高学历
    // {
    //             "k": "4",
    //             "v": "博士研究生"
    //         },
    //         {
    //             "k": "8",
    //             "v": "硕士及以上"
    //         },
    //         {
    //             "k": "3",
    //             "v": "硕士研究生"
    //         },
    //         {
    //             "k": "7",
    //             "v": "本科及以上"
    //         },
    //         {
    //             "k": "2",
    //             "v": "本科"
    //         },
    //         {
    //             "k": "6",
    //             "v": "大专及以上"
    //         },
    //         {
    //             "k": "1",
    //             "v": "大专"
    //         },
    //         {
    //             "k": "5",
    //             "v": "其他"
    //         }
    private function handelEducationId($value)
    {
        // 判断必须是上面的值
        $educationId = $value;
        if (!in_array($educationId, [
            4,
            8,
            3,
            7,
            2,
            6,
            1,
            5,
        ])) {
            throw new \Exception('参数错误');
        }
        $this->params['educationId'] = $educationId;
    }

    // 身份(只有1和2)
    private function handelIdentityType($value)
    {
        $identityType = $value;
        if (!in_array($identityType, [
            1,
            2,
        ])) {
            throw new \Exception('参数错误');
        }
        $this->params['identityType'] = $identityType;
    }

    // 毕业开始时间
    private function handelGraduateBeginDate($value)
    {
        // 开始时间添加000
        $this->params['graduateBeginDate'] = TimeHelper::dayToBeginTime($value);
    }

    // 毕业结束时间
    private function handelGraduateEndDate($value)
    {
        // 结束时间添加23:59:59
        $this->params['graduateEndDate'] = TimeHelper::dayToEndtime($value);
    }

    // 工作年限
    private function handelWorkYears($value)
    {
        $this->params['workYears'] = $value;
    }

    // 求职状态
    private function handelWorkStatus($value)
    {
        $this->params['workStatus'] = $value;
    }

    // 户籍国籍 逗号隔开的
    private function handelHouseholdRegisterId($value)
    {
        $this->params['householdRegisterId'] = explode(',', $value);
    }

    // 现居住地 逗号隔开
    private function handelAreaId($value)
    {
        $this->params['areaId'] = explode(',', $value);
    }

    // 注册时间开始
    private function handelStartCreateTime($value)
    {
        $this->params['startCreateTime'] = TimeHelper::dayToBeginTime($value);
    }

    // 注册时间结束
    private function handelEndCreateTime($value)
    {
        $this->params['endCreateTime'] = TimeHelper::dayToEndtime($value);
    }

    // 最近登录时间
    private function handelStartLastLoginTime($value)
    {
        $this->params['startLastLoginTime'] = TimeHelper::dayToBeginTime($value);
    }

    // 最近登录时间
    private function handelEndLastLoginTime($value)
    {
        $this->params['endLastLoginTime'] = TimeHelper::dayToEndtime($value);
    }

    // 求职意向
    private function handelJobCategory($value)
    {
        $this->params['jobCategory'] = $value;
    }

    // 意向城市 逗号隔开
    private function handelCityId($value)
    {
        $this->params['cityId'] = explode(',', $value);
    }

    // 工作性质
    private function handelNatureType($value)
    {
        $this->params['natureType'] = $value;
    }

    // 薪资要求
    private function handelWageId($value)
    {
        $this->params['wageId'] = $value;
    }

    // 到岗时间
    private function handelArriveDateType($value)
    {
        $this->params['arriveDateType'] = $value;
    }

    // 是否开通代投
    private function handelIsProxyDeliver($value)
    {
        $this->params['isProxyDeliver'] = $value;
    }

    // 海外经历
    private function handelIsAbroad($value)
    {
        $this->params['isAbroad'] = $value;
    }

    // 985/211
    private function handelIsProjectSchool($value)
    {
        $this->params['isProjectSchool'] = $value;
    }

    // 博士后经历
    private function handelIsPostdoc($value)
    {
        $this->params['isPostdoc'] = $value;
    }

    // 组合 研究方向/学术成果/荣誉奖励
    private function handelTextCombination($value)
    {
        $this->params['textCombination'] = $value;
    }

    // 研究方向
    private function handelResearchDirection($value)
    {
        $this->params['researchDirection'] = $value;
    }

    // 学术成果
    private function handelAcademicAchievement($value)
    {
        $this->params['academicAchievement'] = $value;
    }

    // 荣誉奖励
    private function handelReward($value)
    {
        $this->params['reward'] = $value;
    }

    // 年龄
    private function handelAgeId($value)
    {
        $wageInfo               = Resume::getMixAndMaxAge($value['ageId']);
        $this->params['minAge'] = $wageInfo['min'];
        $this->params['maxAge'] = $wageInfo['max'];
    }

    // 职称 逗号隔开
    private function handelTitleId($value)
    {
        $this->params['titleId'] = explode(',', $value);
    }

    // 性别
    private function handelGender($value)
    {
        $this->params['gender'] = $value;
    }

    // 注册来源
    private function handelSourceType($value)
    {
        $this->params['sourceType'] = $value;
    }

    // 毕业院校
    private function handelSchool($value)
    {
        $this->params['school'] = $value;
    }

    // 是否开启订阅
    private function handelIsSubscribe($value)
    {
        $this->params['isSubscribe'] = $value;
    }

    // 简历刷新时间
    private function handelRefreshTimeStart($value)
    {
        $this->params['refreshTimeStart'] = TimeHelper::dayToBeginTime($value);
    }

    // 简历刷新时间
    private function handelRefreshTimeEnd($value)
    {
        $this->params['refreshTimeEnd'] = TimeHelper::dayToEndtime($value);
    }

    // 最近更新时间
    private function handelStartLastUpdateTime($value)
    {
        $this->params['startLastUpdateTime'] = TimeHelper::dayToBeginTime($value);
    }

    // 最近更新时间
    private function handelEndLastUpdateTime($value)
    {
        $this->params['endLastUpdateTime'] = TimeHelper::dayToEndtime($value);
    }

    // 学科专业 majorId逗号隔开的,要变成数组
    private function handelMajorId($value)
    {
        $this->params['majorId'] = explode(',', $value);
    }

    // 最小完整度
    private function handelStartComplete($value)
    {
        $this->params['startComplete'] = $value;
    }

    // 最大完整度
    private function handelEndComplete($value)
    {
        $this->params['endComplete'] = $value;
    }

    // 是否绑定微信
    private function handelIsWxBind($value)
    {
        $this->params['isWxBind'] = $value;
    }

    // 最近活跃时间开始
    private function handelStartLastActiveTime($value)
    {
        $this->params['startLastActiveTime'] = TimeHelper::dayToBeginTime($value);
    }

    // 最近活跃时间结束
    private function handelEndLastActiveTime($value)
    {
        $this->params['endLastActiveTime'] = TimeHelper::dayToEndtime($value);
    }

    // 简历开放状态
    private function handelIsResumeStatus($value)
    {
        $this->params['isResumeStatus'] = $value;
    }

    // 会员类型
    private function handelVipType($value)
    {
        $this->params['vipType'] = $value;
    }

    private function handelResumeTagId($value)
    {
        $this->params['resumeTagId'] = explode(',', $value);
    }

    private function handelSortCreateTime($value)
    {
        // 判断是和上面的值符合,然后设置排序
        if ($value == self::SORT_CREATE_TIME_ASC) {
            $this->order = 'a.add_time asc';
        } elseif ($value == self::SORT_CREATE_TIME_DESC) {
            $this->order = 'a.add_time desc';
        }
    }

    private function handelSortLastLoginTime($value)
    {
        if ($value == self::SORT_LAST_LOGIN_TIME_ASC) {
            $this->order = 'b.last_login_time asc';
        } elseif ($value == self::SORT_LAST_LOGIN_TIME_DESC) {
            $this->order = 'b.last_login_time desc';
        }
    }

    private function handelSortLastActiveTime($value)
    {
        if ($value == self::SORT_LAST_ACTIVE_TIME_ASC) {
            $this->order = 'b.last_active_time asc';
        } elseif ($value == self::SORT_LAST_ACTIVE_TIME_DESC) {
            $this->order = 'b.last_active_time desc';
        }
    }

    private function handelSortLastUpdateTime($value)
    {
        if ($value == self::SORT_LAST_UPDATE_TIME_ASC) {
            $this->order = 'a.last_update_time asc';
        } elseif ($value == self::SORT_LAST_UPDATE_TIME_DESC) {
            $this->order = 'a.last_update_time desc';
        }
    }

    private function handelSortRefreshTime($value)
    {
        if ($value == self::SORT_REFRESH_TIME_ASC) {
            $this->order = 'a.refresh_time asc';
        } elseif ($value == self::SORT_REFRESH_TIME_DESC) {
            $this->order = 'a.refresh_time desc';
        }
    }

    private function handelSortComplete($value)
    {
        if ($value == self::SORT_RESUME_COMPLETE_ASC) {
            $this->order = 'a.complete asc';
        } elseif ($value == self::SORT_RESUME_COMPLETE_DESC) {
            $this->order = 'a.complete desc';
        }
    }

    private function handelSortVipExpireTime($value)
    {
        if ($value == self::SORT_VIP_EXPIRE_TIME_ASC) {
            $this->order = 'a.vip_expire_time asc';
        } elseif ($value == self::SORT_VIP_EXPIRE_TIME_DESC) {
            $this->order = 'a.vip_expire_time desc';
        }
    }

    private function handelSortOnSiteApplyAmount($value)
    {
        if ($value == self::SORT_ON_SITE_APPLY_AMOUNT_ASC) {
            $this->order = 'd.on_site_apply_amount asc';
        } elseif ($value == self::SORT_ON_SITE_APPLY_AMOUNT_DESC) {
            $this->order = 'd.on_site_apply_amount desc';
        }
    }

    private function handelSortOffSiteApplyAmount($value)
    {
        if ($value == self::SORT_OFF_SITE_APPLY_AMOUNT_ASC) {
            $this->order = 'd.off_site_apply_amount asc';
        } elseif ($value == self::SORT_OFF_SITE_APPLY_AMOUNT_DESC) {
            $this->order = 'd.off_site_apply_amount desc';
        }
    }

    private function handelSortInterviewAmount($value)
    {
        if ($value == self::SORT_INTERVIEW_AMOUNT_ASC) {
            $this->order = 'd.interview_amount asc';
        } elseif ($value == self::SORT_INTERVIEW_AMOUNT_DESC) {
            $this->order = 'd.interview_amount desc';
        }
    }

    private function handelSortDownloadAmount($value)
    {
        if ($value == self::SORT_DOWNLOAD_AMOUNT_ASC) {
            $this->order = 'd.resume_download_amount asc';
        } elseif ($value == self::SORT_DOWNLOAD_AMOUNT_DESC) {
            $this->order = 'd.resume_download_amount desc';
        }
    }

    private function handelSortResumeLibraryCollectAmount($value)
    {
        if ($value == self::SORT_RESUME_LIBRARY_COLLECT_AMOUNT_ASC) {
            $this->order = 'd.resume_library_collect_amount asc';
        } elseif ($value == self::SORT_RESUME_LIBRARY_COLLECT_AMOUNT_DESC) {
            $this->order = 'd.resume_library_collect_amount desc';
        }
    }

    private function handelSortResumeLibraryDownloadAmount($value)
    {
        if ($value == self::SORT_RESUME_LIBRARY_DOWNLOAD_AMOUNT_ASC) {
            $this->order = 'd.resume_library_download_amount asc';
        } elseif ($value == self::SORT_RESUME_LIBRARY_DOWNLOAD_AMOUNT_DESC) {
            $this->order = 'd.resume_library_download_amount desc';
        }
    }

    // 求职者主动发起的聊天次数排序
    private function handelSortOwnChatNum($value)
    {
        // 为了少关联表,这里先去直聊表里面找主动发起是求职者的数量,然后进行排序,得出求职者的resumeId的顺序,然后再按照这个顺序来排序
        $resumeIdArray = BaseChatRoom::find()
            ->select('resume_id')
            ->where([
                'creator_type' => BaseChatRoom::CREATOR_TYPE_PERSON,
            ])
            ->groupBy('resume_id')
            ->orderBy('count(*) asc')
            ->column();
        if (!$resumeIdArray) {
            return;
        }
        if ($value == self::SORT_OWN_CHAT_NUM_ASC) {
            $this->order = new Expression('FIELD(a.id,' . implode(',', $resumeIdArray) . ') asc');
        } elseif ($value == self::SORT_OWN_CHAT_NUM_DESC) {
            $this->order = new Expression('FIELD(a.id,' . implode(',', $resumeIdArray) . ') desc');
        }
    }

    private function handelSortOtherChatNum($value)
    {
        // 为了少关联表,这里先去直聊表里面找主动发起是求职者的数量,然后进行排序,得出求职者的resumeId的顺序,然后再按照这个顺序来排序
        $resumeIdArray = BaseChatRoom::find()
            ->select('resume_id')
            ->where([
                'creator_type' => BaseChatRoom::CREATOR_TYPE_COMPANY,
            ])
            ->groupBy('resume_id')
            ->orderBy('count(*) asc')
            ->column();
        if (!$resumeIdArray) {
            return;
        }
        if ($value == self::SORT_OTHER_CHAT_NUM_ASC) {
            $this->order = new Expression('FIELD(a.id,' . implode(',', $resumeIdArray) . ') asc');
        } elseif ($value == self::SORT_OTHER_CHAT_NUM_DESC) {
            $this->order = new Expression('FIELD(a.id,' . implode(',', $resumeIdArray) . ') desc');
        }
        // 加到orderBy里面,按照上面的给a.id排序在最前面(用new Query)
    }

    private function handelSortEmailCount($value)
    {
    }

    private function handelSortLinkCount($value)
    {
    }

    private function handelPage($value)
    {
        // $this->page = $value;
    }

    private function handelPageSize($value)
    {
        // $this->pageSize = $value;
    }

    private function handelResumeType($value)
    {
        if (!is_array($value)) {
            $this->params['resumeType'] = explode(',', $value);
        } else {
            $this->params['resumeType'] = $value;
        }
    }

    private function handelProjectCate($value)
    {
        if (!is_array($value)) {
            $this->params['projectCate'] = explode(',', $value);
        } else {
            $this->params['projectCate'] = $value;
        }
    }

}
