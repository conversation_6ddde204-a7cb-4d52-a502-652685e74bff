<?php

namespace common\libs;

use common\base\models\BaseArea;
use common\base\models\BaseCompany;
use common\base\models\BaseEmailLog;
use common\base\models\BaseFile;
use common\base\models\BaseJob;
use common\base\models\BaseJobApply;
use common\base\models\BaseJobSubscribe;
use common\base\models\BaseMember;
use common\base\models\BaseResume;
use common\base\models\BaseResumeAttachment;
use common\base\models\BaseResumeComplete;
use common\base\models\BaseResumeLibrary;
use common\helpers\FileHelper;
use common\helpers\UrlHelper;
use common\libs\Aliyun\Oss;
use common\service\jobSubscribe\JobSubscribeApplication;
use frontendPc\models\OffSiteJobApply;
use yii;
use yii\base\Exception;
use yii\mail\MessageInterface;

class EmailQueue
{

    const EMAIL_TYPE_REGISTER             = 1;     //注册
    const EMAIL_TYPE_CHANGE_PASSWORD      = 2;     //修改密码
    const EMAIL_TYPE_CHANGE_EMAIL         = 3;     //修改邮箱
    const EMAIL_TYPE_BIND_EMAIL           = 4;     //绑定邮箱
    const EMAIL_TYPE_OFF_SITE_JOB_APPLY   = 5;     //投递站外职位
    const EMAIL_TYPE_JOB_AUDIT_REJECT     = 6;     //职位审核拒绝
    const EMAIL_TYPE_JOB_AUDIT_PASS       = 7;     //职位审核拒绝
    const EMAIL_TYPE_RESUME_CHECK         = 8;     //检查被查看
    const EMAIL_TYPE_RESUME_FIRST_PASS    = 9;     //通过初筛
    const EMAIL_TYPE_RESUME_INTERVIEW     = 10;     //面试邀请
    const EMAIL_TYPE_RESUME_BACK          = 11;     //简历不合适
    const EMAIL_TYPE_JOB_UPDATE           = 12;     //职位更新
    const EMAIL_TYPE_JOB_APPLY            = 13;     //单位被职位投递
    const EMAIL_COMPANY_INFO_AUDIT_PASS   = 14;     //单位信息审核拒接
    const EMAIL_COMPANY_INFO_AUDIT_REJECT = 15;     //单位信息审核通过
    const EMAIL_RESUME_INVITE_JOB_APPLY   = 16;     //单位邀求职者投递
    const EMAIL_PLATFORM_DELIVERY         = 17;     //平台投递
    const EMAIL_POST_DELIVERY             = 18;     //邮件投递
    const EMAIL_NOT_WILL_DELIVERY         = 19;     //非合作单位投递
    const EMAIL_SEEKER_DELIVERY           = 20;     //求职者投递
    const EMAIL_JOB_SUBSCRIBE             = 21;     //职位订阅

    const CACHE_KEY_LIST = [
        self::EMAIL_TYPE_REGISTER        => Cache::SMTP_MEMBER_REGISTER_KEY,
        self::EMAIL_TYPE_CHANGE_PASSWORD => Cache::SMTP_MEMBER_CHANGE_PASSWORD_KEY,
        self::EMAIL_TYPE_CHANGE_EMAIL    => Cache::SMTP_MEMBER_CHANGE_EMAIL_KEY,
        self::EMAIL_TYPE_BIND_EMAIL      => Cache::SMTP_MEMBER_BIND_EMAIL_KEY,
        // self::EMAIL_TYPE_JOB_AUDIT_REJECT   => Cache::SMTP_MEMBER_JOB_AUDIT_END,

    ];

    const EMAIL_TYPE_LIST = [
        self::EMAIL_TYPE_REGISTER             => '注册',
        self::EMAIL_TYPE_CHANGE_PASSWORD      => '修改密码',
        self::EMAIL_TYPE_CHANGE_EMAIL         => '修改邮箱',
        self::EMAIL_TYPE_BIND_EMAIL           => '绑定邮箱',
        self::EMAIL_TYPE_OFF_SITE_JOB_APPLY   => '投递站外职位',
        self::EMAIL_TYPE_JOB_AUDIT_REJECT     => '职位审核拒绝',
        self::EMAIL_TYPE_JOB_AUDIT_PASS       => '职位审核拒绝',
        self::EMAIL_TYPE_RESUME_CHECK         => '检查被查看',
        self::EMAIL_TYPE_RESUME_FIRST_PASS    => '通过初筛',
        self::EMAIL_TYPE_RESUME_INTERVIEW     => '面试邀请',
        self::EMAIL_TYPE_RESUME_BACK          => '简历不合适',
        self::EMAIL_TYPE_JOB_UPDATE           => '职位更新',
        self::EMAIL_TYPE_JOB_APPLY            => '单位被职位投递',
        self::EMAIL_COMPANY_INFO_AUDIT_PASS   => '单位信息审核拒接',
        self::EMAIL_COMPANY_INFO_AUDIT_REJECT => '单位信息审核通过',
        self::EMAIL_RESUME_INVITE_JOB_APPLY   => '单位邀求职者投递',
        self::EMAIL_PLATFORM_DELIVERY         => '平台投递',
        self::EMAIL_POST_DELIVERY             => '邮件投递',
        self::EMAIL_NOT_WILL_DELIVERY         => '非合作单位投递',
        self::EMAIL_SEEKER_DELIVERY           => '求职者投递成功',
        self::EMAIL_JOB_SUBSCRIBE             => '职位订阅',
    ];

    // const TEMPLATE_NAME_LIST = [
    //     self::EMAIL_TYPE_REGISTER           => 'member_register',
    //     self::EMAIL_TYPE_CHANGE_PASSWORD    => 'member_change_password',
    //     self::EMAIL_TYPE_CHANGE_EMAIL       => 'member_change_email',
    //     self::EMAIL_TYPE_BIND_EMAIL         => 'member_bind_email',
    //     self::EMAIL_TYPE_OFF_SITE_JOB_APPLY => 'member_off_site_job_apply',
    //     self::EMAIL_TYPE_JOB_AUDIT_END      => 'member_job_audit_end',
    //
    // ];

    const TITLE_LIST = [
        self::EMAIL_TYPE_REGISTER             => '用户注册',
        self::EMAIL_TYPE_CHANGE_PASSWORD      => '用户修改密码',
        self::EMAIL_TYPE_CHANGE_EMAIL         => '用户修改邮箱',
        self::EMAIL_TYPE_BIND_EMAIL           => '用户绑定邮箱',
        self::EMAIL_TYPE_OFF_SITE_JOB_APPLY   => '简历反馈',
        self::EMAIL_TYPE_JOB_AUDIT_REJECT     => '单位认证-审核拒绝',
        self::EMAIL_TYPE_JOB_AUDIT_PASS       => '单位认证-审核通过',
        self::EMAIL_TYPE_RESUME_CHECK         => '简历被查看',
        self::EMAIL_TYPE_RESUME_FIRST_PASS    => '通过初筛',
        self::EMAIL_TYPE_RESUME_INTERVIEW     => '面试邀请',
        self::EMAIL_TYPE_RESUME_BACK          => '简历不合适',
        self::EMAIL_TYPE_JOB_UPDATE           => '职位更新',
        self::EMAIL_COMPANY_INFO_AUDIT_PASS   => '单位认证-审核通过',
        self::EMAIL_COMPANY_INFO_AUDIT_REJECT => '单位认证-审核驳回',
        self::EMAIL_RESUME_INVITE_JOB_APPLY   => '职位投递邀请',
        self::EMAIL_JOB_SUBSCRIBE             => '职位订阅',
    ];

    const SUBJECT_LIST = [
        self::EMAIL_TYPE_REGISTER             => '【高校人才网】用户注册-验证邮件',
        self::EMAIL_TYPE_CHANGE_PASSWORD      => '【高校人才网】用户修改密码-验证邮件',
        self::EMAIL_TYPE_CHANGE_EMAIL         => '【高校人才网】用户修改邮箱-验证邮件',
        self::EMAIL_TYPE_BIND_EMAIL           => '【高校人才网】用户绑定邮箱-验证邮件',
        self::EMAIL_TYPE_OFF_SITE_JOB_APPLY   => '【高校人才网】简历反馈',
        self::EMAIL_TYPE_JOB_AUDIT_REJECT     => '【高校人才网】单位认证-审核通过',
        self::EMAIL_TYPE_JOB_AUDIT_PASS       => '【高校人才网】单位认证-审核驳回',
        self::EMAIL_TYPE_RESUME_CHECK         => '【高校人才网】投递反馈-简历被查看',
        self::EMAIL_TYPE_RESUME_FIRST_PASS    => '【高校人才网】投递反馈-通过初筛',
        self::EMAIL_TYPE_RESUME_INTERVIEW     => '【高校人才网】投递反馈-面试邀请',
        self::EMAIL_TYPE_RESUME_BACK          => '【高校人才网】投递反馈-暂不合适',
        self::EMAIL_TYPE_JOB_UPDATE           => '【高校人才网】关注单位-职位更新',
        self::EMAIL_TYPE_JOB_APPLY            => '【高校人才网】简历反馈',
        self::EMAIL_COMPANY_INFO_AUDIT_PASS   => '【高校人才网】单位认证-审核通过',
        self::EMAIL_COMPANY_INFO_AUDIT_REJECT => '【高校人才网】单位认证-审核驳回',
        self::EMAIL_RESUME_INVITE_JOB_APPLY   => '【高校人才网】职位投递邀请',
        self::EMAIL_PLATFORM_DELIVERY         => '【高校人才网】简历反馈',
        self::EMAIL_POST_DELIVERY             => '【高校人才网】简历反馈',
        self::EMAIL_NOT_WILL_DELIVERY         => '【高校人才网】简历反馈',
        self::EMAIL_SEEKER_DELIVERY           => '【高校人才网】简历投递成功通知',
        self::EMAIL_JOB_SUBSCRIBE             => '【高校人才网】向您发送了您订阅的职位信息',
    ];

    private $email;
    private $key;
    private $userType;
    private $emailType;
    private $content;
    private $pageData;
    private $attachmentFile;
    private $stuffFileArr = [];
    private $emailListCC  = [];
    private $onlineResume = [];
    private $subject      = '';
    private $checkedData;
    private $replyEmail;

    // 姓名+岗位+用人部门+工作城市+学历
    private $titleData = [];

    /**
     * @var MessageInterface message instance
     */
    protected $compose;

    const REPLY_EMAIL = '<EMAIL>';
    const REPLY_PHONE = '020-85611139';
    const BOY         = 'https://img.gaoxiaojob.com/uploads/static/image/resume/male.png';
    const GIRL        = 'https://img.gaoxiaojob.com/uploads/static/image/resume/female.png';

    const COOPERATIVE_DELIVER_NOTICE   = '此邮件为系统代发，请勿直接回复。如需联系人才，请通过高校人才网单位管理系统或下载简历获取人才联系方式。';
    const UNCOOPERATIVE_DELIVER_NOTICE = '此邮件为系统代发，请勿直接回复。如需联系求职者，请使用“全部回复”或通过下方联系方式进行联系。';

    public function __construct($email, $userType, $emailType, $content = '', $pageData = [])
    {
        $this->email     = $email;
        $this->userType  = $userType;
        $this->content   = $content;
        $this->emailType = $emailType;
        $this->pageData  = $pageData;
    }

    // 这个方法是用于设置邮件被读取了以后,往系统里面发信息的
    public function setCheckedData($data)
    {
        $this->checkedData = $data;
    }

    /**
     * 验证码部分才需要
     * @return string
     */
    private function setKey()
    {
        switch ($this->userType) {
            case BaseMember::TYPE_PERSON:
                $userKey = 'PERSON';
                break;
            case BaseMember::TYPE_COMPANY:
                $userKey = 'COMPANY';
                break;
            default:
                $userKey = 'OTHER';
                break;
        }

        $this->key = self::CACHE_KEY_LIST[$this->emailType] . ':' . $userKey . ':' . $this->email;

        return $this->key;
    }

    public function add()
    {
        $this->setKey();

        switch ($this->userType) {
            case self::EMAIL_TYPE_REGISTER:
            case self::EMAIL_TYPE_CHANGE_PASSWORD:
            case self::EMAIL_TYPE_CHANGE_EMAIL:
            case self::EMAIL_TYPE_BIND_EMAIL:
            case self::EMAIL_TYPE_OFF_SITE_JOB_APPLY:
            case self::EMAIL_TYPE_JOB_AUDIT_REJECT:
            case self::EMAIL_TYPE_JOB_AUDIT_PASS:
            case self::EMAIL_TYPE_RESUME_CHECK:
            case self::EMAIL_TYPE_RESUME_FIRST_PASS:
            case self::EMAIL_TYPE_RESUME_INTERVIEW:
            case self::EMAIL_TYPE_RESUME_BACK:
            case self::EMAIL_TYPE_JOB_UPDATE:
            case self::EMAIL_TYPE_JOB_APPLY:
            case self::EMAIL_COMPANY_INFO_AUDIT_PASS:
            case self::EMAIL_COMPANY_INFO_AUDIT_REJECT:
            case self::EMAIL_RESUME_INVITE_JOB_APPLY:
            case self::EMAIL_PLATFORM_DELIVERY:
            case self::EMAIL_POST_DELIVERY:
            case self::EMAIL_NOT_WILL_DELIVERY:
            case self::EMAIL_SEEKER_DELIVERY:
                break;
            default:
                throw new Exception('不存在的类型');
        }

        // 前面都是一些检查和组合,下面这里就是实际的写入一个数据
        $model          = new BaseEmailLog();
        $model->content = $this->content ?: '';
        $model->email   = $this->email;
        $model->type    = $this->emailType;
        $model->status  = BaseEmailLog::STATUS_WAIT;
        if (!$model->save()) {
            throw new Exception($model->getFirstErrorsMessage());
        }

        return $model->id;
    }

    //    private function setContent()
    //    {
    //        switch ($this->type) {
    //            case self::EMAIL_TYPE_REGISTER:
    //            case self::EMAIL_TYPE_CHANGE_PASSWORD:
    //            case self::EMAIL_TYPE_CHANGE_EMAIL:
    //            case self::EMAIL_TYPE_BIND_EMAIL:
    //                $this->content = $this->code;
    //            case self::EMAIL_TYPE_OFF_SITE_JOB_APPLY:
    //                $this->content = $this->content; //待处理
    //        }
    //    }

    /**
     * 验证邮箱验证码
     * @param $code
     * @return bool
     * @throws Exception
     */
    public function validation($code)
    {
        $key       = $this->setKey();
        $cacheCode = Cache::get($key);

        if (!$cacheCode) {
            throw new Exception('验证码不存在');
        }

        if ($cacheCode != $code) {
            throw new Exception('验证码错误');
        }

        return true;
    }

    /**
     * 发送激活邮件
     * @param $subject
     * @param $content
     * @throws Exception
     */
    public function send()
    {
        //初始化，设置模板信息

        // 鉴于很多人在这里发错测试的职位,先做一个限制,只有正式环境才继续往下走
        if (Yii::$app->params['environment'] != 'prod') {
            // 可以在非正式环境发送邮件的邮箱
            $smsTestEmail = Yii::$app->params['smsTestEmail'];
            // 对比两个邮箱是否有包含
            $emailArray = explode(',', $this->email);
            $sendFlag   = false;
            foreach ($emailArray as $emailItem) {
                if (in_array($emailItem, $smsTestEmail)) {
                    $sendFlag = true;
                    break;
                }
            }

            if (!$sendFlag) {
                return true;
            }
        }

        // 把内存开到512m
        ini_set('memory_limit', '512M');

        $this->setCompose();
        $view    = $this->getView();
        $subject = $this->getSubject();

        $this->compose->setHtmlBody($view);
        //设置邮件主题
        $this->compose->setSubject($subject);
        $email = explode(',', $this->email);
        $this->compose->setTo($email);

        if (!empty($this->onlineResume)) {
            $this->compose->attachContent(file_get_contents($this->onlineResume['path']), ['fileName' => $this->onlineResume['name']]);
            unlink($this->onlineResume['path']);
        }

        //判断是否需要发送附件
        if (!empty($this->attachmentFile)) {
            //            $this->compose->attachContent(file_get_contents($this->attachmentFile['path']), ['fileName' => $this->attachmentFile['name']]);
            $this->compose->attachContent(BaseResumeAttachment::getContent($this->attachmentFile['id']), ['fileName' => $this->attachmentFile['name']]);
        }

        //判断是否要添加应聘材料
        if (!empty($this->stuffFileArr)) {
            foreach ($this->stuffFileArr as $k => $stuffFile) {
                $this->compose->attachContent($this->getContentByFileId($stuffFile['id']), ['fileName' => $stuffFile['name']]);
            }
        }

        // 判断抄送
        if ($this->emailListCC) {
            $this->compose->setCc($this->emailListCC);
        }

        // 默认回复
        if ($this->replyEmail) {
            $this->compose->setReplyTo($this->replyEmail);
        }

        if ($this->compose->send()) {
            //设置缓存
            $this->setCode();
        } else {
            throw new Exception('邮件发送失败');
        }
    }

    /**
     * @throws \Exception
     */
    public function getView(): string
    {
        $title      = self::TITLE_LIST[$this->emailType];
        $content    = json_decode($this->content, true);
        $template   = '@console/mail/common.php';
        $urlMessage = '前往平台查看详情';
        $baseUrl    = Yii::$app->params['pcHost'];
        $setUrl     = '';
        $sign       = '——高校人才网';
        $visitUrl   = 'http://' . str_replace('http://', '', $baseUrl);
        $checkedUrl = $visitUrl . '/e.gif';
        // 拼接url
        if ($this->checkedData) {
            $checkedUrl .= '?';
            foreach ($this->checkedData as $key => $value) {
                $checkedUrl .= $key . '=' . $value . '&';
            }
        }

        switch ($this->emailType) {
            case self::EMAIL_TYPE_REGISTER:
                $html = '<pre class="content">验证码为：<span>' . $content['code'] . '</span>，5分钟内有效，您正在注册高校人才网账号，切勿将验证码告知他人，如非本人操作请忽略。感谢您的关注和信赖，祝您求职顺利！</pre>';

                break;
            case self::EMAIL_TYPE_CHANGE_PASSWORD:
                $html = '<pre class="content">验证码为：<span>' . $content['code'] . '</span>，5分钟内有效，用于找回密码操作，如非本人操作，请注意账号安全。感谢您的关注和信赖，祝您求职顺利！</pre>';
                break;
            case self::EMAIL_TYPE_CHANGE_EMAIL:
                $html = '<pre class="content">验证码为：<span>' . $content['code'] . '</span>，5分钟内有效，用于更改邮箱，如非本人操作，请注意账号安全。感谢您的关注和信赖，祝您求职顺利！</pre>';
                break;
            case self::EMAIL_TYPE_BIND_EMAIL:
                $html = '<pre class="content">验证码为：<span>' . $content['code'] . '</span>，5分钟内有效，用于绑定邮箱，如非本人操作，请注意账号安全。感谢您的关注和信赖，祝您求职顺利！</pre>';
                break;
            case self::EMAIL_TYPE_OFF_SITE_JOB_APPLY:
                if ($content['offSiteJobApplyId']) {
                    // 站外投递
                    $offSiteJobApply = OffSiteJobApply::find()
                        ->where(['id' => $content['offSiteJobApplyId']])
                        ->select('resume_attachment_id,stuff_file_id,job_id,member_id,resume_id')
                        ->asArray()
                        ->one();

                    if ($offSiteJobApply['resume_attachment_id']) {
                        $resumeAttachment = BaseResumeAttachment::find()
                            ->select([
                                'id',
                                'file_url',
                                'file_name',
                                'resume_id',
                            ])
                            ->where(['id' => $offSiteJobApply['resume_attachment_id']])
                            ->asArray()
                            ->one();
                        //$realPath         = BaseResumeAttachment::getRealPath($resumeAttachment['file_url']);
                        //if (file_exists($realPath)) {
                        if ((new Oss())->isResumeAttachmentExist($resumeAttachment['file_url'])) {
                            $this->attachmentFile = [
                                'id'   => $resumeAttachment['id'],
                                'path' => $resumeAttachment['file_url'],
                                'name' => $resumeAttachment['file_name'],
                            ];
                        }
                        if ($offSiteJobApply['stuff_file_id']) {
                            // 设置好附件简历
                            $this->stuffFileArr = FileHelper::getListNameListByIds($offSiteJobApply['stuff_file_id']);
                        }
                    }

                    $memberEmail   = BaseMember::findOneVal(['id' => $offSiteJobApply['member_id']], 'email');
                    $name          = BaseResume::findOneVal(['member_id' => $offSiteJobApply['member_id']], 'name');
                    $educationName = BaseResume::getEducation($offSiteJobApply['resume_id']);

                    $resumeDown       = new ResumeHandle();
                    $onlineResumePath = $resumeDown->setOperator(0, 0)
                        ->setData($offSiteJobApply['resume_id'])
                        ->send();

                    $this->onlineResume = [
                        'path' => $onlineResumePath,
                        'name' => '在线简历_' . str_replace([
                                '/',
                                '.',
                            ], '_', $name) . '.pdf',
                    ];

                    if ($memberEmail) {
                        $this->emailListCC[] = $memberEmail;
                    }

                    $info     = BaseJob::find()
                        ->alias('a')
                        ->select('a.name as jobName,b.full_name as companyName,a.department,a.city_id')
                        ->innerJoin(['b' => BaseCompany::tableName()], 'a.company_id=b.id')
                        ->where(['a.id' => $offSiteJobApply['job_id']])
                        ->asArray()
                        ->one();
                    $cityName = BaseArea::findOneVal(['id' => $info['city_id']], 'name');

                    $this->titleData = [
                        $name,
                        $info['jobName'],
                        $info['department'],
                        $cityName,
                        $educationName,
                    ];
                    // 附件简历比较容易找,但是附件在七牛云?
                }
                // 找到
                $html       = '<pre class="content">尊敬的' . $info['companyName'] . '，您好！<p>人才“' . $name . '”向您“' . $info['jobName'] . '”的岗位投递了一份求职简历，请您及时处理。</p><p>（*本求职附件未经高校人才网审核，请谨慎判断附件内容及真实性）</p></pre>';
                $urlMessage = '前往平台申请合作';
                $setUrl     = 'member/company/applyCooperation';
                break;
            case self::EMAIL_TYPE_RESUME_CHECK:
                break;
            case self::EMAIL_TYPE_RESUME_FIRST_PASS:
                $html = '<pre class="content">' . $content['name'] . '，您好！您投递的“' . $content['companyName'] . '-' . $content['jobName'] . '”职位的简历已经通过初筛，请留意后续通知。高校人才网祝您求职顺利！</pre>';
                break;
            case self::EMAIL_TYPE_RESUME_INTERVIEW:
                $html     = '<pre class="content">' . $content['name'] . '，你好
   您投递的<span>“' . $content['jobName'] . '”</span>职位的招聘单位向您发布面试邀请，请查看具体试邀请详情。高校人才网祝您面试顺利
   <span>注：</span>如有特殊情况可直接与联系人沟通
                </pre>
                <div class="info">
                    <div class="title">面试信息</div>
                    <p>面试职位：' . $content['jobName'] . '</p>
                    <p>面试时间：' . $content['time'] . '</p>
                    <p>面试地址：' . $content['address'] . '</p>
                    <p>联 系 人：' . $content['contact'] . '</p>
                    <p>联系电话：' . $content['telephone'] . '</p>
                </div>';
                $template = '@console/mail/interview.php';
                break;
            case self::EMAIL_TYPE_RESUME_BACK:
                // $html = '<pre class="content">' . $content['name'] . '，您好！很抱歉！您投递至“' . $content['companyName'] . '-' . $content['jobName'] . '”职位的简历不符合招聘单位的要求。 高校人才网根据您的求职意向为您精选了以下职位，欢迎您查看！</pre>';
                $template = '@console/mail/deliverFeedback.php';
                $html     = $this->pageData['html'];
                break;
            case self::EMAIL_TYPE_JOB_UPDATE:
                break;
            case self::EMAIL_TYPE_JOB_APPLY:
                //                $resumeAttachment = BaseResumeAttachment::find()
                //                    ->select([
                //                        'file_url',
                //                        'file_name',
                //                        'resume_id',
                //                    ])
                //                    ->where(['id' => $content['resumeAttachmentId']])
                //                    ->asArray()
                //                    ->one();
                //                if ($resumeAttachment) {
                //                    $realPath = BaseResumeAttachment::getRealPath($resumeAttachment['file_url']);
                //                    if (file_exists($realPath)) {
                //                        $this->attachmentFile = [
                //                            'path' => $realPath,
                //                            'name' => $resumeAttachment['file_name'],
                //                        ];
                //                    }
                //                }
                //                if ($content['stuffFileId']) {
                //                    // 设置好附件简历
                //                    $this->stuffFileArr = FileHelper::getListNameListByIds($content['stuffFileId']);
                //                }
                //
                //                // 这里有个bug,就是没有附件简历的时候,其实是需要直接在线的
                //                $resumeId = $resumeAttachment['resume_id'] ?: $content['resumeId'];
                //
                //                $resumeDown       = new ResumeHandle();
                //                $onlineResumePath = $resumeDown->setOperator(0, 0)
                //                    ->setData($resumeId)
                //                    ->send();
                //                $name             = $resumeDown->resumeModel->name;
                //
                //                $this->onlineResume = [
                //                    'path' => $onlineResumePath,
                //                    'name' => '在线简历_' . $name . '.pdf',
                //                ];

                $html = '<pre class="content">' . $content['name'] . '，您好！您发布的职位“' . $content['jobName'] . '”收到了一份简历投递，请您尽快对简历进行处理</pre>';

                // 姓名+岗位+用人部门+工作城市+学历
                // 'resumeId'      => $resumeId,
                //                 'name'          => $companyName,
                //                 'resumeName'    => $companyName,
                //                 'jobName'       => $jobApplyModel->job_name,
                //                 'department'    => $jobInfo->department,
                //                 'cityName'      => $cityName,
                //                 'educationName' => $educationName,
                //                 'jobApplyId'    => $jobApplyModel->id,

                $this->titleData = [
                    $content['resumeName'],
                    $content['jobName'],
                    $content['department'],
                    $content['cityName'],
                    $content['educationName'],
                ];

                $setUrl = 'member/company/resume/detail/' . $content['jobApplyId'];
                break;
            case self::EMAIL_TYPE_JOB_AUDIT_REJECT:
                $html = '<pre class="content">您发布的职位<span>“<?=$aa?>”</span>平台方审核不通过，请尽快登录管理后台查看驳回原因，修改重新提交。
<span class="danger">驳回原因：</span>职位名称错误</pre>';

                break;
            case self::EMAIL_TYPE_JOB_AUDIT_PASS:
                break;
            case self::EMAIL_COMPANY_INFO_AUDIT_PASS:
                $html = '<pre class="content">您好！您的单位资质认证平台方已审核通过，欢迎您前往登录单位后台解锁更多功能。感谢贵单位对高校人才网的信任，祝您使用愉快！</pre>';
                break;
            case self::EMAIL_COMPANY_INFO_AUDIT_REJECT:
                $html = '<pre class="content">您好！您的单位资质认证平台方审核不通过，请登录管理后台查看详情，按要求重新提交。感谢贵单位对高校人才网的信任，祝您使用愉快！</pre>';
                break;
            case self::EMAIL_RESUME_INVITE_JOB_APPLY:
                /**
                 * 标题：职位投递邀请
                 */ $jobId = $content['jobId'];
                $resumeId  = $content['resumeId'];
                $remark    = $content['remark'];

                $job       = BaseJob::getMailJob($jobId);
                $tags      = '';
                $hoverTags = '';
                if ($job['welfareTagArr']) {
                    foreach ($job['welfareTagArr'] as $item) {
                        $tags = $tags . ' <span class="tag-primary">' . $item . '</span>';
                    }
                }
                if ($job['isWelfare'] == 1) {
                    $hoverTags = '<div class="tag-collect">
                        <span class="tag-primary">···</span>
                        <div class="hover-area">';
                    foreach ($job['welfareTagAll'] as $item) {
                        $hoverTags = $hoverTags . ' <span class="tag-primary">' . $item . '</span>';
                    }
                    $hoverTags = $hoverTags . '</div>
                    </div>';
                }

                $remarkLog = '';
                if ($remark) {
                    $remarkLog = '<div>备注：' . $remark . '</div>';
                }

                $memberName = BaseResume::findOneVal(['id' => $resumeId], 'name');
                $html       = '<div class="container">
                        <div class="email-body">
                    <div class="hi">尊敬的' . $memberName . '，您好：</div>
                    <div class="email-content">我是<a href="' . $job['companyLink'] . '"
                            target="_blank">' . $job['companyName'] . '</a>' . $job['department'] . '
                        招聘负责人，诚邀您投递我单位 “<a href="' . $job['jobLink'] . '" target="_blank">' . $job['jobName'] . '</a>“
                        岗位。如果您对此岗位感兴趣，欢迎登录高校人才网查阅详情并投递。若您的应聘材料经过初步筛选，我们会进一步与您接洽。我们期待与您深入交流，共同探索合作的可能！如您身边有适合此岗位的其他人才，也欢迎推荐。谢谢！</div>
                        ' . $remarkLog . '
                    <div class="email-footer">' . $job['companyName'] . '</div>
                </div>';

                $html = $html . '<div class="job-invite-content" href="" target="_blank">
                    <a class="job-detail" href="' . $job['jobLink'] . '" target="_blank">
                        <div class="title">职位邀请</div>
                        <div class="top">
                            <div class="job-name">' . $job['jobName'] . '</div>
                            <div class="job-date">' . $job['refreshDate'] . '发布</div>
                        </div>
                        <div class="job-info">
                            <div class="job-require">' . $job['education'] . '丨' . $job['areaName'] . ' 丨' . $job['amount'] . '人</div>
                            <div class="job-welfare">
                                ' . $tags . $hoverTags . '
                            </div>
                        </div>
                       ';

                if ($job['requirement']) {
                    $html = $html . ' <div class="work-require">
                            <div class="require">任职要求</div>
                            <pre>' . $job['requirement'] . '
                            </pre>
                        </div>
                        <div class="more">查看更多</div>';
                }

                $html = $html . ' </a>
                        <a class="accept-btn" href="' . $job['jobLink'] . '?from=email" target="_blank">接受邀请</a>
                    </div>
                </div>';

                $template = '@console/mail/jobInvite.php';
                break;
            case self::EMAIL_PLATFORM_DELIVERY:
                /**
                 * 投递反馈模版A(合作单位)
                 */

                $template      = '@console/mail/deliver_notice_a.php';
                $recommendList = BaseResumeLibrary::getEmailRecommend($content);
                $memberId      = BaseResume::findOneVal(['id' => $content['resumeId']], 'member_id');
                $memberEmail   = BaseMember::findOneVal(['id' => $memberId], 'email');
                $userInfo      = BaseResume::getUserInfo($memberId);
                $updateInfo    = BaseResume::getUpdateInfo($userInfo['resumeId']);
                $jobModel      = BaseJob::findOne(['id' => $content['jobId']]);
                $recommendCard = '';
                $userGender    = $userInfo['gender'] == 1 ? self::BOY : self::GIRL;
                $moreLink      = $visitUrl . '/member/company/talent/search';
                $department    = $jobModel['department'] ? '丨' . $jobModel['department'] : '';
                $hideSystemUrl = $visitUrl . UrlHelper::createJobDetailPath($jobModel->id);
                //$headNotice    = self::COOPERATIVE_DELIVER_NOTICE;

                if ($memberEmail) {
                    $this->replyEmail = $memberEmail;
                }

                foreach ($recommendList as $item) {
                    $gender        = $item['gender'] == 1 ? self::BOY : self::GIRL;
                    $newLink       = $visitUrl . '/member/company/talent/detail/' . $item['id'];
                    $recommendCard = $recommendCard . '<a target="_blank"
                    href="' . $newLink . '" class="recommend-card">
                                                <div class="avatar">
                                                    <img src="' . $item["avatar"] . '"/>
                                                </div>
                                                <div class="detail">
                                                    <div class="top">
                                                        <b>' . $item['name'] . '</b>
                                                        <img class="gender" src="' . $gender . '"/>
                                                        <span class="age">' . $item['age'] . '岁</span>
                                                        <div class="tag">
                                                            ' . $item['tag'] . '
                                                        </div>
                                                    </div>
                                                    <div class="education">
                                                                    ' . $item['school'] . ' ' . $item['majorTxt'] . ' ' . $item['educationName'] . '
                                                                </div>';
                    if ($item['jobCategory']) {
                        $recommendCard = $recommendCard . '<div class="want">意向职位：' . $item['jobCategory'] . '</div>';
                    }
                    $recommendCard = $recommendCard . '</div></a>';
                }

                $tag = '';
                foreach ($updateInfo['tag'] as $v) {
                    $tag = $tag . '<span class="tag-orange">' . $v . '</span>';
                }

                //$positionLink = $visitUrl . '/member/company/resume/apply/job?jobId=' . $content['jobId'] . '&from=email';
                $positionLink = $visitUrl . UrlHelper::createJobDetailPath($content['jobId']);
                $detailLink   = $visitUrl . '/member/company/resume/detail/' . $content['jobApplyId'];
                $loginLink    = $visitUrl . '/member/company/resume/apply/list';
                $contactLink  = $visitUrl . '/member/company/resume/detail/' . $content['jobApplyId'] . '?from=email';

                $html = '<div class="email-body">
                             <div class="hi">Hi，尊敬的招聘方' . '：</div>
                                  <div class="email-content">
                                       <span class="name">' . $userInfo["name"] . '</span>
                                       投递了您的职位<a target="_blank"
                                       href="' . $positionLink . '" class="position">' . $jobModel['name'] . $department . '</a>，快去看看吧~
                                       </div>
                            </div>';
                $html = $html . '<div class="date">
                                <span class="recently">最近登录时间：' . $userInfo['last_login_time'] . '</span>
                                <a href="' . $loginLink . '" target="_blank"  class="login">登录高校人才网查看更多简历</a>
                            </div>';
                $html = $html . '<div class="userinfo">
                                        <div> 
                                            <img src="' . $userInfo["avatar"] . '"
                                        class="userphoto"/>
                                     </div>
                                        <div class="info">
                                            <div class="user-basic">
                                                <span class="name">' . $userInfo["name"] . '</span>
                                                <img class="gender" src="' . $userGender . '" alt="">
                                                <div class="tag-content">' . $tag . '</div>
                                            </div>
                                            <div class="education">
                                                ' . $userInfo["educationName"] . '丨' . $userInfo["schoolName"] . '丨' . $userInfo["majorTxt"] . '丨' . $userInfo["age"] . '岁 | 联系邮箱：' . $memberEmail . '
                                            </div>
                                            <div class="operate">
                                                <a href="' . $contactLink . '" class="chat" target="_blank"><span>在线沟通</span></a>
                                                <a href="' . $detailLink . '" class="more" target="_blank">查看更多简历信息</a>
                                            </div>
                                        </div>
                                    </div>';

                if ($recommendList) {
                    $html = $html . '<div class="recommend-content">
                                    <div class="content">
                                        <div class="recommend-title">
                                            <b class="big">人才推荐</b>
                                            <span class="recommend-tips">高校人才网根据职位要求为您推荐以下人才</span>
                                        </div>
                                        <div class="recommend-content">
                                            ' . $recommendCard . '
                                        </div>
                                        <div class="more"><a target="_blank" href="' . $moreLink . '">查看更多人才</a></div>
                                    </div>
                                </div>';
                }

                $cityTitle = BaseArea::getAreaName($jobModel['city_id']);
                //获取投递信息
                $jobApplyModel = BaseJobApply::findOne($content['jobApplyId']);

                $this->subject = ($jobApplyModel->equity_status == BaseJobApply::EQUITY_STATUS_EFFECT ? '【高意向】' : '') . $userInfo['name'] . '｜' . $userInfo['schoolName'] . '_' . $userInfo['educationName'] . '_' . $userInfo['majorTxt'] . ',应聘 ' . $jobModel['name'] . '_' . $cityTitle;
                break;
            case self::EMAIL_POST_DELIVERY:
                /**
                 * 投递反馈模版B
                 * 这里内容会比较长
                 */

                $template      = '@console/mail/deliver_notice_b.php';
                $recommendList = BaseResumeLibrary::getEmailRecommend($content);
                $memberId      = BaseResume::findOneVal(['id' => $content['resumeId']], 'member_id');
                $memberEmail   = BaseMember::findOneVal(['id' => $memberId], 'email');
                $info          = BaseResume::getInfo($memberId);
                $userInfo      = $info['userInfo'];
                $updateInfo    = BaseResume::getUpdateInfo($userInfo['resumeId']);
                $jobModel      = BaseJob::findOne(['id' => $content['jobId']]);
                $recommendCard = '';
                $triangle      = 'https://img.gaoxiaojob.com/uploads/static/image/resume/title.png';
                $userGender    = $userInfo['gender'] == 1 ? self::BOY : self::GIRL;
                $moreLink      = $visitUrl . '/member/company/talent/search';
                //$backLink      = $visitUrl . '/member/company/resume/apply/job?jobId=' . $content['jobId'] . '&from=email';
                $backLink      = $visitUrl . UrlHelper::createJobDetailPath($content['jobId']);
                $contactLink   = $visitUrl . '/member/company/resume/detail/' . $content['jobApplyId'] . '?from=email';
                $operateLink1  = $visitUrl . '/member/company/resume/detail/' . $content['jobApplyId'] . '?from=email&operateType=1';
                $operateLink2  = $visitUrl . '/member/company/resume/detail/' . $content['jobApplyId'] . '?from=email&operateType=-1';
                $department    = $jobModel['department'] ? '丨' . $jobModel['department'] : '';
                $advantage     = $userInfo['advantage'] ? '<div class="advantage-content">
                                        <div class="wrapper-title">个人优势
                                            <div class="underline"></div>
                                        </div>
                                        <pre class="text-content">' . $userInfo['advantage'] . '</pre>
                                    </div>' : '';
                $hideSystemUrl = $visitUrl . UrlHelper::createJobDetailPath($jobModel->id);
                //$headNotice    = self::COOPERATIVE_DELIVER_NOTICE;

                if ($memberEmail) {
                    $this->replyEmail = $memberEmail;
                }

                foreach ($recommendList as $item) {
                    $gender        = $item['gender'] == 1 ? self::BOY : self::GIRL;
                    $recommendCard = $recommendCard . '<div class="recommend-card">
                                                <div class="avatar">
                                                    <img src="' . $item["avatar"] . '"/>
                                                </div>
                                                <div class="detail">
                                                    <div class="top">
                                                        <b>' . $item['name'] . '</b>
                                                        <img class="gender" src="' . $gender . '"/>
                                                        <span class="age">' . $item['age'] . '岁</span>
                                                        <div class="tag">
                                                            ' . $item['tag'] . '
                                                        </div>
                                                    </div>
                                                    <div class="education">
                                                                    ' . $item['school'] . ' ' . $item['majorTxt'] . ' ' . $item['educationName'] . '
                                                                </div>';
                    if ($item['jobCategory']) {
                        $recommendCard = $recommendCard . '<div class="want">意向职位：' . $item['jobCategory'] . '</div>';
                    }
                    $recommendCard = $recommendCard . '</div></a>';
                }

                $tag = '';
                foreach ($updateInfo['tag'] as $v) {
                    $tag = $tag . '<span class="tag-orange">' . $v . '</span>&nbsp;';
                }

                $html = '<div class="email-body">
                            <div class="hi">Hi，尊敬的招聘方：</div>
                            <div class="email-content"><b>' . $userInfo["name"] . '</b>投递了您的职位<a href="' . $backLink . '" target="_blank" class="primary">' . $jobModel['name'] . $department . '</a>，快去看看吧~</div>
                         </div>';

                $html = $html . '<div class="row-content">
                                        <div class="date">
                                        <span class="recently">最近登录时间：' . $userInfo['last_login_time'] . '</span>
                                        <a href="' . $visitUrl . '/member/company/resume/apply/list" target="_blank" class="login">登录高校人才网查看更多简历</a>
                                    </div>
                                    
                                    <div class="info-content clear">
                                        <div class="avatar" style="background-image: url(' . $userInfo["avatar"] . ');">
                                        </div>
                    
                                        <div class="info">
                                            <div class="row user-basic">
                                                <span class="name">' . $userInfo["name"] . '</span>
                                                <img class="gender" src="' . $userGender . '" width="20px" alt=""/>
                                                <div class="tag-content">' . $tag . '</div>
                                            </div>
                                            <div class="row clear">
                                                ' . $userInfo["educationName"] . ' | ' . $userInfo["schoolName"] . ' | ' . $userInfo["majorTxt"] . ' | ' . $userInfo["age"] . '岁 | 联系邮箱：' . $memberEmail . '
                                            </div>
                                            <div class="row operate">
                                                <a href="' . $contactLink . '" target="_blank" class="chat"><span>在线沟通</span></a>
                                                <a href="' . $contactLink . '" target="_blank" class="primary">联系人才</a>
                                                <a href="' . $operateLink1 . '" target="_blank">通过初筛</a>
                                                <a href="' . $operateLink2 . '" target="_blank">暂不合适</a>
                                            </div>
                                        </div>
                                    </div>
                                   ' . $advantage . '
                                </div>';

                //求职意向
                $intentionList = $info['intentionList'];
                if ($intentionList) {
                    $logs = '';
                    foreach ($intentionList as $key => $value) {
                        $logs = $logs . '<div class="row has-dot">
                                        <span class="name">' . $value['jobCategoryName'] . '</span>
                                        <span>&nbsp;' . $value['areaName'] . '&nbsp;|</span>
                                        <span>&nbsp;' . $value['wageName'] . '&nbsp;|</span>
                                        <span>&nbsp;' . $value['natureName'] . '</span>
                                    </div>';
                    }

                    $html = $html . '<div class="row-content intention-content spacing-1">
                                    <div class="wrapper-title">求职意向
                                        <div class="underline"></div>
                                    </div>
                    
                                    <div class="row status">
                                        <img src="' . $triangle . '" width="16px" alt="">
                                        求职状态：' . $userInfo['workStatusName'] . '；到岗时间：' . $userInfo['arriveDateTypeName'] . '
                                    </div>
                                    ' . $logs . '
                                </div>';
                }

                //教育经历
                $educationList = $info['educationList'];
                if ($educationList) {
                    $logs = '';
                    foreach ($educationList as $key => $item) {
                        $tagBlue = '';
                        if ($item['isOverseasStudy'] == 1) {
                            $tagBlue = $tagBlue . '<span class="tag-blue ">海外</span>';
                        }
                        if ($item['is_project_school'] == 1) {
                            $tagBlue = $tagBlue . '<span class="tag-orange ">985/211</span>';
                        }
                        $isRecruitment = '';
                        if ($item['isRecruitment'] == 1) {
                            $isRecruitment = '（统招）';
                        }
                        $college = $item['college'] ? ' | 二级院系（机构）：' . $item['college'] : '';

                        $studyEndDate = strtotime($item['studyEndDate']) > 0 ? $item['studyEndDate'] : '至今';
                        $logs         = $logs . '<div class="row">
                                            <div class="jc-between-title">
                                                <div class="title has-dot">
                                                    <span class="name">' . $item['school'] . '</span>
                                                    ' . $tagBlue . '
                                                </div>
                                                <div class="aside">' . $item['studyBeginDate'] . '-' . $studyEndDate . '</div>
                                            </div>
                                            <div class="common-details">
                                                <span>' . $item['educationName'] . $isRecruitment . '| ' . $item['majorName'] . $college . ' </span >
                                            </div >
                                        </div > ';
                    }
                    $html = $html . '<div class="row-content education-content spacing-1" >
                                        <div class="wrapper-title" > 教育经历
                                            <div class="underline" ></div >
                                        </div >
                                        ' . $logs . '
                                    </div > ';
                }

                //往下做视觉隐藏部分
                // $html = $html . '<div id="showDetail">';

                //研究方向
                $researchDirection = $info['researchDirection'];
                if ($researchDirection) {
                    $html = $html . '<div class="row-content">
                                        <div class="wrapper-title">研究方向
                                            <div class="underline"></div>
                                        </div>
                                        <pre class="text-content">' . $researchDirection . '</pre>
                                    </div>';
                }

                //工作经历
                $workList = $info['workList'];
                if ($workList) {
                    $logs = '';
                    foreach ($workList as $key => $item) {
                        $department = strlen($item['department']) > 0 ? '&nbsp;|&nbsp;所在部门：' . $item['department'] : '';
                        $tagBlue    = '';
                        if ($item['isOverseasStudy'] == 1) {
                            $tagBlue = $tagBlue . '<span class="tag-blue ">海外</span>';
                        }
                        if ($item['is_project_school'] == 1) {
                            $tagBlue = $tagBlue . '<span class="tag-blue ">985/211</span>';
                        }
                        $jobEndDate = strtotime($item['jobEndDate']) > 0 ? $item['jobEndDate'] : '至今';
                        $logs       = $logs . '<div class="row">
                                            <div class="jc-between-title">
                                                <div class="title has-dot">
                                                    <span class="name">' . $item['company'] . '</span>
                                                    ' . $tagBlue . '
                                                </div>
                                                <div class="aside">' . $item['jobBeginDate'] . '-' . $jobEndDate . '</div>
                                            </div>
                                            <div class="position">
                                                <span class="name">' . $item['jobName'] . '</span>
                                                ' . $department . '
                                            </div>
                                            <div class="text-description clear">
                                                <div class="label">工作内容：</div>
                                                <pre class="text-content">' . $item['jobContent'] . '</pre>
                                            </div>
                                        </div>';
                    }
                    $html = $html . '<div class="row-content work-content">
                                        <div class="wrapper-title">工作经历
                                            <div class="underline"></div>
                                        </div>
                                        ' . $logs . '
                                    </div>';
                }

                //科研项目
                $projectList = $info['projectList'];
                if ($projectList) {
                    $logs = '';
                    foreach ($projectList as $key => $item) {
                        $tagBlue = '';
                        if ($item['categoryName']) {
                            $tagBlue = $tagBlue . ' <span class="tag-orange">' . $item['categoryName'] . '</span>';
                        }
                        if ($item['isClose'] == 1) {
                            $tagBlue = $tagBlue . '<span class="tag-orange">（已结项）</span>';
                        } else {
                            $tagBlue = $tagBlue . '&nbsp;<span class="tag-orange">（未结项）</span>';
                        }
                        $endDate = strtotime($item['endDate']) > 0 ? $item['endDate'] : '至今';
                        $logs    = $logs . ' <div class="row">
                                            <div class="jc-between-title">
                                                <div class="title has-dot">
                                                    <span class="name">' . $item['name'] . '</span>
                                                    ' . $tagBlue . '
                                                </div>
                                                <div class="aside">' . $item['beginDate'] . '-' . $endDate . '</div>
                                            </div>
                                            <div class="role">
                                                担任角色：' . $item['role'] . ' | 所属单位：' . $item['company'] . '
                                            </div>
                                            <div class="text-description clear">
                                                <div class="label">项目描述：</div>
                                                <pre class="text-content">' . $item['description'] . '</pre>
                                            </div>
                                        </div>';
                    }
                    $html = $html . '<div class="row-content project-content">
                                        <div class="wrapper-title">项目经历
                                            <div class="underline"></div>
                                        </div>
                                       ' . $logs . '
                                    </div>';
                }

                //学术成果
                $pageList   = $info['pageList'];
                $patentList = $info['patentList'];
                $bookList   = $info['bookList'];
                if ($pageList || $patentList || $bookList) {
                    $html = $html . ' <div class="row-content">
                                        <div class="wrapper-title">学术成果
                                            <div class="underline"></div>
                                        </div>';
                    //学术论文
                    if ($pageList) {
                        $logs = '';
                        foreach ($pageList as $k => $item) {
                            $logs = $logs . ' <div class="list">  
                                                <div class="jc-between-title">
                                                    <div class="title has-dot">
                                                            <span class="name">' . $item['title'] . '</span>
                                                    </div>
                                                    <div class="aside">' . $item['publishDate'] . '</div>
                                                </div>
                                                <div class="between-content">
                                                    <div class="list clear">
                                                        <div class="col-1">刊物名/卷（期号）：' . $item['serialNumber'] . '</div>
                                                        <div class="col-2">收录情况：' . $item['recordSituation'] . '</div>
                                                    </div>
                                                    <div class="list clear">
                                                        <div class="col-1">本人位次：' . $item['position'] . '</div>
                                                        <div class="col-2">影响因子：' . $item['impactFactor'] . '</div>
                                                    </div>
                                                </div>
                                                <div class="text-description clear">
                                                    <div class="label">论文描述：</div>
                                                    <pre class="text-content">' . $item['description'] . '</pre>
                                                </div>
                                            </div>';
                        }
                        $html = $html . '<div class="row">
                                            <div class="classify-title">学术论文</div>
                                           ' . $logs . '
                                        </div>';
                    }

                    //学术专利
                    if ($patentList) {
                        $logs = '';
                        foreach ($patentList as $k => $item) {
                            $logs = $logs . ' <div class="list">
                                                <div class="jc-between-title">
                                                    <div class="title has-dot">
                                                            <span class="name">' . $item['name'] . '</span>
                                                    </div>
                                                    <div class="aside">' . $item['authorizationDate'] . '</div>
                                                </div>
                                                <div class="between-content">
                                                    <div class="list clear">
                                                        <div class="col-1">本人位次：' . $item['positionText'] . '</div>
                                                        <div class="col-2">专利编号：' . $item['number'] . '</div>
                                                    </div>
                                                    <div class="list clear">
                                                        <div class="col-1">完成状态：' . $item['finishStatus'] . '</div>
                                                    </div>
                                                </div>
                                                <div class="text-description clear">
                                                    <div class="label">论文描述：</div>
                                                    <pre class="text-content">' . $item['description'] . '</pre>
                                                </div>
                                            </div>';
                        }
                        $html = $html . '<div class="row">
                                            <div class="classify-title">学术专利</div>
                                           ' . $logs . '
                                        </div>';
                    }

                    //学术专著
                    if ($bookList) {
                        $logs = '';
                        foreach ($bookList as $k => $item) {
                            $logs = $logs . '<div class="second-list">
                                                <div class="jc-between-title">
                                                    <div class="title has-dot">
                                                        <span class="name">' . $item['name'] . '</span>
                                                    </div>
                                                    <div class="aside">' . $item['publishDate'] . '</div>
                                                </div>
                                                <div class="common-details">字数（万字）：' . $item['words'] . ' | 发行数量：' . $item['publishAmount'] . '
                                                </div>
                                            </div>';
                        }
                        $html = $html . '<div class="row book-content">
                                            <div class="classify-title">学术专著</div>
                                           ' . $logs . '
                                        </div>';
                    }

                    $html = $html . '</div>';
                }

                //荣誉奖励
                $rewardList      = $info['rewardList'];
                $otherRewardList = $info['otherRewardList'];
                if ($rewardList || $otherRewardList) {
                    $html = $html . '<div class="row-content">
                                        <div class="wrapper-title">荣誉奖励
                                            <div class="underline"></div>
                                        </div>';

                    //学术奖励
                    if ($rewardList) {
                        $logs = '';
                        foreach ($rewardList as $k => $item) {
                            $logs = $logs . '<div class="second-list">
                                                <div class="jc-between-title">
                                                    <div class="title has-dot">
                                                        <span class="name">' . $item['name'] . '</span>
                                                    </div>
                                                    <div class="aside">' . $item['obtainDate'] . '</div>
                                                </div>
                                                <div class="common-details">奖励级别：' . $item['level'] . ' | 获奖角色：' . $item['role'] . '</div>
                                            </div>';
                        }
                        $html = $html . '<div class="row">
                                            <div class="classify-title">学术奖励</div>
                                           ' . $logs . '
                                        </div>';
                    }

                    //其他荣誉
                    if ($otherRewardList) {
                        $logs = '';
                        foreach ($rewardList as $k => $item) {
                            $logs = $logs . '<div class="second-list">
                                                <div class="jc-between-title">
                                                    <div class="title has-dot">
                                                        <span class="name">' . $item['name'] . '</span>
                                                    </div>
                                                    <div class="aside">' . $item['obtainDate'] . '</div>
                                                </div>
                                                <div class="common-details">奖励级别：' . $item['level'] . ' | 获奖角色：' . $item['role'] . '</div>
                                            </div>';
                        }
                        $html = $html . '<div class="row">
                                            <div class="classify-title">其他荣誉</div>
                                            ' . $logs . '
                                         </div>';
                    }

                    $html = $html . '</div>';
                }

                //技能/语言
                $certificateList = $info['certificateList'];
                $skillList       = $info['skillList'];
                $otherSkillList  = $info['otherSkillList'];
                if ($certificateList || $skillList || $otherSkillList) {
                    $html = $html . '<div class="row-content">
                                        <div class="wrapper-title">技能/语言
                                            <div class="underline"></div>
                                        </div>';
                    //资质证书
                    if ($certificateList) {
                        $logs = '';
                        foreach ($certificateList as $k => $item) {
                            $logs = $logs . '<div class="second-list">
                                                <div class="jc-between-title">
                                                    <div class="title has-dot">
                                                        <span class="name">' . $item['certificateName'] . '</span>
                                                    </div>
                                                    <div class="aside">' . $item['obtainDate'] . '</div>
                                                </div>
                                                <div class="common-details">成绩：' . $item['score'] . '</div>
                                            </div>';
                        }
                        $html = $html . '<div class="row">
                                            <div class="classify-title">资质证书</div>
                                            ' . $logs . '
                                        </div>';
                    }

                    //技能/语言
                    if ($skillList) {
                        $logs = '';
                        foreach ($skillList as $k => $item) {
                            $logs = $logs . '<div class="second-list">
                                                <div class="common-details has-dot">
                                                    <span class="bold">' . $item['skillName'] . '</span>
                                                    &nbsp;|&nbsp;
                                                    掌握程度：' . $item['degreeTypeName'] . '
                                                </div>
                                            </div>';
                        }
                        $html = $html . '<div class="row skill-content">
                                            <div class="classify-title">技能/语言</div>
                                            ' . $logs . '
                                        </div>';
                    }

                    //其他技能
                    if ($otherSkillList) {
                        $logs = '';
                        foreach ($otherSkillList as $k => $item) {
                            $logs = $logs . '<div class="second-list">
                                                <div class="common-details has-dot">
                                                    <span class="bold">' . $item['name'] . '</span>
                                                    &nbsp;|&nbsp;
                                                    掌握程度：' . $item['degreeTypeName'] . '
                                                </div>
                                                <div class="text-description clear">
                                                    <div class="label">技能说明:</div>
                                                    <pre class="text-content">' . $item['description'] . '</pre>
                                                </div>
                                            </div>';
                        }
                        $html = $html . '<div class="row other-skill">
                                            <div class="classify-title">其他技能</div>
                                                ' . $logs . '
                                        </div>';
                    }

                    $html = $html . '</div>';
                }

                //附加信息
                $addInfoList = $info['addInfoList'];
                if ($addInfoList) {
                    $logs = '';
                    foreach ($addInfoList as $key => $item) {
                        $themeName = $item['themeName'] ? $item['themeName'] : $item['themeIdName'];
                        $logs      = $logs . '<div class="row">
                                            <div class="title">
                                                    <span class="name has-dot">' . $themeName . '</span>
                                            </div>
                                            <div class="text-description clear">
                                                <div class="label">主题描述：</div>
                                                <pre class="text-content">' . $item['content'] . '</pre>
                                            </div>
                                        </div>';
                    }
                    $html = $html . '<div class="row-content addition-content">
                                        <div class="wrapper-title">附加信息
                                            <div class="underline"></div>
                                        </div>
                                        ' . $logs . '
                                    </div>';
                }

                // $html = $html . '</div>';

                // $html = $html . '<div class="expand" >
                //                 <a class="button" href="#showDetail">展开更多简历详情</a>
                //             </div>';

                //人才推荐
                $recommendList = BaseResumeLibrary::getEmailRecommend($content);
                $recommendCard = '';
                foreach ($recommendList as $item) {
                    $gender        = $item['gender'] == 1 ? self::BOY : self::GIRL;
                    $newLink       = $visitUrl . '/member/company/talent/detail/' . $item['id'];
                    $recommendCard = $recommendCard . '<a target="_blank"
                                    href="' . $newLink . '"class="recommend-card">
                                                            <div class="avatar">
                                                                <img src="' . $item["avatar"] . '"
                                                                     alt=""/>
                                                            </div>
                                                            <div class="detail">
                                                                <div class="top">
                                                                    <b>' . $item["name"] . '</b>
                                                                    <img class="gender" src="' . $gender . '" alt=""/>
                                                                    <span class="age">' . $item['age'] . '岁</span>
                                                                    <div class="tag">
                                                                        ' . $item['tag'] . '
                                                                    </div>
                                                                </div>
                                                                <div class="education">
                                                                    ' . $item['school'] . ' ' . $item['majorTxt'] . ' ' . $item['educationName'] . '
                                                                </div>';
                    if ($item['jobCategory']) {
                        $recommendCard = $recommendCard . '<div class="want">意向职位：' . $item['jobCategory'] . '</div>';
                    }
                    $recommendCard = $recommendCard . '</div></a>';
                }

                if ($recommendList) {
                    $html = $html . '<div class="recommend-content">
                                    <div class="content">
                                        <div class="recommend-title">
                                            <div class="big">
                                                <img src="' . $recommendList[0]['leftTitleImage'] . '" />
                                                人才推荐
                                                <img src="' . $recommendList[0]['rightTitleImage'] . '" />
                                            </div>
                                            <a class="recommend-tips">高校人才网根据职位要求为您推荐以下人才</a>
                                        </div>
                                        <div class="recommend-content">
                                            ' . $recommendCard . '
                                        </div>
                                        <div class="more"><a href="' . $moreLink . '"  target="_blank">查看更多人才</a></div>
                                    </div>
                                </div>';
                }

                //附件以及材料
                $resumeAttachment = BaseResumeAttachment::find()
                    ->select([
                        'id',
                        'file_url',
                        'file_name',
                        'resume_id',
                    ])
                    ->where(['id' => $content['resumeAttachmentId']])
                    ->asArray()
                    ->one();
                if ($resumeAttachment) {
                    //                    $realPath = BaseResumeAttachment::getRealPath($resumeAttachment['file_url']);
                    //                    if (file_exists($realPath)) {
                    //                        $this->attachmentFile = [
                    //                            'path' => $realPath,
                    //                            'name' => $resumeAttachment['file_name'],
                    //                        ];
                    //                    }
                    if ((new Oss())->isResumeAttachmentExist($resumeAttachment['file_url'])) {
                        $this->attachmentFile = [
                            'id'   => $resumeAttachment['id'],
                            'path' => $resumeAttachment['file_url'],
                            'name' => $resumeAttachment['file_name'],
                        ];
                    }
                }
                if ($content['stuffFileId']) {
                    // 设置好附件简历
                    $this->stuffFileArr = FileHelper::getListNameListByIds($content['stuffFileId']);
                }
                $resumeId = $resumeAttachment['resume_id'] ?: $content['resumeId'];
                if ($resumeId) {
                    $resumeDown       = new ResumeHandle();
                    $onlineResumePath = $resumeDown->setOperator(0, 0)
                        ->setData($resumeId)
                        ->send();
                    $name             = str_replace([
                        '/',
                        '.',
                    ], '_', $resumeDown->resumeModel->name);

                    $this->onlineResume = [
                        'path' => $onlineResumePath,
                        'name' => '在线简历_' . $name . '.pdf',
                    ];
                }
                $cityTitle     = BaseArea::getAreaName($jobModel['city_id']);
                $jobApplyModel = BaseJobApply::findOne($content['jobApplyId']);
                $this->subject = ($jobApplyModel->equity_status == BaseJobApply::EQUITY_STATUS_EFFECT ? '【高意向】' : '') . $userInfo['name'] . '｜' . $userInfo['schoolName'] . '_' . $userInfo['educationName'] . '_' . $userInfo['majorTxt'] . ',应聘 ' . $jobModel['name'] . '_' . $cityTitle;
                break;
            case self::EMAIL_NOT_WILL_DELIVERY:
                /**
                 * 非合作单位邮件投递通用模板
                 */

                $template      = '@console/mail/deliver_notice_a.php';
                $recommendList = BaseResumeLibrary::getEmailRecommend($content);
                $memberId      = BaseResume::findOneVal(['id' => $content['resumeId']], 'member_id');
                $userInfo      = BaseResume::getUserInfo($memberId);
                $updateInfo    = BaseResume::getUpdateInfo($userInfo['resumeId']);
                $jobModel      = BaseJob::findOne(['id' => $content['jobId']]);
                $recommendCard = '';
                $userGender    = $userInfo['gender'] == 1 ? self::BOY : self::GIRL;
                //$backLink      = $visitUrl . '/member/company/resume/apply/job?jobId=' . $content['jobId'];
                $backLink      = $visitUrl . UrlHelper::createJobDetailPath($content['jobId']);
                $detailLink    = $visitUrl . '/member/company/applyCooperation';
                $department    = $jobModel['department'] ? '丨' . $jobModel['department'] : '';
                $memberEmail   = BaseMember::findOneVal(['id' => $memberId], 'email');
                $memberMobile  = BaseMember::findOneVal(['id' => $memberId], 'mobile');
                $hideSystemUrl = $visitUrl . UrlHelper::createJobDetailPath($jobModel->id);
                //$headNotice    = self::UNCOOPERATIVE_DELIVER_NOTICE;
                if ($memberEmail) {
                    $this->replyEmail = $memberEmail;
                }

                foreach ($recommendList as $item) {
                    $gender        = $item['gender'] == 1 ? self::BOY : self::GIRL;
                    $newLink       = $visitUrl . '/member/company/talent/detail/' . $item['id'];
                    $recommendCard = $recommendCard . '<a target="_blank"
                    href="' . $newLink . '" class="recommend-card">
                                                <div class="avatar">
                                                    <img src="' . $item["avatar"] . '"/>
                                                </div>
                                                <div class="detail">
                                                    <div class="top">
                                                        <b>' . $item['name'] . '</b>
                                                        <img class="gender" src="' . $gender . '"/>
                                                        <span class="age">' . $item['age'] . '岁</span>
                                                        <div class="tag">
                                                            ' . $item['tag'] . '
                                                        </div>
                                                    </div>
                                                    <div class="education">
                                                                    ' . $item['school'] . ' ' . $item['majorTxt'] . ' ' . $item['educationName'] . '
                                                                </div>';
                    if ($item['jobCategory']) {
                        $recommendCard = $recommendCard . '<div class="want">意向职位：' . $item['jobCategory'] . '</div>';
                    }
                    $recommendCard = $recommendCard . '</div></a>';
                }

                $tag = '';
                foreach ($updateInfo['tag'] as $v) {
                    $tag = $tag . '<span class="tag-orange">' . $v . '</span>';
                }

                $html = '<div class="email-body">
                             <div>Hi，尊敬的招聘方' . '：</div>
                                  <div class="email-content">
                                       <span class="name">' . $userInfo["name"] . '</span>
                                       投递了您的职位<a target="_blank"
                                       href="' . $backLink . '" class="position">' . $jobModel['name'] . $department . '</a>，快去看看吧~
                                       </div>
                            </div>';
                $html = $html . '<div class="date">
                                <span class="recently">最近登录时间：' . $userInfo['last_login_time'] . '</span>
                                <a class="login" href="' . $detailLink . '">登录高校人才网查看更多简历</a>
                            </div>';
                $html = $html . '<div class="userinfo">
                                        <div> 
                                            <img src="' . $userInfo["avatar"] . '"
                                        class="userphoto"/>
                                     </div>
                                        <div class="info">
                                            <div class="user-basic">
                                                <b class="name">' . $userInfo["name"] . '</b>
                                                <img class="gender" src="' . $userGender . '" alt="">
                                                <div class="tag-content">' . $tag . '</div>
                                            </div>
                                            <div class="education">
                                                ' . $userInfo["educationName"] . '丨' . $userInfo["schoolName"] . '丨' . $userInfo["majorTxt"] . '丨' . $userInfo["age"] . '岁
                                            </div>
                                            <div class="contact">联系电话：' . $memberMobile . '丨 联系邮箱：<span class="email-address">' . $memberEmail . '</span></div>
                                            <div class="operate">
                                                <a href="' . $detailLink . '" class="chat" target="_blank"><span>在线沟通</span></a>
                                                <a href="' . $detailLink . '" class="more" target="_blank">查看更多简历信息</a>
                                            </div>
                                        </div>
                                    </div>';

                if ($recommendList) {
                    $html = $html . '<div class="recommend-content">
                                    <div class="content">
                                        <div class="recommend-title">
                                            <b class="big">人才推荐</b>
                                            <span class="recommend-tips">高校人才网根据职位要求为您推荐以下人才</span>
                                        </div>
                                        <div class="recommend-content">
                                            ' . $recommendCard . '
                                        </div>
                                        <div class="more"><a target="_blank" href="' . $detailLink . '">查看更多人才</a></div>
                                    </div>
                                </div>';
                }

                //附件以及材料
                $resumeAttachment = BaseResumeAttachment::find()
                    ->select([
                        'id',
                        'file_url',
                        'file_name',
                        'resume_id',
                    ])
                    ->where(['id' => $content['resumeAttachmentId']])
                    ->asArray()
                    ->one();
                if ($resumeAttachment) {
                    //                    $realPath = BaseResumeAttachment::getRealPath($resumeAttachment['file_url']);
                    //                    if (file_exists($realPath)) {
                    //                        $this->attachmentFile = [
                    //                            'path' => $realPath,
                    //                            'name' => $resumeAttachment['file_name'],
                    //                        ];
                    //                    }
                    if ((new Oss())->isResumeAttachmentExist($resumeAttachment['file_url'])) {
                        $this->attachmentFile = [
                            'id'   => $resumeAttachment['id'],
                            'path' => $resumeAttachment['file_url'],
                            'name' => $resumeAttachment['file_name'],
                        ];
                    }
                }
                if ($content['stuffFileId']) {
                    // 设置好附件简历
                    $this->stuffFileArr = FileHelper::getListNameListByIds($content['stuffFileId']);
                }
                $resumeId = $resumeAttachment['resume_id'] ?: $content['resumeId'];
                if ($resumeId) {
                    $resumeDown       = new ResumeHandle();
                    $onlineResumePath = $resumeDown->setOperator(0, 0)
                        ->setData($resumeId)
                        ->send();
                    $name             = str_replace([
                        '/',
                        '.',
                    ], '_', $resumeDown->resumeModel->name);

                    $this->onlineResume = [
                        'path' => $onlineResumePath,
                        'name' => '在线简历_' . $name . '.pdf',
                    ];
                }

                if ($memberEmail) {
                    $this->emailListCC[] = $memberEmail;
                }

                $cityTitle     = BaseArea::getAreaName($jobModel['city_id']);
                $this->subject = $userInfo['name'] . '｜' . $userInfo['schoolName'] . '_' . $userInfo['educationName'] . '_' . $userInfo['majorTxt'] . ',应聘 ' . $jobModel['name'] . '_' . $cityTitle;
                break;
            case self::EMAIL_SEEKER_DELIVERY:
                /**
                 * 求职者投递“邮件投递”的职位,投递通知模版
                 */

                $template     = '@console/mail/deliverSucceeded.php';
                $memberId     = BaseResume::findOneVal(['id' => $content['resumeId']], 'member_id');
                $userInfo     = BaseResume::getUserInfo($memberId);
                $jobModel     = BaseJob::findOne(['id' => $content['jobId']]);
                $companyName  = BaseCompany::findOneVal(['id' => $jobModel['company_id']], 'full_name');
                $applyAddress = $jobModel['apply_address'] ? '<p>联系邮箱：' . $jobModel['apply_address'] . '</p>' : '';
                $jobLink      = $visitUrl . '/job/detail/' . $content['jobId'] . '.html';
                $department   = $jobModel['department'] ? '丨' . $jobModel['department'] : '';
                $companyLink  = $visitUrl . '/company/detail/' . $jobModel['company_id'] . '.html';
                $html         = '<div class="content">
                            <div class="email-text">Hi，尊敬的' . $userInfo['name'] . '：
                                <p>您的简历已成功投递至
                                <a href="' . $companyLink . '" target="_blank">' . $companyName . '</a>&nbsp;|&nbsp;<a href="' . $jobLink . '" target="_blank">' . $jobModel['name'] . '</a>用人部门处！请耐心等待用人部门回复！也可联系职位招聘人员跟进后续投递进度。</p>
                                <p> 联系人：' . $companyName . $department . '</p>
                                ' . $applyAddress . '
                            </div>
                        </div>';
                break;
            case self::EMAIL_JOB_SUBSCRIBE:
                $template = '@console/mail/deliverFeedback.php';
                //获取用户信息
                $resumeId   = $content['resumeId'];
                $resumeName = BaseResume::findOneVal(['id' => $resumeId], 'name');
                $memberId   = BaseResume::findOneVal(['id' => $resumeId], 'member_id');
                //获取订阅信息
                //拼接跳转连接
                $visitUrl   = 'http://' . str_replace('http://', '', Yii::$app->params['pcHost']);
                $resumeLink = $visitUrl . '/member/person/resume';

                //获取与求职者匹配的12条职位
                $service = JobSubscribeApplication::getInstance();
                $jobList = $service->getLogJobList($content['id']);

                //获取未完成模块
                $unCompleteText = BaseResumeComplete::getUnCompleteText($memberId);
                //邮件主体页面内容
                $html = '<div class="container">';
                $tips = '';
                if ($unCompleteText) {
                    $tips = '<div class="resume-tips">
                            <span class="warning">*</span>
                            温馨提示：您的简历中
                            <span class="wait-perfect">' . $unCompleteText . '</span>
                            未填写完整，详尽的简历内容更容易提高竞争力、受到用人部门青睐哦。
                            <a href="' . $resumeLink . '" target="_blank">去完善简历</a>
                        </div>';
                }

                $html = $html . '<div class="email-body">
                                <div class="hi">' . $resumeName . '，您好：</div>
                                <div class="email-content">高校人才网根据您的职位订阅条件，为您精挑细选一批优质职位，推荐您投递！这些岗位目前正在急聘中，现在正是投递好时机~</div>
                                ' . $tips . '
                            </div>';
                if ($jobList) {
                    $tagContent = '';
                    $visitUrl   = 'http://' . str_replace('http://', '', Yii::$app->params['pcHost']);
                    $moreLink   = $visitUrl . '/job';
                    foreach ($jobList as $item) {
                        $tagHtml = '<span class="tag" >' . $item['education'] . '</span>';
                        if ($item['majorName']) {
                            $tagHtml .= '<span class="tag" >' . $item['majorName'] . '</span>';
                        }
                        $tagHtml    .= '<span class="tag" >' . $item['amount'] . '人</span>';
                        $tagContent .= '<a target="_blank" href="' . $item['url'] . '" class="recommend-card">
                                        <div class="job-top">
                                            <div class="job-name">' . $item['jobName'] . '</div>
                                            <div class="release-date" >' . $item['refreshDate'] . '发布</div>
                                        </div>
                                        <div class="tag-content">' . $tagHtml . '</div>
                                        <div class="job-bottom">
                                            <div class="organizational">' . $item['companyName'] . '</div>
                                            <div class="address" style="display:' . $item['cityShow'] . '"><img src="' . $item['address'] . '"/>' . $item['city'] . '</div>
                                        </div>
                                    </a>';
                    }

                    $html = $html . '<div class="recommend-wrapper">
                    <div class="content">
                        <div class="recommend-title">
                            <b class="big">我的订阅</b>
                            <span class="recommend-tips">高校人才网根据您的订阅条件，为您精选以下职位</span>
                        </div>
                        <div class="recommend-content">
                           ' . $tagContent . '
                        </div>
                        <div class="more"><a target="_blank" href="' . $moreLink . '">查看更多精选职位</a></div>
                    </div>
                </div>';
                }
                $html = $html . '</div>';
                break;
        }

        $realUrl = $visitUrl . '/' . $setUrl;

        $view = Yii::$app->view->renderFile($template, [
            'html'          => $html,
            //            'logo'       => $this->compose->embed(Yii::getAlias('@frontendPc') . '/web/static/assets/email/images/logo.png'),
            'logo'          => 'https://img.gaoxiaojob.com/uploads/static/image/logo/logo_column.png?imageView2/0/w/122/h/33',
            'notes'         => $this->compose->embed(Yii::getAlias('@frontendPc') . '/web/static/assets/email/images/notes.png'),
            // 'bottom'     => $this->compose->embed(Yii::getAlias('@frontendPc') . '/web/static/assets/email/images/showcase.png'),
            'replyEmail'    => self::REPLY_EMAIL,
            'replyPhone'    => self::REPLY_PHONE,
            'sign'          => $sign,
            'triangle'      => 'https://img.gaoxiaojob.com/uploads/static/image/resume/title.png',
            'bottom'        => 'https://img.gaoxiaojob.com/uploads/static/image/resume/showcase.png',
            'notification'  => 'https://img.gaoxiaojob.com/uploads/static/image/resume/notification.png',
            'title'         => $title,
            'urlMessage'    => $urlMessage,
            'url'           => $realUrl,
            // 邮件头部的提示信息
            'headNotice'    => $headNotice ?? "",
            // 这个url的作用主要是给我们自己的运营人员方便跳转的,隐藏在某些字符上面
            'hideSystemUrl' => $hideSystemUrl,
            'checkedUrl'    => $checkedUrl,
        ]);

        return $view;
    }

    public function getSubject(): string
    {
        $subject = '';
        switch ($this->emailType) {
            case self::EMAIL_TYPE_REGISTER:
            case self::EMAIL_TYPE_CHANGE_PASSWORD:
            case self::EMAIL_TYPE_CHANGE_EMAIL:
            case self::EMAIL_TYPE_BIND_EMAIL:
            case self::EMAIL_TYPE_OFF_SITE_JOB_APPLY:
            case self::EMAIL_TYPE_JOB_AUDIT_REJECT:
            case self::EMAIL_TYPE_JOB_AUDIT_PASS:
            case self::EMAIL_TYPE_RESUME_CHECK:
            case self::EMAIL_TYPE_RESUME_FIRST_PASS:
            case self::EMAIL_TYPE_RESUME_INTERVIEW:
            case self::EMAIL_TYPE_RESUME_BACK:
            case self::EMAIL_TYPE_JOB_UPDATE:
            case self::EMAIL_TYPE_JOB_APPLY:
            case self::EMAIL_COMPANY_INFO_AUDIT_PASS:
            case self::EMAIL_COMPANY_INFO_AUDIT_REJECT:
            case self::EMAIL_SEEKER_DELIVERY:
            case self::EMAIL_JOB_SUBSCRIBE:
                if ($this->titleData[0]) {
                    // 去掉空的数组内容先
                    $this->titleData = array_filter($this->titleData);
                    $subject         = implode('_', $this->titleData);
                } else {
                    $subject = self::SUBJECT_LIST[$this->emailType];
                }
                break;
            case self::EMAIL_RESUME_INVITE_JOB_APPLY:
                $content = json_decode($this->content, true);
                $subject = "【投递邀请】" . $content['companyName'] . "-邀您投递" . $content['jobName'];
                break;
            case self::EMAIL_PLATFORM_DELIVERY:
            case self::EMAIL_POST_DELIVERY:
            case self::EMAIL_NOT_WILL_DELIVERY:
                $subject = $this->subject;
                break;
        }

        return $subject;
    }

    public function setCode()
    {
        switch ($this->emailType) {
            case self::EMAIL_TYPE_REGISTER:
            case self::EMAIL_TYPE_CHANGE_PASSWORD:
            case self::EMAIL_TYPE_CHANGE_EMAIL:
            case self::EMAIL_TYPE_BIND_EMAIL:
                $this->setKey();
                try {
                    $content = json_decode($this->content, true);
                    Cache::set($this->key, $content['code'], 60 * 5);
                } catch (Exception $e) {
                    throw new Exception($e->getMessage());
                }

                break;
        }
    }

    public function setCompose()
    {
        try {
            // 只有正式环境才有可能是
            if (Yii::$app->params['environment'] == 'prod') {
                switch ($this->emailType) {
                    // 验证码部分
                    case self::EMAIL_TYPE_REGISTER;
                    case self::EMAIL_TYPE_CHANGE_PASSWORD;
                    case self::EMAIL_TYPE_CHANGE_EMAIL;
                    case self::EMAIL_TYPE_BIND_EMAIL;
                        $this->compose = Yii::$app->mailer2->compose();
                        break;
                    // 投递成功部分
                    case self::EMAIL_SEEKER_DELIVERY;
                        $this->compose = Yii::$app->mailer3->compose();
                        break;
                    case self::EMAIL_JOB_SUBSCRIBE;
                        $this->compose = Yii::$app->mailer4->compose();
                        break;
                    case self::EMAIL_RESUME_INVITE_JOB_APPLY;
                        $this->compose = Yii::$app->mailer5->compose();
                        break;
                    default:
                        $this->compose = Yii::$app->mailer->compose();
                        break;
                }
            } else {
                $this->compose = Yii::$app->mailer->compose();
            }
        } catch (Exception $e) {
            $this->compose = Yii::$app->mailer->compose();
        }
    }

    public function getContentByFileId($id)
    {
        return BaseFile::getContent($id);
    }
}