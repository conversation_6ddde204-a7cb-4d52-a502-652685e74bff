<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "job_temp".
 *
 * @property int $id 主键id
 * @property string $add_time 创建时间
 * @property int $member_id 会员id
 * @property int $company_id 企业id
 * @property string $name 职位名称
 * @property string $period_date 职位有效期
 * @property string $code 职位代码
 * @property int $job_category_id 职位类别id
 * @property int $education_type 学历要求类型
 * @property string $major_id 专业id
 * @property int $nature_type 性质类型
 * @property int $is_negotiable 是否面议：0否，1是
 * @property int $wage_type 薪资类型(1月，2年，3日）
 * @property int $min_wage 薪资最低
 * @property int $max_wage 薪资最高
 * @property int $experience_type 经验要求类型
 * @property string $age_type 年龄要求类型
 * @property int $min_age 年龄要求最低
 * @property int $max_age 年龄要求最高
 * @property int $title_type 职称类型
 * @property int $political_type 政治面貌类型
 * @property int $abroad_type 海外经历类型
 * @property string $amount 招聘人数
 * @property string $department 用户部门
 * @property int $district_id 工作地点id
 * @property int $province_id 省Id
 * @property int $city_id 市Id
 * @property string $address 工作地点详细地址
 * @property string $welfare_tag 福利标签
 * @property string $duty 岗位职责
 * @property string $requirement 任职要求
 * @property string $remark 其他说明
 * @property int $audit_status 审核状态
 * @property int $offline_type 下线方式 0:无；1:自动下线；2：手动下线
 * @property int $announcement_id 公告id
 * @property int $create_type 创建类型，1:修改；2:新增
 * @property int $gender_type 性别要求类型 0:不限；1:男；2:女
 * @property int $is_temp 判别临时数据字段
 * @property string $apply_type 应聘方式(1电子邮件xxxx
 * @property string $apply_address 投递地址
 * @property int $job_id
 * @property string $delivery_limit_type 投递限制 1学历 2应聘材料
 * @property int $delivery_type 投递类型1=站外投递,2=站内投递
 * @property int $delivery_way 投递方式 1平台投递 2邮箱投递 3网址投递
 * @property string $extra_notify_address 投递通知地址
 * @property string $establishment_type 职位编制类型 对应字典表类型31
 * @property int $is_establishment 是否有编制 1有编制 2无编制
 * @property int $contact_id 联系人ID
 * @property string $contact_synergy_id 协同账号ID
 */
class JobTemp extends \common\base\BaseActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'job_temp';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['add_time', 'period_date'], 'safe'],
            [['member_id', 'company_id', 'job_category_id', 'education_type', 'nature_type', 'is_negotiable', 'wage_type', 'min_wage', 'max_wage', 'experience_type', 'min_age', 'max_age', 'title_type', 'political_type', 'abroad_type', 'district_id', 'province_id', 'city_id', 'audit_status', 'offline_type', 'announcement_id', 'create_type', 'gender_type', 'is_temp', 'job_id', 'delivery_type', 'delivery_way', 'is_establishment', 'contact_id'], 'integer'],
            [['period_date'], 'required'],
            [['name'], 'string', 'max' => 256],
            [['code', 'department', 'apply_type'], 'string', 'max' => 32],
            [['major_id', 'welfare_tag'], 'string', 'max' => 512],
            [['age_type', 'amount', 'address'], 'string', 'max' => 128],
            [['duty', 'requirement', 'remark'], 'string', 'max' => 2048],
            [['apply_address'], 'string', 'max' => 600],
            [['delivery_limit_type', 'establishment_type', 'contact_synergy_id'], 'string', 'max' => 60],
            [['extra_notify_address'], 'string', 'max' => 255],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'add_time' => 'Add Time',
            'member_id' => 'Member ID',
            'company_id' => 'Company ID',
            'name' => 'Name',
            'period_date' => 'Period Date',
            'code' => 'Code',
            'job_category_id' => 'Job Category ID',
            'education_type' => 'Education Type',
            'major_id' => 'Major ID',
            'nature_type' => 'Nature Type',
            'is_negotiable' => 'Is Negotiable',
            'wage_type' => 'Wage Type',
            'min_wage' => 'Min Wage',
            'max_wage' => 'Max Wage',
            'experience_type' => 'Experience Type',
            'age_type' => 'Age Type',
            'min_age' => 'Min Age',
            'max_age' => 'Max Age',
            'title_type' => 'Title Type',
            'political_type' => 'Political Type',
            'abroad_type' => 'Abroad Type',
            'amount' => 'Amount',
            'department' => 'Department',
            'district_id' => 'District ID',
            'province_id' => 'Province ID',
            'city_id' => 'City ID',
            'address' => 'Address',
            'welfare_tag' => 'Welfare Tag',
            'duty' => 'Duty',
            'requirement' => 'Requirement',
            'remark' => 'Remark',
            'audit_status' => 'Audit Status',
            'offline_type' => 'Offline Type',
            'announcement_id' => 'Announcement ID',
            'create_type' => 'Create Type',
            'gender_type' => 'Gender Type',
            'is_temp' => 'Is Temp',
            'apply_type' => 'Apply Type',
            'apply_address' => 'Apply Address',
            'job_id' => 'Job ID',
            'delivery_limit_type' => 'Delivery Limit Type',
            'delivery_type' => 'Delivery Type',
            'delivery_way' => 'Delivery Way',
            'extra_notify_address' => 'Extra Notify Address',
            'establishment_type' => 'Establishment Type',
            'is_establishment' => 'Is Establishment',
            'contact_id' => 'Contact ID',
            'contact_synergy_id' => 'Contact Synergy ID',
        ];
    }
}
