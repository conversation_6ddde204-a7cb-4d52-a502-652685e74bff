<?php

namespace common\helpers;

use common\base\models\BaseArea;
use itbdw\Ip\IpLocation;
use Yii;

class IpHelper extends \yii\helpers\IpHelper
{

    const DATA_FILE = '/data/qqwry.dat';

    /**
     * 获取登录的ip地址(数字)
     * @return string
     * @throws \yii\base\NotSupportedException
     */
    public static function getIpInt()
    {
        try {
            $ip = Yii::$app->request->userIP;

            return ip2long($ip) ?: 0;
        } catch (\Exception $e) {
            return 0;
        }
    }

    public static function getIp()
    {
        try {
            $ip = Yii::$app->request->userIP;
            if ($ip == '::1') {
                $ip = 0;
            }

            return $ip;
        } catch (\Exception $e) {
            return '';
        }
    }

    public static function getArea($ip = '')
    {
        if (!$ip) {
            return '';
        }
        $ip   = $ip ?: self::getIp();
        $path = Yii::getAlias('@common') . self::DATA_FILE;

        if (!file_exists($path)) {
            return '';
        }

        $data = IpLocation::getLocation($ip, $path);

        return $data;
    }

    public static function getCity($ip = '')
    {
        return self::getArea($ip)['city'];
    }

    public static function getFullAreaByIntIp($ip)
    {
        if (!$ip) {
            return '';
        }

        $ip   = long2ip($ip);
        $path = Yii::getAlias('@common') . self::DATA_FILE;

        if (!file_exists($path)) {
            return '';
        }

        $data = IpLocation::getLocation($ip, $path);

        //    [country] => 中国
        //     [province] => 北京
        //     [city] =>
        //     [county] =>
        $areaArray = [
            $data['country'],
            $data['province'],
            $data['city'],
            $data['county'],
        ];

        // 合并去重
        $areaArray = array_unique(array_filter($areaArray));

        $area = implode(',', $areaArray);

        return $area;
    }

    public static function getFullAreaByIp($ip)
    {
        if (!$ip) {
            return '';
        }

        $path = Yii::getAlias('@common') . self::DATA_FILE;

        if (!file_exists($path)) {
            return '';
        }

        $data = IpLocation::getLocation($ip, $path);

        //    [country] => 中国
        //     [province] => 北京
        //     [city] =>
        //     [county] =>
        $areaArray = [
            $data['country'],
            $data['province'],
            $data['city'],
            $data['county'],
        ];

        // 合并去重
        $areaArray = array_unique(array_filter($areaArray));

        return implode(',', $areaArray);
    }

    public static function getAreaInfo()
    {
        $ip   = self::getIp();
        $path = Yii::getAlias('@common') . self::DATA_FILE;

        if (!file_exists($path)) {
            return '';
        }

        $data = IpLocation::getLocation($ip, $path);

        //    [country] => 中国
        //     [province] => 北京
        //     [city] =>
        //     [county] =>
        $areaArray = [
            $data['country'],
            $data['province'],
            $data['city'],
            $data['county'],
        ];

        // 从城市开始匹配
        if ($data['city']) {
            // 找到城市对应的id
            // 把市去掉文案去掉
            $data['city'] = str_replace('市', '', $data['city']);

            $cityId = BaseArea::findOneVal([
                'name' => $data['city'],
            ], 'id');

            $areaArray['cityId'] = $cityId;
        }

        return $areaArray;
    }

    public static function matchAreaId($areaName, $level = 2)
    {
        $areaId = BaseArea::findOneVal([
            'name'  => $areaName,
            'level' => $level,
        ], 'id');

        return $areaId;
    }

    // 根据ip去获取地区id，包含省和市
    public static function getAreaIds($ip = '')
    {
        $area = self::getArea($ip);

        $areaArray = [];

        if ($area['city']) {
            // 找到城市对应的id
            // 把市去掉文案去掉
            $area['city'] = str_replace('市', '', $area['city']);

            $cityId = BaseArea::findOneVal([
                'name' => $area['city'],
            ], 'id');

            $areaArray['cityId'] = $cityId;
        }

        if ($area['province']) {
            // 找到省对应的id
            $provinceId = BaseArea::findOneVal([
                'name' => $area['province'],
            ], 'id');

            $areaArray['provinceId'] = $provinceId;
            if (in_array($provinceId, BaseArea::HIGH_CROWN_ID_LIST)) {
                $areaArray['cityId'] = BaseArea::HIGH_CROWN_CITY_ID[$provinceId];
            }
        }

        $return = ArrayHelper::merge($area, $areaArray);

        return $return;
    }

    public static function isChina($ip = '')
    {
        $area = self::getArea($ip);

        return $area['country'] === '中国';
    }

}
